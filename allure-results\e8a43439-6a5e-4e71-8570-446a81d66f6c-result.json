{"uuid": "e8a43439-6a5e-4e71-8570-446a81d66f6c", "name": "Legal Council: Create New Resolution from Approved Resolution", "historyId": "a658ea48501d3e434f6a37494fdc28b6:5b3555343836716531a392c635640134", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-alternative-workflows.spec.ts"}, {"name": "titlePath", "value": " > firefox-en > tests\\resolution-alternative-workflows.spec.ts > Resolution Alternative Workflows > Alternative 2: New Resolution from Approved/NotApproved"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "firefox-en"}, {"name": "suite", "value": "tests\\resolution-alternative-workflows.spec.ts"}, {"name": "subSuite", "value": "Resolution Alternative Workflows > Alternative 2: New Resolution from Approved/NotApproved"}], "links": [], "start": 1751856347497, "testCaseId": "a658ea48501d3e434f6a37494fdc28b6", "fullName": "tests/resolution-alternative-workflows.spec.ts:177:9", "titlePath": ["tests", "resolution-alternative-workflows.spec.ts", "Resolution Alternative Workflows", "Alternative 2: New Resolution from Approved/NotApproved"], "stop": 1751856347497}