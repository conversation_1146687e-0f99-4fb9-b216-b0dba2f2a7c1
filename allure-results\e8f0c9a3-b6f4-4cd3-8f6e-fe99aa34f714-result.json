{"uuid": "e8f0c9a3-b6f4-4cd3-8f6e-fe99aa34f714", "name": "Board Secretary: Fund View-Only Access", "historyId": "1b69a281fb5f21a5143dc2c34dee77f1:f0494070a4792d94c64308aac1604abb", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Safari"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > Mobile Safari > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Board Secretary Role Access Control"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "Mobile Safari"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Board Secretary Role Access Control"}], "links": [], "start": 1751856347707, "testCaseId": "1b69a281fb5f21a5143dc2c34dee77f1", "fullName": "tests/authentication-and-rbac.spec.ts:309:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Board Secretary Role Access Control"], "stop": 1751856347707}