{"uuid": "e9156a95-1063-4d1b-8066-02ac22aa4ed6", "name": "Legal Council: Full CRUD Access", "historyId": "48857b7e4823e9cc5c4455d1787c1300:96797789f4669ba5e9de53cebde9126b", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.board-member-management.spec.ts"}, {"name": "titlePath", "value": " > Mobile Chrome > tests\\board-member-management.spec.ts > Board Member Management > Role-Based Access Control"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "Mobile Chrome"}, {"name": "suite", "value": "tests\\board-member-management.spec.ts"}, {"name": "subSuite", "value": "Board Member Management > Role-Based Access Control"}], "links": [], "start": 1751856347590, "testCaseId": "48857b7e4823e9cc5c4455d1787c1300", "fullName": "tests/board-member-management.spec.ts:325:9", "titlePath": ["tests", "board-member-management.spec.ts", "Board Member Management", "Role-Based Access Control"], "stop": 1751856347591}