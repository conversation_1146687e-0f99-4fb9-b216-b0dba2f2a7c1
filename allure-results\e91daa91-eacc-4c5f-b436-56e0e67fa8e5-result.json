{"uuid": "e91daa91-eacc-4c5f-b436-56e0e67fa8e5", "name": "Unauthorized API Access Prevention", "historyId": "354af82c3f282832cbd42b3f318edaad:73db7be642bfc9fe252d7c3eec44a098", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > chromium-ar > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Security Features"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "chromium-ar"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Security Features"}], "links": [], "start": 1751856346864, "testCaseId": "354af82c3f282832cbd42b3f318edaad", "fullName": "tests/authentication-and-rbac.spec.ts:463:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Security Features"], "stop": 1751856346864}