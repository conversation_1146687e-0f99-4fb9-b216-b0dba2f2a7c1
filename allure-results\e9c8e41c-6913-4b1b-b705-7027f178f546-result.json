{"uuid": "e9c8e41c-6913-4b1b-b705-7027f178f546", "name": "Display No Members Message When Fund Has No Members", "historyId": "227c7b7915627ce5349fe0c3980d2dd9:f0494070a4792d94c64308aac1604abb", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Safari"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.board-member-management.spec.ts"}, {"name": "titlePath", "value": " > Mobile Safari > tests\\board-member-management.spec.ts > Board Member Management > View Board Members"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "Mobile Safari"}, {"name": "suite", "value": "tests\\board-member-management.spec.ts"}, {"name": "subSuite", "value": "Board Member Management > View Board Members"}], "links": [], "start": 1751856347736, "testCaseId": "227c7b7915627ce5349fe0c3980d2dd9", "fullName": "tests/board-member-management.spec.ts:308:9", "titlePath": ["tests", "board-member-management.spec.ts", "Board Member Management", "View Board Members"], "stop": 1751856347736}