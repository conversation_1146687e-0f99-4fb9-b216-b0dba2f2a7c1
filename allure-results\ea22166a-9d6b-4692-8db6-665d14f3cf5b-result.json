{"uuid": "ea22166a-9d6b-4692-8db6-665d14f3cf5b", "name": "Verify Resolution Code Generation", "historyId": "8b972b4b3cfd04a92783077048bef5be:f6a8753cfef08377165401815dd911d6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-lifecycle.spec.ts"}, {"name": "titlePath", "value": " > firefox-ar > tests\\resolution-lifecycle.spec.ts > Resolution Lifecycle Management > Business Rule Validation"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "firefox-ar"}, {"name": "suite", "value": "tests\\resolution-lifecycle.spec.ts"}, {"name": "subSuite", "value": "Resolution Lifecycle Management > Business Rule Validation"}], "links": [], "start": 1751856347093, "testCaseId": "8b972b4b3cfd04a92783077048bef5be", "fullName": "tests/resolution-lifecycle.spec.ts:321:9", "titlePath": ["tests", "resolution-lifecycle.spec.ts", "Resolution Lifecycle Management", "Business Rule Validation"], "stop": 1751856347093}