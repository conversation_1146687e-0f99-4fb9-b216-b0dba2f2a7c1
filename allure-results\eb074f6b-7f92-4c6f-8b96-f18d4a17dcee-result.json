{"uuid": "eb074f6b-7f92-4c6f-8b96-f18d4a17dcee", "name": "Legal Council: Complete Resolution Data", "historyId": "c00fc75e9f625139abb4e02f58cd1dad:94ad96afc690c6d345a3d6e3ad9d1bce", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-lifecycle.spec.ts"}, {"name": "titlePath", "value": " > chromium-en > tests\\resolution-lifecycle.spec.ts > Resolution Lifecycle Management > Standard Resolution Workflow (Alternative 3)"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "chromium-en"}, {"name": "suite", "value": "tests\\resolution-lifecycle.spec.ts"}, {"name": "subSuite", "value": "Resolution Lifecycle Management > Standard Resolution Workflow (Alternative 3)"}], "links": [], "start": 1751869837496, "testCaseId": "c00fc75e9f625139abb4e02f58cd1dad", "fullName": "tests/resolution-lifecycle.spec.ts:103:9", "titlePath": ["tests", "resolution-lifecycle.spec.ts", "Resolution Lifecycle Management", "Standard Resolution Workflow (Alternative 3)"], "stop": 1751869837496}