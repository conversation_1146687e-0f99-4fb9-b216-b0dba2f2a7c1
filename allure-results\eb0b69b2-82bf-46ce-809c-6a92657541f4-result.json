{"uuid": "eb0b69b2-82bf-46ce-809c-6a92657541f4", "name": "Legal Council: Create New Resolution from Approved Resolution", "historyId": "a658ea48501d3e434f6a37494fdc28b6:370d56a8895ca6917b09138f18b00251", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "webkit-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-alternative-workflows.spec.ts"}, {"name": "titlePath", "value": " > webkit-ar > tests\\resolution-alternative-workflows.spec.ts > Resolution Alternative Workflows > Alternative 2: New Resolution from Approved/NotApproved"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "webkit-ar"}, {"name": "suite", "value": "tests\\resolution-alternative-workflows.spec.ts"}, {"name": "subSuite", "value": "Resolution Alternative Workflows > Alternative 2: New Resolution from Approved/NotApproved"}], "links": [], "start": 1751869837344, "testCaseId": "a658ea48501d3e434f6a37494fdc28b6", "fullName": "tests/resolution-alternative-workflows.spec.ts:177:9", "titlePath": ["tests", "resolution-alternative-workflows.spec.ts", "Resolution Alternative Workflows", "Alternative 2: New Resolution from Approved/NotApproved"], "stop": 1751869837344}