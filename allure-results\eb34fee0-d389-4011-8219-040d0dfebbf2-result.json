{"uuid": "eb34fee0-d389-4011-8219-040d0dfebbf2", "name": "Remember Me Functionality", "historyId": "6597dd4360ce2f550b6885c45a59c16a:5b3555343836716531a392c635640134", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > firefox-en > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Authentication Flow"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "firefox-en"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Authentication Flow"}], "links": [], "start": 1751856347398, "testCaseId": "6597dd4360ce2f550b6885c45a59c16a", "fullName": "tests/authentication-and-rbac.spec.ts:132:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Authentication Flow"], "stop": 1751856347399}