{"uuid": "eb735948-67df-4e06-8154-6ed5d6e8c8ee", "name": "Board Secretary: Full CRUD Access", "historyId": "d9332574ca81e17b036a5289a8ab15c1:5b3555343836716531a392c635640134", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.board-member-management.spec.ts"}, {"name": "titlePath", "value": " > firefox-en > tests\\board-member-management.spec.ts > Board Member Management > Role-Based Access Control"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "firefox-en"}, {"name": "suite", "value": "tests\\board-member-management.spec.ts"}, {"name": "subSuite", "value": "Board Member Management > Role-Based Access Control"}], "links": [], "start": 1751869837548, "testCaseId": "d9332574ca81e17b036a5289a8ab15c1", "fullName": "tests/board-member-management.spec.ts:342:9", "titlePath": ["tests", "board-member-management.spec.ts", "Board Member Management", "Role-Based Access Control"], "stop": 1751869837548}