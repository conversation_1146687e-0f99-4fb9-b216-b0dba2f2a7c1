{"uuid": "eb77d72e-e0d1-4324-838c-35a16d7b8cd5", "name": "Verify Pending Resolution Actions", "historyId": "df88be07a3916c70e6fc575412d45c91:2d9eadd8e5698e677c4123f9b6b8cac6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Microsoft Edge"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-lifecycle.spec.ts"}, {"name": "titlePath", "value": " > Microsoft Edge > tests\\resolution-lifecycle.spec.ts > Resolution Lifecycle Management > Resolution State Validation"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "Microsoft Edge"}, {"name": "suite", "value": "tests\\resolution-lifecycle.spec.ts"}, {"name": "subSuite", "value": "Resolution Lifecycle Management > Resolution State Validation"}], "links": [], "start": 1751869838098, "testCaseId": "df88be07a3916c70e6fc575412d45c91", "fullName": "tests/resolution-lifecycle.spec.ts:246:9", "titlePath": ["tests", "resolution-lifecycle.spec.ts", "Resolution Lifecycle Management", "Resolution State Validation"], "stop": 1751869838098}