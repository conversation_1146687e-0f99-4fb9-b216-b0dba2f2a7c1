{"uuid": "eb91bc6f-dfb3-48ed-8bd0-917152ef2d00", "name": "Verify Arabic Font Rendering and Unicode Support", "historyId": "fc63ff105932a20784c5368cd30a0a40:94ad96afc690c6d345a3d6e3ad9d1bce", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-and-error-handling.spec.ts"}, {"name": "titlePath", "value": " > chromium-en > tests\\localization-and-error-handling.spec.ts > Localization and Error Handling > Arabic Language and RTL Layout"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "chromium-en"}, {"name": "suite", "value": "tests\\localization-and-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Arabic Language and RTL Layout"}], "links": [], "start": 1751856347312, "testCaseId": "fc63ff105932a20784c5368cd30a0a40", "fullName": "tests/localization-and-error-handling.spec.ts:139:9", "titlePath": ["tests", "localization-and-error-handling.spec.ts", "Localization and Error Handling", "Arabic Language and RTL Layout"], "stop": 1751856347312}