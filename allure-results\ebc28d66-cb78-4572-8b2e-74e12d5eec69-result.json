{"uuid": "ebc28d66-cb78-4572-8b2e-74e12d5eec69", "name": "Board Member: View Only Access", "historyId": "53096d6447ca9c9f6f467b3a27cb7c3c:f0494070a4792d94c64308aac1604abb", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Safari"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.board-member-management.spec.ts"}, {"name": "titlePath", "value": " > Mobile Safari > tests\\board-member-management.spec.ts > Board Member Management > Role-Based Access Control"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "Mobile Safari"}, {"name": "suite", "value": "tests\\board-member-management.spec.ts"}, {"name": "subSuite", "value": "Board Member Management > Role-Based Access Control"}], "links": [], "start": 1751856347743, "testCaseId": "53096d6447ca9c9f6f467b3a27cb7c3c", "fullName": "tests/board-member-management.spec.ts:377:9", "titlePath": ["tests", "board-member-management.spec.ts", "Board Member Management", "Role-Based Access Control"], "stop": 1751856347743}