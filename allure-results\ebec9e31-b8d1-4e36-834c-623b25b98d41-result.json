{"uuid": "ebec9e31-b8d1-4e36-834c-623b25b98d41", "name": "Board Secretary: Suspend Voting by Adding Resolution Items", "historyId": "38b53aaf1a17c5b21cdb9663cc86ffab:94ad96afc690c6d345a3d6e3ad9d1bce", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-alternative-workflows.spec.ts"}, {"name": "titlePath", "value": " > chromium-en > tests\\resolution-alternative-workflows.spec.ts > Resolution Alternative Workflows > Alternative 1: Voting Suspension Workflow"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "chromium-en"}, {"name": "suite", "value": "tests\\resolution-alternative-workflows.spec.ts"}, {"name": "subSuite", "value": "Resolution Alternative Workflows > Alternative 1: Voting Suspension Workflow"}], "links": [], "start": 1751869837481, "testCaseId": "38b53aaf1a17c5b21cdb9663cc86ffab", "fullName": "tests/resolution-alternative-workflows.spec.ts:74:9", "titlePath": ["tests", "resolution-alternative-workflows.spec.ts", "Resolution Alternative Workflows", "Alternative 1: Voting Suspension Workflow"], "stop": 1751869837481}