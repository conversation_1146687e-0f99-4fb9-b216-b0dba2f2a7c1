{"uuid": "ec04a006-936e-4402-9c07-ef81f01d7427", "name": "should display MSG001 (Required Field) in English", "historyId": "9ba55942d64383946b481f77f8d4eb0f:370d56a8895ca6917b09138f18b00251", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "webkit-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-error-handling.spec.ts"}, {"name": "titlePath", "value": " > webkit-ar > tests\\localization-error-handling.spec.ts > Localization and Error Handling > System Message Localization (MSG Codes)"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "webkit-ar"}, {"name": "suite", "value": "tests\\localization-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > System Message Localization (MSG Codes)"}], "links": [], "start": 1751869837317, "testCaseId": "9ba55942d64383946b481f77f8d4eb0f", "fullName": "tests/localization-error-handling.spec.ts:211:9", "titlePath": ["tests", "localization-error-handling.spec.ts", "Localization and Error Handling", "System Message Localization (MSG Codes)"], "stop": 1751869837317}