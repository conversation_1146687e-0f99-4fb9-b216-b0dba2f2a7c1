{"uuid": "ec1647fa-7596-4ee6-a94b-5214b2d02f25", "name": "Fund Manager: Dashboard and Navigation Access", "historyId": "8f56bdf47c78bef184f4cb887339ba64:73db7be642bfc9fe252d7c3eec44a098", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > chromium-ar > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Fund Manager Role Access Control"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "chromium-ar"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Fund Manager Role Access Control"}], "links": [], "start": 1751856346842, "testCaseId": "8f56bdf47c78bef184f4cb887339ba64", "fullName": "tests/authentication-and-rbac.spec.ts:157:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Fund Manager Role Access Control"], "stop": 1751856346842}