{"uuid": "ec773ef4-bb95-4f68-b588-d59c87140a29", "name": "Verify Pending Resolution Actions", "historyId": "df88be07a3916c70e6fc575412d45c91:73db7be642bfc9fe252d7c3eec44a098", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-lifecycle.spec.ts"}, {"name": "titlePath", "value": " > chromium-ar > tests\\resolution-lifecycle.spec.ts > Resolution Lifecycle Management > Resolution State Validation"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "chromium-ar"}, {"name": "suite", "value": "tests\\resolution-lifecycle.spec.ts"}, {"name": "subSuite", "value": "Resolution Lifecycle Management > Resolution State Validation"}], "links": [], "start": 1751856346956, "testCaseId": "df88be07a3916c70e6fc575412d45c91", "fullName": "tests/resolution-lifecycle.spec.ts:246:9", "titlePath": ["tests", "resolution-lifecycle.spec.ts", "Resolution Lifecycle Management", "Resolution State Validation"], "stop": 1751856346956}