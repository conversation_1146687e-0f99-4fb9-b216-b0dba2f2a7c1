{"uuid": "ec8ee65a-af69-48e6-a2d5-c9bb4c1d0885", "name": "Unauthorized API Access Prevention", "historyId": "354af82c3f282832cbd42b3f318edaad:94ad96afc690c6d345a3d6e3ad9d1bce", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > chromium-en > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Security Features"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "chromium-en"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Security Features"}], "links": [], "start": 1751869837399, "testCaseId": "354af82c3f282832cbd42b3f318edaad", "fullName": "tests/authentication-and-rbac.spec.ts:463:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Security Features"], "stop": 1751869837399}