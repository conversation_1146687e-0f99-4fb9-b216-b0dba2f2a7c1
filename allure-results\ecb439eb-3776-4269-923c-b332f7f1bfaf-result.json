{"uuid": "ecb439eb-3776-4269-923c-b332f7f1bfaf", "name": "Verify Error Message Positioning and Styling", "historyId": "8b1b437d5d71f61571680be53dabf181:370d56a8895ca6917b09138f18b00251", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "webkit-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-and-error-handling.spec.ts"}, {"name": "titlePath", "value": " > webkit-ar > tests\\localization-and-error-handling.spec.ts > Localization and Error Handling > Error Message Display and UX"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "webkit-ar"}, {"name": "suite", "value": "tests\\localization-and-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Error Message Display and UX"}], "links": [], "start": 1751869837301, "testCaseId": "8b1b437d5d71f61571680be53dabf181", "fullName": "tests/localization-and-error-handling.spec.ts:452:9", "titlePath": ["tests", "localization-and-error-handling.spec.ts", "Localization and Error Handling", "Error Message Display and UX"], "stop": 1751869837301}