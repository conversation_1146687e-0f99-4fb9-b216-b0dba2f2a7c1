{"uuid": "ecd31812-09b4-42f6-9fac-e3cc68dcfdc3", "name": "User Access Limited to Assigned Funds Only", "historyId": "bf1a3e47d151385a70d34cb53157d9aa:370d56a8895ca6917b09138f18b00251", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "webkit-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > webkit-ar > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Fund-Specific Permissions"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "webkit-ar"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Fund-Specific Permissions"}], "links": [], "start": 1751869837262, "testCaseId": "bf1a3e47d151385a70d34cb53157d9aa", "fullName": "tests/authentication-and-rbac.spec.ts:397:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Fund-Specific Permissions"], "stop": 1751869837262}