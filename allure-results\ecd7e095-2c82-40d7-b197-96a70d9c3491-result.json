{"uuid": "ecd7e095-2c82-40d7-b197-96a70d9c3491", "name": "Resolution Approval: All Members Vote Yes", "historyId": "332a9dfb9752ac0cb6b550b44adb1d59:94ad96afc690c6d345a3d6e3ad9d1bce", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-lifecycle.spec.ts"}, {"name": "titlePath", "value": " > chromium-en > tests\\resolution-lifecycle.spec.ts > Resolution Lifecycle Management > Standard Resolution Workflow (Alternative 3)"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "chromium-en"}, {"name": "suite", "value": "tests\\resolution-lifecycle.spec.ts"}, {"name": "subSuite", "value": "Resolution Lifecycle Management > Standard Resolution Workflow (Alternative 3)"}], "links": [], "start": 1751856347368, "testCaseId": "332a9dfb9752ac0cb6b550b44adb1d59", "fullName": "tests/resolution-lifecycle.spec.ts:182:9", "titlePath": ["tests", "resolution-lifecycle.spec.ts", "Resolution Lifecycle Management", "Standard Resolution Workflow (Alternative 3)"], "stop": 1751856347369}