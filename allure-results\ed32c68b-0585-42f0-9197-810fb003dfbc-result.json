{"uuid": "ed32c68b-0585-42f0-9197-810fb003dfbc", "name": "should display MSG001 (Required Field) in English", "historyId": "9ba55942d64383946b481f77f8d4eb0f:96797789f4669ba5e9de53cebde9126b", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-error-handling.spec.ts"}, {"name": "titlePath", "value": " > Mobile Chrome > tests\\localization-error-handling.spec.ts > Localization and Error Handling > System Message Localization (MSG Codes)"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "Mobile Chrome"}, {"name": "suite", "value": "tests\\localization-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > System Message Localization (MSG Codes)"}], "links": [], "start": 1751869837752, "testCaseId": "9ba55942d64383946b481f77f8d4eb0f", "fullName": "tests/localization-error-handling.spec.ts:211:9", "titlePath": ["tests", "localization-error-handling.spec.ts", "Localization and Error Handling", "System Message Localization (MSG Codes)"], "stop": 1751869837752}