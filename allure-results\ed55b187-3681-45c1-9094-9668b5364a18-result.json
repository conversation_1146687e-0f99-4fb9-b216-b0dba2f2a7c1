{"uuid": "ed55b187-3681-45c1-9094-9668b5364a18", "name": "Verify Language Switch Functionality", "historyId": "b8096cda4948a18fea345330732de175:73db7be642bfc9fe252d7c3eec44a098", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-and-error-handling.spec.ts"}, {"name": "titlePath", "value": " > chromium-ar > tests\\localization-and-error-handling.spec.ts > Localization and Error Handling > Language Switching and Persistence"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "chromium-ar"}, {"name": "suite", "value": "tests\\localization-and-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Language Switching and Persistence"}], "links": [], "start": 1751869837018, "testCaseId": "b8096cda4948a18fea345330732de175", "fullName": "tests/localization-and-error-handling.spec.ts:234:9", "titlePath": ["tests", "localization-and-error-handling.spec.ts", "Localization and Error Handling", "Language Switching and Persistence"], "stop": 1751869837018}