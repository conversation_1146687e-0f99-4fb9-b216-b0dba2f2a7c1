{"uuid": "ee288544-abac-4d8a-9911-5d52c8af4711", "name": "Fund Manager: Dashboard and Navigation Access", "historyId": "8f56bdf47c78bef184f4cb887339ba64:96797789f4669ba5e9de53cebde9126b", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > Mobile Chrome > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Fund Manager Role Access Control"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "Mobile Chrome"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Fund Manager Role Access Control"}], "links": [], "start": 1751856347546, "testCaseId": "8f56bdf47c78bef184f4cb887339ba64", "fullName": "tests/authentication-and-rbac.spec.ts:157:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Fund Manager Role Access Control"], "stop": 1751856347546}