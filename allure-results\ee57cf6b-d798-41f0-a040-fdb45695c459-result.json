{"uuid": "ee57cf6b-d798-41f0-a040-fdb45695c459", "name": "Verify Pending Resolution Actions", "historyId": "df88be07a3916c70e6fc575412d45c91:370d56a8895ca6917b09138f18b00251", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "webkit-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-lifecycle.spec.ts"}, {"name": "titlePath", "value": " > webkit-ar > tests\\resolution-lifecycle.spec.ts > Resolution Lifecycle Management > Resolution State Validation"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "webkit-ar"}, {"name": "suite", "value": "tests\\resolution-lifecycle.spec.ts"}, {"name": "subSuite", "value": "Resolution Lifecycle Management > Resolution State Validation"}], "links": [], "start": 1751856347225, "testCaseId": "df88be07a3916c70e6fc575412d45c91", "fullName": "tests/resolution-lifecycle.spec.ts:246:9", "titlePath": ["tests", "resolution-lifecycle.spec.ts", "Resolution Lifecycle Management", "Resolution State Validation"], "stop": 1751856347226}