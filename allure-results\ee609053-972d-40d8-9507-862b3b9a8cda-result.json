{"uuid": "ee609053-972d-40d8-9507-862b3b9a8cda", "name": "Verify English Login Page Layout", "historyId": "91158ceed80d652e854c42cc4e9e5eb0:96797789f4669ba5e9de53cebde9126b", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-and-error-handling.spec.ts"}, {"name": "titlePath", "value": " > Mobile Chrome > tests\\localization-and-error-handling.spec.ts > Localization and Error Handling > English Language and LTR Layout"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "Mobile Chrome"}, {"name": "suite", "value": "tests\\localization-and-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > English Language and LTR Layout"}], "links": [], "start": 1751869837716, "testCaseId": "91158ceed80d652e854c42cc4e9e5eb0", "fullName": "tests/localization-and-error-handling.spec.ts:177:9", "titlePath": ["tests", "localization-and-error-handling.spec.ts", "Localization and Error Handling", "English Language and LTR Layout"], "stop": 1751869837717}