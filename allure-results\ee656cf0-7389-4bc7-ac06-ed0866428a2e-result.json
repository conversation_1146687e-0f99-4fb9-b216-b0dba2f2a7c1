{"uuid": "ee656cf0-7389-4bc7-ac06-ed0866428a2e", "name": "Verify State Consistency After Alternative Workflows", "historyId": "13d259707a803d441a68c075b2d0edf7:94ad96afc690c6d345a3d6e3ad9d1bce", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-alternative-workflows.spec.ts"}, {"name": "titlePath", "value": " > chromium-en > tests\\resolution-alternative-workflows.spec.ts > Resolution Alternative Workflows > Alternative Workflow Business Rules"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "chromium-en"}, {"name": "suite", "value": "tests\\resolution-alternative-workflows.spec.ts"}, {"name": "subSuite", "value": "Resolution Alternative Workflows > Alternative Workflow Business Rules"}], "links": [], "start": 1751869837493, "testCaseId": "13d259707a803d441a68c075b2d0edf7", "fullName": "tests/resolution-alternative-workflows.spec.ts:377:9", "titlePath": ["tests", "resolution-alternative-workflows.spec.ts", "Resolution Alternative Workflows", "Alternative Workflow Business Rules"], "stop": 1751869837493}