{"uuid": "ee93da94-ec88-44f4-a3ff-8c07af4fd592", "name": "Verify Arabic Text Rendering Across Browsers", "historyId": "d5ee680cc12e89e7d709ac18149b3f81:73db7be642bfc9fe252d7c3eec44a098", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-and-error-handling.spec.ts"}, {"name": "titlePath", "value": " > chromium-ar > tests\\localization-and-error-handling.spec.ts > Localization and Error Handling > Cross-Browser Compatibility"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "chromium-ar"}, {"name": "suite", "value": "tests\\localization-and-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Cross-Browser Compatibility"}], "links": [], "start": 1751856346915, "testCaseId": "d5ee680cc12e89e7d709ac18149b3f81", "fullName": "tests/localization-and-error-handling.spec.ts:398:9", "titlePath": ["tests", "localization-and-error-handling.spec.ts", "Localization and Error Handling", "Cross-Browser Compatibility"], "stop": 1751856346915}