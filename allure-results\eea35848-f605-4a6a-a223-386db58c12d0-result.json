{"uuid": "eea35848-f605-4a6a-a223-386db58c12d0", "name": "Board Member: View-Only Fund and Member Access", "historyId": "fc060ecde5cb783469717a03cd7e3549:370d56a8895ca6917b09138f18b00251", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "webkit-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > webkit-ar > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Board Member Role Access Control"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "webkit-ar"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Board Member Role Access Control"}], "links": [], "start": 1751869837260, "testCaseId": "fc060ecde5cb783469717a03cd7e3549", "fullName": "tests/authentication-and-rbac.spec.ts:372:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Board Member Role Access Control"], "stop": 1751869837261}