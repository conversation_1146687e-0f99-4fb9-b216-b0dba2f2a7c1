{"uuid": "eeb05f19-4395-4354-ad9c-4680bfade3d5", "name": "Handle Network Errors During State Transitions", "historyId": "3b411f353bb34b4e67b879ced7ad10f8:5b3555343836716531a392c635640134", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-lifecycle.spec.ts"}, {"name": "titlePath", "value": " > firefox-en > tests\\resolution-lifecycle.spec.ts > Resolution Lifecycle Management > Error Handling and Edge Cases"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "firefox-en"}, {"name": "suite", "value": "tests\\resolution-lifecycle.spec.ts"}, {"name": "subSuite", "value": "Resolution Lifecycle Management > Error Handling and Edge Cases"}], "links": [], "start": 1751869837646, "testCaseId": "3b411f353bb34b4e67b879ced7ad10f8", "fullName": "tests/resolution-lifecycle.spec.ts:370:9", "titlePath": ["tests", "resolution-lifecycle.spec.ts", "Resolution Lifecycle Management", "Error <PERSON> and <PERSON> Cases"], "stop": 1751869837646}