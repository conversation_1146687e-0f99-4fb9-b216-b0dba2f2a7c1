{"uuid": "eece2a83-f729-4daf-98af-9e4cdea68254", "name": "Handle Network Errors During State Transitions", "historyId": "3b411f353bb34b4e67b879ced7ad10f8:f0494070a4792d94c64308aac1604abb", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Safari"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-lifecycle.spec.ts"}, {"name": "titlePath", "value": " > Mobile Safari > tests\\resolution-lifecycle.spec.ts > Resolution Lifecycle Management > Error Handling and Edge Cases"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "Mobile Safari"}, {"name": "suite", "value": "tests\\resolution-lifecycle.spec.ts"}, {"name": "subSuite", "value": "Resolution Lifecycle Management > Error Handling and Edge Cases"}], "links": [], "start": 1751869837952, "testCaseId": "3b411f353bb34b4e67b879ced7ad10f8", "fullName": "tests/resolution-lifecycle.spec.ts:370:9", "titlePath": ["tests", "resolution-lifecycle.spec.ts", "Resolution Lifecycle Management", "Error <PERSON> and <PERSON> Cases"], "stop": 1751869837952}