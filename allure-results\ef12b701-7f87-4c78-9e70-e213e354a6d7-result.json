{"uuid": "ef12b701-7f87-4c78-9e70-e213e354a6d7", "name": "Login Form Validation", "historyId": "865e19413c5bc548ac295043ba600862:5b3555343836716531a392c635640134", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > firefox-en > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Authentication Flow"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "firefox-en"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Authentication Flow"}], "links": [], "start": 1751856347393, "testCaseId": "865e19413c5bc548ac295043ba600862", "fullName": "tests/authentication-and-rbac.spec.ts:78:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Authentication Flow"], "stop": 1751856347393}