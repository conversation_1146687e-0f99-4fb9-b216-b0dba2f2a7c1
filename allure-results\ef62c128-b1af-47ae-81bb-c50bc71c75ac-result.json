{"uuid": "ef62c128-b1af-47ae-81bb-c50bc71c75ac", "name": "should display English dashboard with proper navigation", "historyId": "eb1b7da02e3df43cd8839020f4c61c78:73db7be642bfc9fe252d7c3eec44a098", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-error-handling.spec.ts"}, {"name": "titlePath", "value": " > chromium-ar > tests\\localization-error-handling.spec.ts > Localization and Error Handling > English Language and LTR Layout"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "chromium-ar"}, {"name": "suite", "value": "tests\\localization-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > English Language and LTR Layout"}], "links": [], "start": 1751869837040, "testCaseId": "eb1b7da02e3df43cd8839020f4c61c78", "fullName": "tests/localization-error-handling.spec.ts:118:9", "titlePath": ["tests", "localization-error-handling.spec.ts", "Localization and Error Handling", "English Language and LTR Layout"], "stop": 1751869837040}