{"uuid": "ef63a791-2cda-462f-8d24-6410fa1ae1c1", "name": "Cross-Fund Access Prevention", "historyId": "c9d8a30d0f40c7195e8b733c748cc1d2:96797789f4669ba5e9de53cebde9126b", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > Mobile Chrome > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Fund-Specific Permissions"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "Mobile Chrome"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Fund-Specific Permissions"}], "links": [], "start": 1751869837679, "testCaseId": "c9d8a30d0f40c7195e8b733c748cc1d2", "fullName": "tests/authentication-and-rbac.spec.ts:425:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Fund-Specific Permissions"], "stop": 1751869837680}