{"uuid": "ef72099e-4619-4d29-9469-04f355a2a606", "name": "Legal Council: Full Fund and Resolution Access", "historyId": "9b620bc7f6d829e04e919924dcd0707a:5b3555343836716531a392c635640134", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > firefox-en > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Legal Council Role Access Control"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "firefox-en"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Legal Council Role Access Control"}], "links": [], "start": 1751856347408, "testCaseId": "9b620bc7f6d829e04e919924dcd0707a", "fullName": "tests/authentication-and-rbac.spec.ts:240:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Legal Council Role Access Control"], "stop": 1751856347408}