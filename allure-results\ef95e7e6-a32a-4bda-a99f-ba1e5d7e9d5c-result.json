{"uuid": "ef95e7e6-a32a-4bda-a99f-ba1e5d7e9d5c", "name": "Board Member: Vote on Resolution", "historyId": "fe6968b6982df532e2c74dc24e020cba:370d56a8895ca6917b09138f18b00251", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "webkit-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-lifecycle.spec.ts"}, {"name": "titlePath", "value": " > webkit-ar > tests\\resolution-lifecycle.spec.ts > Resolution Lifecycle Management > Standard Resolution Workflow (Alternative 3)"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "webkit-ar"}, {"name": "suite", "value": "tests\\resolution-lifecycle.spec.ts"}, {"name": "subSuite", "value": "Resolution Lifecycle Management > Standard Resolution Workflow (Alternative 3)"}], "links": [], "start": 1751856347219, "testCaseId": "fe6968b6982df532e2c74dc24e020cba", "fullName": "tests/resolution-lifecycle.spec.ts:162:9", "titlePath": ["tests", "resolution-lifecycle.spec.ts", "Resolution Lifecycle Management", "Standard Resolution Workflow (Alternative 3)"], "stop": 1751856347219}