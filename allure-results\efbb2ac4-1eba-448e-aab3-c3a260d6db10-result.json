{"uuid": "efbb2ac4-1eba-448e-aab3-c3a260d6db10", "name": "should handle form validation in webkit", "historyId": "f124693791cf1fb5b87b1781c43f0e2f:2d9eadd8e5698e677c4123f9b6b8cac6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Microsoft Edge"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-error-handling.spec.ts"}, {"name": "titlePath", "value": " > Microsoft Edge > tests\\localization-error-handling.spec.ts > Localization and Error Handling > Cross-Browser Compatibility"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "Microsoft Edge"}, {"name": "suite", "value": "tests\\localization-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Cross-Browser Compatibility"}], "links": [], "start": 1751869838061, "testCaseId": "f124693791cf1fb5b87b1781c43f0e2f", "fullName": "tests/localization-error-handling.spec.ts:308:11", "titlePath": ["tests", "localization-error-handling.spec.ts", "Localization and Error Handling", "Cross-Browser Compatibility"], "stop": 1751869838061}