{"uuid": "eff850d7-3c2d-4727-82fd-074136240bf3", "name": "Legal Council: Board Member Management Access", "historyId": "086853cbbdf9d178e4dadad1c47b2d8c:94ad96afc690c6d345a3d6e3ad9d1bce", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > chromium-en > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Legal Council Role Access Control"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "chromium-en"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Legal Council Role Access Control"}], "links": [], "start": 1751869837389, "testCaseId": "086853cbbdf9d178e4dadad1c47b2d8c", "fullName": "tests/authentication-and-rbac.spec.ts:264:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Legal Council Role Access Control"], "stop": 1751869837389}