{"uuid": "f004bff5-f69e-49e9-92ff-3e2e76888f90", "name": "should persist language preference across page navigation", "historyId": "505dff47ef2db1ac36a2d22f4a648a5f:2d9eadd8e5698e677c4123f9b6b8cac6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Microsoft Edge"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-error-handling.spec.ts"}, {"name": "titlePath", "value": " > Microsoft Edge > tests\\localization-error-handling.spec.ts > Localization and Error Handling > Language Switching and Persistence"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "Microsoft Edge"}, {"name": "suite", "value": "tests\\localization-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Language Switching and Persistence"}], "links": [], "start": 1751869838045, "testCaseId": "505dff47ef2db1ac36a2d22f4a648a5f", "fullName": "tests/localization-error-handling.spec.ts:156:9", "titlePath": ["tests", "localization-error-handling.spec.ts", "Localization and Error Handling", "Language Switching and Persistence"], "stop": 1751869838045}