{"uuid": "f0637ee6-36ff-474c-87c4-ee5a65e92b6e", "name": "should display Arabic correctly in firefox", "historyId": "425757085ca622d8f2696e29502c7c75:370d56a8895ca6917b09138f18b00251", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "webkit-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-error-handling.spec.ts"}, {"name": "titlePath", "value": " > webkit-ar > tests\\localization-error-handling.spec.ts > Localization and Error Handling > Cross-Browser Compatibility"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "webkit-ar"}, {"name": "suite", "value": "tests\\localization-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Cross-Browser Compatibility"}], "links": [], "start": 1751869837326, "testCaseId": "425757085ca622d8f2696e29502c7c75", "fullName": "tests/localization-error-handling.spec.ts:288:11", "titlePath": ["tests", "localization-error-handling.spec.ts", "Localization and Error Handling", "Cross-Browser Compatibility"], "stop": 1751869837326}