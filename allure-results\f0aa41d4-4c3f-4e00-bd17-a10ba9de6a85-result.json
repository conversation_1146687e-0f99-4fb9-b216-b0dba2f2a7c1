{"uuid": "f0aa41d4-4c3f-4e00-bd17-a10ba9de6a85", "name": "should display error messages with proper positioning in RTL", "historyId": "3b0077a68dec2444263b4e95548e7405:f0494070a4792d94c64308aac1604abb", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Safari"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-error-handling.spec.ts"}, {"name": "titlePath", "value": " > Mobile Safari > tests\\localization-error-handling.spec.ts > Localization and Error Handling > Error Message Display and UX"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "Mobile Safari"}, {"name": "suite", "value": "tests\\localization-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Error Message Display and UX"}], "links": [], "start": 1751869837919, "testCaseId": "3b0077a68dec2444263b4e95548e7405", "fullName": "tests/localization-error-handling.spec.ts:472:9", "titlePath": ["tests", "localization-error-handling.spec.ts", "Localization and Error Handling", "Error Message Display and UX"], "stop": 1751869837919}