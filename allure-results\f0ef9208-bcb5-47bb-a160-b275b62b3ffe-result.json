{"uuid": "f0ef9208-bcb5-47bb-a160-b275b62b3ffe", "name": "Verify Draft Resolution Actions", "historyId": "05be8bf1479076a55e8b972352a5cae9:73db7be642bfc9fe252d7c3eec44a098", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-lifecycle.spec.ts"}, {"name": "titlePath", "value": " > chromium-ar > tests\\resolution-lifecycle.spec.ts > Resolution Lifecycle Management > Resolution State Validation"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "chromium-ar"}, {"name": "suite", "value": "tests\\resolution-lifecycle.spec.ts"}, {"name": "subSuite", "value": "Resolution Lifecycle Management > Resolution State Validation"}], "links": [], "start": 1751869837092, "testCaseId": "05be8bf1479076a55e8b972352a5cae9", "fullName": "tests/resolution-lifecycle.spec.ts:229:9", "titlePath": ["tests", "resolution-lifecycle.spec.ts", "Resolution Lifecycle Management", "Resolution State Validation"], "stop": 1751869837092}