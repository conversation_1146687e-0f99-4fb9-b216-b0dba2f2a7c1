{"uuid": "f10915a3-8c6c-4385-9459-74d29c0f133e", "name": "Legal Council: Board Member Management Access", "historyId": "086853cbbdf9d178e4dadad1c47b2d8c:f0494070a4792d94c64308aac1604abb", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Safari"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > Mobile Safari > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Legal Council Role Access Control"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "Mobile Safari"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Legal Council Role Access Control"}], "links": [], "start": 1751856347703, "testCaseId": "086853cbbdf9d178e4dadad1c47b2d8c", "fullName": "tests/authentication-and-rbac.spec.ts:264:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Legal Council Role Access Control"], "stop": 1751856347703}