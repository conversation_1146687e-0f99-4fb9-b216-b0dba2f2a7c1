{"uuid": "f11b66ee-423c-4488-bb12-826b3c8096db", "name": "Verify Arabic Font Rendering and Unicode Support", "historyId": "fc63ff105932a20784c5368cd30a0a40:2d9eadd8e5698e677c4123f9b6b8cac6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Microsoft Edge"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-and-error-handling.spec.ts"}, {"name": "titlePath", "value": " > Microsoft Edge > tests\\localization-and-error-handling.spec.ts > Localization and Error Handling > Arabic Language and RTL Layout"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "Microsoft Edge"}, {"name": "suite", "value": "tests\\localization-and-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Arabic Language and RTL Layout"}], "links": [], "start": 1751856347910, "testCaseId": "fc63ff105932a20784c5368cd30a0a40", "fullName": "tests/localization-and-error-handling.spec.ts:139:9", "titlePath": ["tests", "localization-and-error-handling.spec.ts", "Localization and Error Handling", "Arabic Language and RTL Layout"], "stop": 1751856347910}