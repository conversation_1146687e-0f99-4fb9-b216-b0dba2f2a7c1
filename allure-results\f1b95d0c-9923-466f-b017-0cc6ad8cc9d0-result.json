{"uuid": "f1b95d0c-9923-466f-b017-0cc6ad8cc9d0", "name": "validate test environment state", "historyId": "1c06143af9eba8617305277c95d9d617:5002af1d94715955e69bf287b9bb17d2", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "cleanup"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "setup.global.cleanup.ts"}, {"name": "titlePath", "value": " > cleanup > setup\\global.cleanup.ts"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "cleanup"}, {"name": "suite", "value": "setup\\global.cleanup.ts"}], "links": [], "start": 1751869836942, "testCaseId": "1c06143af9eba8617305277c95d9d617", "fullName": "setup/global.cleanup.ts:292:8", "titlePath": ["setup", "global.cleanup.ts"], "stop": 1751869836943}