{"uuid": "f1bed492-f6e7-4bca-a23d-832392b82538", "name": "Verify Voting Suspension Notifications to All Stakeholders", "historyId": "bd1d44b52c92d3dd56deed68629f3ef9:2d9eadd8e5698e677c4123f9b6b8cac6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Microsoft Edge"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-alternative-workflows.spec.ts"}, {"name": "titlePath", "value": " > Microsoft Edge > tests\\resolution-alternative-workflows.spec.ts > Resolution Alternative Workflows > Alternative 1: Voting Suspension Workflow"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "Microsoft Edge"}, {"name": "suite", "value": "tests\\resolution-alternative-workflows.spec.ts"}, {"name": "subSuite", "value": "Resolution Alternative Workflows > Alternative 1: Voting Suspension Workflow"}], "links": [], "start": 1751856347956, "testCaseId": "bd1d44b52c92d3dd56deed68629f3ef9", "fullName": "tests/resolution-alternative-workflows.spec.ts:113:9", "titlePath": ["tests", "resolution-alternative-workflows.spec.ts", "Resolution Alternative Workflows", "Alternative 1: Voting Suspension Workflow"], "stop": 1751856347956}