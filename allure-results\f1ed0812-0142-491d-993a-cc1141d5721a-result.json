{"uuid": "f1ed0812-0142-491d-993a-cc1141d5721a", "name": "should handle mixed Arabic-English text", "historyId": "6390a4563f0f058d28b6cda0746e8d64:5b3555343836716531a392c635640134", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-error-handling.spec.ts"}, {"name": "titlePath", "value": " > firefox-en > tests\\localization-error-handling.spec.ts > Localization and Error Handling > Font Rendering and Unicode Support"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "firefox-en"}, {"name": "suite", "value": "tests\\localization-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Font Rendering and Unicode Support"}], "links": [], "start": 1751869837608, "testCaseId": "6390a4563f0f058d28b6cda0746e8d64", "fullName": "tests/localization-error-handling.spec.ts:367:9", "titlePath": ["tests", "localization-error-handling.spec.ts", "Localization and Error Handling", "Font Rendering and Unicode Support"], "stop": 1751869837608}