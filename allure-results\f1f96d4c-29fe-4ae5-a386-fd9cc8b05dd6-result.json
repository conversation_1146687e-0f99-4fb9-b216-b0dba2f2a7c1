{"uuid": "f1f96d4c-29fe-4ae5-a386-fd9cc8b05dd6", "name": "Verify Pending Resolution Actions", "historyId": "df88be07a3916c70e6fc575412d45c91:f6a8753cfef08377165401815dd911d6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-lifecycle.spec.ts"}, {"name": "titlePath", "value": " > firefox-ar > tests\\resolution-lifecycle.spec.ts > Resolution Lifecycle Management > Resolution State Validation"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "firefox-ar"}, {"name": "suite", "value": "tests\\resolution-lifecycle.spec.ts"}, {"name": "subSuite", "value": "Resolution Lifecycle Management > Resolution State Validation"}], "links": [], "start": 1751856347086, "testCaseId": "df88be07a3916c70e6fc575412d45c91", "fullName": "tests/resolution-lifecycle.spec.ts:246:9", "titlePath": ["tests", "resolution-lifecycle.spec.ts", "Resolution Lifecycle Management", "Resolution State Validation"], "stop": 1751856347086}