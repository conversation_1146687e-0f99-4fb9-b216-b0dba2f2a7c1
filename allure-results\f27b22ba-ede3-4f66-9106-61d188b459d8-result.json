{"uuid": "f27b22ba-ede3-4f66-9106-61d188b459d8", "name": "Verify Arabic Dashboard Layout and Navigation", "historyId": "ed2e2a66e2b706de7c21b901cdf5e653:96797789f4669ba5e9de53cebde9126b", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-and-error-handling.spec.ts"}, {"name": "titlePath", "value": " > Mobile Chrome > tests\\localization-and-error-handling.spec.ts > Localization and Error Handling > Arabic Language and RTL Layout"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "Mobile Chrome"}, {"name": "suite", "value": "tests\\localization-and-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Arabic Language and RTL Layout"}], "links": [], "start": 1751869837711, "testCaseId": "ed2e2a66e2b706de7c21b901cdf5e653", "fullName": "tests/localization-and-error-handling.spec.ts:69:9", "titlePath": ["tests", "localization-and-error-handling.spec.ts", "Localization and Error Handling", "Arabic Language and RTL Layout"], "stop": 1751869837711}