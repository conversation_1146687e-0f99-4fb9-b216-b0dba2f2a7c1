{"uuid": "f286b268-a158-4df4-b5c0-746a8b1e88cb", "name": "Session Timeout Handling", "historyId": "588ba1481be19d42d0c3ab125e4f46b7:f6a8753cfef08377165401815dd911d6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > firefox-ar > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Authentication Flow"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "firefox-ar"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Authentication Flow"}], "links": [], "start": 1751856346974, "testCaseId": "588ba1481be19d42d0c3ab125e4f46b7", "fullName": "tests/authentication-and-rbac.spec.ts:113:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Authentication Flow"], "stop": 1751856346974}