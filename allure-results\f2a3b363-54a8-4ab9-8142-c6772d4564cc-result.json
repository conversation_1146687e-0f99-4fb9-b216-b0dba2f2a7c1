{"uuid": "f2a3b363-54a8-4ab9-8142-c6772d4564cc", "name": "Verify Arabic Resolution Management Interface", "historyId": "8dd5b1e969c0ea0b928c93b169de0395:370d56a8895ca6917b09138f18b00251", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "webkit-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-and-error-handling.spec.ts"}, {"name": "titlePath", "value": " > webkit-ar > tests\\localization-and-error-handling.spec.ts > Localization and Error Handling > Arabic Language and RTL Layout"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "webkit-ar"}, {"name": "suite", "value": "tests\\localization-and-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Arabic Language and RTL Layout"}], "links": [], "start": 1751856347167, "testCaseId": "8dd5b1e969c0ea0b928c93b169de0395", "fullName": "tests/localization-and-error-handling.spec.ts:115:9", "titlePath": ["tests", "localization-and-error-handling.spec.ts", "Localization and Error Handling", "Arabic Language and RTL Layout"], "stop": 1751856347167}