{"uuid": "f2ca1742-4371-4dc0-9065-ea00b70154f3", "name": "Verify Arabic Text Rendering Across Browsers", "historyId": "d5ee680cc12e89e7d709ac18149b3f81:2d9eadd8e5698e677c4123f9b6b8cac6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Microsoft Edge"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-and-error-handling.spec.ts"}, {"name": "titlePath", "value": " > Microsoft Edge > tests\\localization-and-error-handling.spec.ts > Localization and Error Handling > Cross-Browser Compatibility"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "Microsoft Edge"}, {"name": "suite", "value": "tests\\localization-and-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Cross-Browser Compatibility"}], "links": [], "start": 1751856347929, "testCaseId": "d5ee680cc12e89e7d709ac18149b3f81", "fullName": "tests/localization-and-error-handling.spec.ts:398:9", "titlePath": ["tests", "localization-and-error-handling.spec.ts", "Localization and Error Handling", "Cross-Browser Compatibility"], "stop": 1751856347929}