{"uuid": "f2eccf98-e1c9-4fe6-b8f7-6a32b43939ab", "name": "Board Member: View-Only Fund and Member Access", "historyId": "fc060ecde5cb783469717a03cd7e3549:94ad96afc690c6d345a3d6e3ad9d1bce", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > chromium-en > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Board Member Role Access Control"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "chromium-en"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Board Member Role Access Control"}], "links": [], "start": 1751856347268, "testCaseId": "fc060ecde5cb783469717a03cd7e3549", "fullName": "tests/authentication-and-rbac.spec.ts:372:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Board Member Role Access Control"], "stop": 1751856347268}