{"uuid": "f2f95e94-e9aa-4d1e-8114-a89aab7aa9ed", "name": "Fund Manager: Fund Management Permissions", "historyId": "5cb4845e610de2236c409a1f2de1771a:f0494070a4792d94c64308aac1604abb", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Safari"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > Mobile Safari > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Fund Manager Role Access Control"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "Mobile Safari"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Fund Manager Role Access Control"}], "links": [], "start": 1751869837820, "testCaseId": "5cb4845e610de2236c409a1f2de1771a", "fullName": "tests/authentication-and-rbac.spec.ts:172:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Fund Manager Role Access Control"], "stop": 1751869837820}