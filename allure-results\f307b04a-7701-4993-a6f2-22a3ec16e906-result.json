{"uuid": "f307b04a-7701-4993-a6f2-22a3ec16e906", "name": "Board Secretary: Fund View-Only Access", "historyId": "1b69a281fb5f21a5143dc2c34dee77f1:96797789f4669ba5e9de53cebde9126b", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > Mobile Chrome > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Board Secretary Role Access Control"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "Mobile Chrome"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Board Secretary Role Access Control"}], "links": [], "start": 1751869837673, "testCaseId": "1b69a281fb5f21a5143dc2c34dee77f1", "fullName": "tests/authentication-and-rbac.spec.ts:309:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Board Secretary Role Access Control"], "stop": 1751869837673}