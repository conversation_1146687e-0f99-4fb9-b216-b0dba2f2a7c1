{"uuid": "f36dce7e-a570-40ac-9f61-307ca4ae9d60", "name": "Verify Required Field Validation", "historyId": "6e05c5c1c7ed1798880ae0c7f3c0ec01:370d56a8895ca6917b09138f18b00251", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "webkit-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.board-member-management.spec.ts"}, {"name": "titlePath", "value": " > webkit-ar > tests\\board-member-management.spec.ts > Board Member Management > Business Rule Validation"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "webkit-ar"}, {"name": "suite", "value": "tests\\board-member-management.spec.ts"}, {"name": "subSuite", "value": "Board Member Management > Business Rule Validation"}], "links": [], "start": 1751869837281, "testCaseId": "6e05c5c1c7ed1798880ae0c7f3c0ec01", "fullName": "tests/board-member-management.spec.ts:424:9", "titlePath": ["tests", "board-member-management.spec.ts", "Board Member Management", "Business Rule Validation"], "stop": 1751869837281}