{"uuid": "f3ec6fe3-7265-4609-b5dd-ff9707debd49", "name": "Verify New Resolution Code Generation and Relationship", "historyId": "249f9621a9743614a6cad722c9bf8be5:94ad96afc690c6d345a3d6e3ad9d1bce", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-alternative-workflows.spec.ts"}, {"name": "titlePath", "value": " > chromium-en > tests\\resolution-alternative-workflows.spec.ts > Resolution Alternative Workflows > Alternative 2: New Resolution from Approved/NotApproved"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "chromium-en"}, {"name": "suite", "value": "tests\\resolution-alternative-workflows.spec.ts"}, {"name": "subSuite", "value": "Resolution Alternative Workflows > Alternative 2: New Resolution from Approved/NotApproved"}], "links": [], "start": 1751869837488, "testCaseId": "249f9621a9743614a6cad722c9bf8be5", "fullName": "tests/resolution-alternative-workflows.spec.ts:261:9", "titlePath": ["tests", "resolution-alternative-workflows.spec.ts", "Resolution Alternative Workflows", "Alternative 2: New Resolution from Approved/NotApproved"], "stop": 1751869837488}