{"uuid": "f43408ee-f869-42cb-b358-51fc49359250", "name": "Verify Voting Suspension Notifications to All Stakeholders", "historyId": "bd1d44b52c92d3dd56deed68629f3ef9:370d56a8895ca6917b09138f18b00251", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "webkit-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-alternative-workflows.spec.ts"}, {"name": "titlePath", "value": " > webkit-ar > tests\\resolution-alternative-workflows.spec.ts > Resolution Alternative Workflows > Alternative 1: Voting Suspension Workflow"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "webkit-ar"}, {"name": "suite", "value": "tests\\resolution-alternative-workflows.spec.ts"}, {"name": "subSuite", "value": "Resolution Alternative Workflows > Alternative 1: Voting Suspension Workflow"}], "links": [], "start": 1751869837342, "testCaseId": "bd1d44b52c92d3dd56deed68629f3ef9", "fullName": "tests/resolution-alternative-workflows.spec.ts:113:9", "titlePath": ["tests", "resolution-alternative-workflows.spec.ts", "Resolution Alternative Workflows", "Alternative 1: Voting Suspension Workflow"], "stop": 1751869837342}