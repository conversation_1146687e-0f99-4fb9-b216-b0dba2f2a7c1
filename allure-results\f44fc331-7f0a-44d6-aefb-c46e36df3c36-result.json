{"uuid": "f44fc331-7f0a-44d6-aefb-c46e36df3c36", "name": "Verify State Consistency After Alternative Workflows", "historyId": "13d259707a803d441a68c075b2d0edf7:370d56a8895ca6917b09138f18b00251", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "webkit-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-alternative-workflows.spec.ts"}, {"name": "titlePath", "value": " > webkit-ar > tests\\resolution-alternative-workflows.spec.ts > Resolution Alternative Workflows > Alternative Workflow Business Rules"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "webkit-ar"}, {"name": "suite", "value": "tests\\resolution-alternative-workflows.spec.ts"}, {"name": "subSuite", "value": "Resolution Alternative Workflows > Alternative Workflow Business Rules"}], "links": [], "start": 1751856347211, "testCaseId": "13d259707a803d441a68c075b2d0edf7", "fullName": "tests/resolution-alternative-workflows.spec.ts:377:9", "titlePath": ["tests", "resolution-alternative-workflows.spec.ts", "Resolution Alternative Workflows", "Alternative Workflow Business Rules"], "stop": 1751856347211}