{"uuid": "f481e767-751d-4c71-b065-8e92ff4322a5", "name": "should display Arabic correctly in webkit", "historyId": "224f869f1ec37379d737fd3083c71b29:5b3555343836716531a392c635640134", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-error-handling.spec.ts"}, {"name": "titlePath", "value": " > firefox-en > tests\\localization-error-handling.spec.ts > Localization and Error Handling > Cross-Browser Compatibility"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "firefox-en"}, {"name": "suite", "value": "tests\\localization-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Cross-Browser Compatibility"}], "links": [], "start": 1751869837602, "testCaseId": "224f869f1ec37379d737fd3083c71b29", "fullName": "tests/localization-error-handling.spec.ts:288:11", "titlePath": ["tests", "localization-error-handling.spec.ts", "Localization and Error Handling", "Cross-Browser Compatibility"], "stop": 1751869837602}