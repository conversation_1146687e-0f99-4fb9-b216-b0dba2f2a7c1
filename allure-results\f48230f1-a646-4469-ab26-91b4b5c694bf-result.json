{"uuid": "f48230f1-a646-4469-ab26-91b4b5c694bf", "name": "should load Arabic pages within performance thresholds", "historyId": "27fdf9af7907a785516d58ed3e82032a:2d9eadd8e5698e677c4123f9b6b8cac6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Microsoft Edge"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-error-handling.spec.ts"}, {"name": "titlePath", "value": " > Microsoft Edge > tests\\localization-error-handling.spec.ts > Localization and Error Handling > Performance and Accessibility"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "Microsoft Edge"}, {"name": "suite", "value": "tests\\localization-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Performance and Accessibility"}], "links": [], "start": 1751869838067, "testCaseId": "27fdf9af7907a785516d58ed3e82032a", "fullName": "tests/localization-error-handling.spec.ts:424:9", "titlePath": ["tests", "localization-error-handling.spec.ts", "Localization and Error Handling", "Performance and Accessibility"], "stop": 1751869838067}