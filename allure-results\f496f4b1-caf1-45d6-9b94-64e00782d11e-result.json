{"uuid": "f496f4b1-caf1-45d6-9b94-64e00782d11e", "name": "Board Member: View Limited Member Information", "historyId": "7d1440a7fd99e291bbf72fe74355660c:73db7be642bfc9fe252d7c3eec44a098", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.board-member-management.spec.ts"}, {"name": "titlePath", "value": " > chromium-ar > tests\\board-member-management.spec.ts > Board Member Management > View Board Members"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "chromium-ar"}, {"name": "suite", "value": "tests\\board-member-management.spec.ts"}, {"name": "subSuite", "value": "Board Member Management > View Board Members"}], "links": [], "start": 1751869836995, "testCaseId": "7d1440a7fd99e291bbf72fe74355660c", "fullName": "tests/board-member-management.spec.ts:288:9", "titlePath": ["tests", "board-member-management.spec.ts", "Board Member Management", "View Board Members"], "stop": 1751869836995}