{"uuid": "f4b6e5eb-6563-4bb7-820a-d3c1152dd6dc", "name": "User Access Limited to Assigned Funds Only", "historyId": "bf1a3e47d151385a70d34cb53157d9aa:73db7be642bfc9fe252d7c3eec44a098", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > chromium-ar > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Fund-Specific Permissions"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "chromium-ar"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Fund-Specific Permissions"}], "links": [], "start": 1751856346859, "testCaseId": "bf1a3e47d151385a70d34cb53157d9aa", "fullName": "tests/authentication-and-rbac.spec.ts:397:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Fund-Specific Permissions"], "stop": 1751856346859}