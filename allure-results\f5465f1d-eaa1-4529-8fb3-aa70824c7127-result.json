{"uuid": "f5465f1d-eaa1-4529-8fb3-aa70824c7127", "name": "Fund Manager: Fund Management Permissions", "historyId": "5cb4845e610de2236c409a1f2de1771a:f6a8753cfef08377165401815dd911d6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > firefox-ar > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Fund Manager Role Access Control"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "firefox-ar"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Fund Manager Role Access Control"}], "links": [], "start": 1751869837111, "testCaseId": "5cb4845e610de2236c409a1f2de1771a", "fullName": "tests/authentication-and-rbac.spec.ts:172:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Fund Manager Role Access Control"], "stop": 1751869837111}