{"uuid": "f57deb68-7732-4ae6-a8e4-f35dfb726864", "name": "Verify Fund Activities Disabled Without Minimum Members", "historyId": "a92c5ec13db62176133bde17abf98f2a:f6a8753cfef08377165401815dd911d6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.board-member-management.spec.ts"}, {"name": "titlePath", "value": " > firefox-ar > tests\\board-member-management.spec.ts > Board Member Management > Business Rule Validation"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "firefox-ar"}, {"name": "suite", "value": "tests\\board-member-management.spec.ts"}, {"name": "subSuite", "value": "Board Member Management > Business Rule Validation"}], "links": [], "start": 1751856347021, "testCaseId": "a92c5ec13db62176133bde17abf98f2a", "fullName": "tests/board-member-management.spec.ts:397:9", "titlePath": ["tests", "board-member-management.spec.ts", "Board Member Management", "Business Rule Validation"], "stop": 1751856347021}