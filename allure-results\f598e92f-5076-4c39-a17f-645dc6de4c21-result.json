{"uuid": "f598e92f-5076-4c39-a17f-645dc6de4c21", "name": "Logout Functionality", "historyId": "59a58779fe29dabae3d855cb0e1d8ea1:f0494070a4792d94c64308aac1604abb", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Safari"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > Mobile Safari > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Authentication Flow"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "Mobile Safari"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Authentication Flow"}], "links": [], "start": 1751869837815, "testCaseId": "59a58779fe29dabae3d855cb0e1d8ea1", "fullName": "tests/authentication-and-rbac.spec.ts:96:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Authentication Flow"], "stop": 1751869837815}