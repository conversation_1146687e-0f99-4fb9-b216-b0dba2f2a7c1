{"uuid": "f61f3b23-3061-4531-9c22-983fc8c60d33", "name": "Verify Language Switch Functionality", "historyId": "b8096cda4948a18fea345330732de175:f6a8753cfef08377165401815dd911d6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-and-error-handling.spec.ts"}, {"name": "titlePath", "value": " > firefox-ar > tests\\localization-and-error-handling.spec.ts > Localization and Error Handling > Language Switching and Persistence"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "firefox-ar"}, {"name": "suite", "value": "tests\\localization-and-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Language Switching and Persistence"}], "links": [], "start": 1751869837161, "testCaseId": "b8096cda4948a18fea345330732de175", "fullName": "tests/localization-and-error-handling.spec.ts:234:9", "titlePath": ["tests", "localization-and-error-handling.spec.ts", "Localization and Error Handling", "Language Switching and Persistence"], "stop": 1751869837161}