{"uuid": "f627ce51-41f8-45b3-bf50-a2d884ed1f58", "name": "Verify Arabic Resolution Management Interface", "historyId": "8dd5b1e969c0ea0b928c93b169de0395:96797789f4669ba5e9de53cebde9126b", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-and-error-handling.spec.ts"}, {"name": "titlePath", "value": " > Mobile Chrome > tests\\localization-and-error-handling.spec.ts > Localization and Error Handling > Arabic Language and RTL Layout"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "Mobile Chrome"}, {"name": "suite", "value": "tests\\localization-and-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Arabic Language and RTL Layout"}], "links": [], "start": 1751869837714, "testCaseId": "8dd5b1e969c0ea0b928c93b169de0395", "fullName": "tests/localization-and-error-handling.spec.ts:115:9", "titlePath": ["tests", "localization-and-error-handling.spec.ts", "Localization and Error Handling", "Arabic Language and RTL Layout"], "stop": 1751869837714}