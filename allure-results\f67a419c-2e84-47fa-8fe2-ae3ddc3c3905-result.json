{"uuid": "f67a419c-2e84-47fa-8fe2-ae3ddc3c3905", "name": "Verify Voting Suspension Notifications to All Stakeholders", "historyId": "bd1d44b52c92d3dd56deed68629f3ef9:5b3555343836716531a392c635640134", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-alternative-workflows.spec.ts"}, {"name": "titlePath", "value": " > firefox-en > tests\\resolution-alternative-workflows.spec.ts > Resolution Alternative Workflows > Alternative 1: Voting Suspension Workflow"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "firefox-en"}, {"name": "suite", "value": "tests\\resolution-alternative-workflows.spec.ts"}, {"name": "subSuite", "value": "Resolution Alternative Workflows > Alternative 1: Voting Suspension Workflow"}], "links": [], "start": 1751869837620, "testCaseId": "bd1d44b52c92d3dd56deed68629f3ef9", "fullName": "tests/resolution-alternative-workflows.spec.ts:113:9", "titlePath": ["tests", "resolution-alternative-workflows.spec.ts", "Resolution Alternative Workflows", "Alternative 1: Voting Suspension Workflow"], "stop": 1751869837620}