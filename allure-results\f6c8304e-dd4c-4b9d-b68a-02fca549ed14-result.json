{"uuid": "f6c8304e-dd4c-4b9d-b68a-02fca549ed14", "name": "Verify Chairman Uniqueness Constraint", "historyId": "9ef8096d41bb6709aceb1c3866ca2b5d:f0494070a4792d94c64308aac1604abb", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Safari"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.board-member-management.spec.ts"}, {"name": "titlePath", "value": " > Mobile Safari > tests\\board-member-management.spec.ts > Board Member Management > Add Board Members - Legal Council/Board Secretary"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "Mobile Safari"}, {"name": "suite", "value": "tests\\board-member-management.spec.ts"}, {"name": "subSuite", "value": "Board Member Management > Add Board Members - Legal Council/Board Secretary"}], "links": [], "start": 1751856347730, "testCaseId": "9ef8096d41bb6709aceb1c3866ca2b5d", "fullName": "tests/board-member-management.spec.ts:224:9", "titlePath": ["tests", "board-member-management.spec.ts", "Board Member Management", "Add Board Members - Legal Council/Board Secretary"], "stop": 1751856347730}