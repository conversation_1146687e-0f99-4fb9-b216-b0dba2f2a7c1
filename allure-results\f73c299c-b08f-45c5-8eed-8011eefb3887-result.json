{"uuid": "f73c299c-b08f-45c5-8eed-8011eefb3887", "name": "Fund Manager: Resolution Management Permissions", "historyId": "72eadc7e06f7b3369d6647e041f528bd:f6a8753cfef08377165401815dd911d6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > firefox-ar > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Fund Manager Role Access Control"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "firefox-ar"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Fund Manager Role Access Control"}], "links": [], "start": 1751856346980, "testCaseId": "72eadc7e06f7b3369d6647e041f528bd", "fullName": "tests/authentication-and-rbac.spec.ts:196:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Fund Manager Role Access Control"], "stop": 1751856346981}