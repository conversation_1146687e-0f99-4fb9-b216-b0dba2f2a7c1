{"uuid": "f7403c7a-6bf3-484d-9781-a24ac26ac8a8", "name": "Board Secretary: Suspend Voting by Adding Resolution Items", "historyId": "38b53aaf1a17c5b21cdb9663cc86ffab:2d9eadd8e5698e677c4123f9b6b8cac6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Microsoft Edge"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-alternative-workflows.spec.ts"}, {"name": "titlePath", "value": " > Microsoft Edge > tests\\resolution-alternative-workflows.spec.ts > Resolution Alternative Workflows > Alternative 1: Voting Suspension Workflow"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "Microsoft Edge"}, {"name": "suite", "value": "tests\\resolution-alternative-workflows.spec.ts"}, {"name": "subSuite", "value": "Resolution Alternative Workflows > Alternative 1: Voting Suspension Workflow"}], "links": [], "start": 1751869838074, "testCaseId": "38b53aaf1a17c5b21cdb9663cc86ffab", "fullName": "tests/resolution-alternative-workflows.spec.ts:74:9", "titlePath": ["tests", "resolution-alternative-workflows.spec.ts", "Resolution Alternative Workflows", "Alternative 1: Voting Suspension Workflow"], "stop": 1751869838074}