{"uuid": "f754c51c-3803-460c-a736-50a4993a98e2", "name": "Verify Fund Must Have 2 Independent Members for Voting", "historyId": "6505d0ee1a546a7e904daf965c8edb07:5b3555343836716531a392c635640134", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-lifecycle.spec.ts"}, {"name": "titlePath", "value": " > firefox-en > tests\\resolution-lifecycle.spec.ts > Resolution Lifecycle Management > Business Rule Validation"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "firefox-en"}, {"name": "suite", "value": "tests\\resolution-lifecycle.spec.ts"}, {"name": "subSuite", "value": "Resolution Lifecycle Management > Business Rule Validation"}], "links": [], "start": 1751856347528, "testCaseId": "6505d0ee1a546a7e904daf965c8edb07", "fullName": "tests/resolution-lifecycle.spec.ts:302:9", "titlePath": ["tests", "resolution-lifecycle.spec.ts", "Resolution Lifecycle Management", "Business Rule Validation"], "stop": 1751856347528}