{"uuid": "f7593863-4c6f-4d03-82c1-af5137a4b76e", "name": "Verify Required Field Validation", "historyId": "6e05c5c1c7ed1798880ae0c7f3c0ec01:94ad96afc690c6d345a3d6e3ad9d1bce", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.board-member-management.spec.ts"}, {"name": "titlePath", "value": " > chromium-en > tests\\board-member-management.spec.ts > Board Member Management > Business Rule Validation"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "chromium-en"}, {"name": "suite", "value": "tests\\board-member-management.spec.ts"}, {"name": "subSuite", "value": "Board Member Management > Business Rule Validation"}], "links": [], "start": 1751869837417, "testCaseId": "6e05c5c1c7ed1798880ae0c7f3c0ec01", "fullName": "tests/board-member-management.spec.ts:424:9", "titlePath": ["tests", "board-member-management.spec.ts", "Board Member Management", "Business Rule Validation"], "stop": 1751869837417}