{"uuid": "f7ae3470-56d7-4c13-bc7d-16d825115b57", "name": "Session Timeout Handling", "historyId": "588ba1481be19d42d0c3ab125e4f46b7:96797789f4669ba5e9de53cebde9126b", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > Mobile Chrome > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Authentication Flow"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "Mobile Chrome"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Authentication Flow"}], "links": [], "start": 1751869837656, "testCaseId": "588ba1481be19d42d0c3ab125e4f46b7", "fullName": "tests/authentication-and-rbac.spec.ts:113:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Authentication Flow"], "stop": 1751869837656}