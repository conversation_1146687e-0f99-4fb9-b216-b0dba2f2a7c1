{"uuid": "f85a3785-fe37-4648-8822-275c96833638", "name": "Verify Maximum Independent Members Limit (14 members)", "historyId": "8f6138c20663269ddecb9285bf7b4f2b:f0494070a4792d94c64308aac1604abb", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Safari"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.board-member-management.spec.ts"}, {"name": "titlePath", "value": " > Mobile Safari > tests\\board-member-management.spec.ts > Board Member Management > Add Board Members - Legal Council/Board Secretary"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "Mobile Safari"}, {"name": "suite", "value": "tests\\board-member-management.spec.ts"}, {"name": "subSuite", "value": "Board Member Management > Add Board Members - Legal Council/Board Secretary"}], "links": [], "start": 1751856347728, "testCaseId": "8f6138c20663269ddecb9285bf7b4f2b", "fullName": "tests/board-member-management.spec.ts:182:9", "titlePath": ["tests", "board-member-management.spec.ts", "Board Member Management", "Add Board Members - Legal Council/Board Secretary"], "stop": 1751856347728}