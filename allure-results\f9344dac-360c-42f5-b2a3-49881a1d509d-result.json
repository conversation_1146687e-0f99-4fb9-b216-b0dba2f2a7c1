{"uuid": "f9344dac-360c-42f5-b2a3-49881a1d509d", "name": "should load Arabic pages within performance thresholds", "historyId": "27fdf9af7907a785516d58ed3e82032a:94ad96afc690c6d345a3d6e3ad9d1bce", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-error-handling.spec.ts"}, {"name": "titlePath", "value": " > chromium-en > tests\\localization-error-handling.spec.ts > Localization and Error Handling > Performance and Accessibility"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "chromium-en"}, {"name": "suite", "value": "tests\\localization-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Performance and Accessibility"}], "links": [], "start": 1751869837473, "testCaseId": "27fdf9af7907a785516d58ed3e82032a", "fullName": "tests/localization-error-handling.spec.ts:424:9", "titlePath": ["tests", "localization-error-handling.spec.ts", "Localization and Error Handling", "Performance and Accessibility"], "stop": 1751869837473}