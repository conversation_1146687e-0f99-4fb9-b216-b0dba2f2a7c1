{"uuid": "f938d129-e999-4567-952d-dbd28d82b02f", "name": "Legal Council: Full Fund and Resolution Access", "historyId": "9b620bc7f6d829e04e919924dcd0707a:2d9eadd8e5698e677c4123f9b6b8cac6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Microsoft Edge"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > Microsoft Edge > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Legal Council Role Access Control"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "Microsoft Edge"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Legal Council Role Access Control"}], "links": [], "start": 1751856347861, "testCaseId": "9b620bc7f6d829e04e919924dcd0707a", "fullName": "tests/authentication-and-rbac.spec.ts:240:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Legal Council Role Access Control"], "stop": 1751856347861}