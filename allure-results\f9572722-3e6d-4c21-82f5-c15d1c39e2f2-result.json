{"uuid": "f9572722-3e6d-4c21-82f5-c15d1c39e2f2", "name": "Board Secretary: <PERSON><PERSON> New Resolution from Not Approved Resolution", "historyId": "e16730958a900206a3aebb6ac229a1fc:94ad96afc690c6d345a3d6e3ad9d1bce", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-alternative-workflows.spec.ts"}, {"name": "titlePath", "value": " > chromium-en > tests\\resolution-alternative-workflows.spec.ts > Resolution Alternative Workflows > Alternative 2: New Resolution from Approved/NotApproved"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "chromium-en"}, {"name": "suite", "value": "tests\\resolution-alternative-workflows.spec.ts"}, {"name": "subSuite", "value": "Resolution Alternative Workflows > Alternative 2: New Resolution from Approved/NotApproved"}], "links": [], "start": 1751856347348, "testCaseId": "e16730958a900206a3aebb6ac229a1fc", "fullName": "tests/resolution-alternative-workflows.spec.ts:226:9", "titlePath": ["tests", "resolution-alternative-workflows.spec.ts", "Resolution Alternative Workflows", "Alternative 2: New Resolution from Approved/NotApproved"], "stop": 1751856347348}