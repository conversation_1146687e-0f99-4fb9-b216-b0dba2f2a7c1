{"uuid": "f998359a-8bce-4b9b-9dcc-459e8a852512", "name": "Board Member: Resolution Voting Access Only", "historyId": "012e97c0d865ff60893843ced178b493:96797789f4669ba5e9de53cebde9126b", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > Mobile Chrome > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Board Member Role Access Control"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "Mobile Chrome"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Board Member Role Access Control"}], "links": [], "start": 1751869837676, "testCaseId": "012e97c0d865ff60893843ced178b493", "fullName": "tests/authentication-and-rbac.spec.ts:345:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Board Member Role Access Control"], "stop": 1751869837676}