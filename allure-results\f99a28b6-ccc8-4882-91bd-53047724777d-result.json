{"uuid": "f99a28b6-ccc8-4882-91bd-53047724777d", "name": "User Access Limited to Assigned Funds Only", "historyId": "bf1a3e47d151385a70d34cb53157d9aa:f6a8753cfef08377165401815dd911d6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > firefox-ar > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Fund-Specific Permissions"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "firefox-ar"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Fund-Specific Permissions"}], "links": [], "start": 1751856346995, "testCaseId": "bf1a3e47d151385a70d34cb53157d9aa", "fullName": "tests/authentication-and-rbac.spec.ts:397:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Fund-Specific Permissions"], "stop": 1751856346996}