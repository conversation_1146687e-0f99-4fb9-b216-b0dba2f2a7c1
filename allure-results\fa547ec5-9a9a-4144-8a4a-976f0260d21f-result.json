{"uuid": "fa547ec5-9a9a-4144-8a4a-976f0260d21f", "name": "Unauthorized API Access Prevention", "historyId": "354af82c3f282832cbd42b3f318edaad:370d56a8895ca6917b09138f18b00251", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "webkit-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > webkit-ar > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Security Features"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "webkit-ar"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Security Features"}], "links": [], "start": 1751856347133, "testCaseId": "354af82c3f282832cbd42b3f318edaad", "fullName": "tests/authentication-and-rbac.spec.ts:463:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Security Features"], "stop": 1751856347133}