{"uuid": "fa58b018-23ff-4c5f-ad9b-3f2e6f7ba8ae", "name": "Handle Network Errors During State Transitions", "historyId": "3b411f353bb34b4e67b879ced7ad10f8:370d56a8895ca6917b09138f18b00251", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "webkit-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-lifecycle.spec.ts"}, {"name": "titlePath", "value": " > webkit-ar > tests\\resolution-lifecycle.spec.ts > Resolution Lifecycle Management > Error Handling and Edge Cases"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "webkit-ar"}, {"name": "suite", "value": "tests\\resolution-lifecycle.spec.ts"}, {"name": "subSuite", "value": "Resolution Lifecycle Management > Error Handling and Edge Cases"}], "links": [], "start": 1751856347235, "testCaseId": "3b411f353bb34b4e67b879ced7ad10f8", "fullName": "tests/resolution-lifecycle.spec.ts:370:9", "titlePath": ["tests", "resolution-lifecycle.spec.ts", "Resolution Lifecycle Management", "Error <PERSON> and <PERSON> Cases"], "stop": 1751856347235}