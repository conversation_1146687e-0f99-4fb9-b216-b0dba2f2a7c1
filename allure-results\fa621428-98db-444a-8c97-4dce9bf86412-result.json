{"uuid": "fa621428-98db-444a-8c97-4dce9bf86412", "name": "Legal Council: Board Member Management Access", "historyId": "086853cbbdf9d178e4dadad1c47b2d8c:370d56a8895ca6917b09138f18b00251", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "webkit-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > webkit-ar > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Legal Council Role Access Control"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "webkit-ar"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Legal Council Role Access Control"}], "links": [], "start": 1751869837254, "testCaseId": "086853cbbdf9d178e4dadad1c47b2d8c", "fullName": "tests/authentication-and-rbac.spec.ts:264:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Legal Council Role Access Control"], "stop": 1751869837255}