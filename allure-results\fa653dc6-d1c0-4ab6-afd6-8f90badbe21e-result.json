{"uuid": "fa653dc6-d1c0-4ab6-afd6-8f90badbe21e", "name": "Verify Concurrent Editing Prevention", "historyId": "3ec54902971164fc369e694373920449:96797789f4669ba5e9de53cebde9126b", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-lifecycle.spec.ts"}, {"name": "titlePath", "value": " > Mobile Chrome > tests\\resolution-lifecycle.spec.ts > Resolution Lifecycle Management > Error Handling and Edge Cases"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "Mobile Chrome"}, {"name": "suite", "value": "tests\\resolution-lifecycle.spec.ts"}, {"name": "subSuite", "value": "Resolution Lifecycle Management > Error Handling and Edge Cases"}], "links": [], "start": 1751869837809, "testCaseId": "3ec54902971164fc369e694373920449", "fullName": "tests/resolution-lifecycle.spec.ts:389:9", "titlePath": ["tests", "resolution-lifecycle.spec.ts", "Resolution Lifecycle Management", "Error <PERSON> and <PERSON> Cases"], "stop": 1751869837809}