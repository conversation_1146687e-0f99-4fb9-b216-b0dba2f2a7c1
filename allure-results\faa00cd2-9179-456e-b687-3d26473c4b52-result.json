{"uuid": "faa00cd2-9179-456e-b687-3d26473c4b52", "name": "should display Arabic layout correctly on mobile", "historyId": "34e5c03677316b34185e1faa5f2be1a2:f0494070a4792d94c64308aac1604abb", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Safari"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-error-handling.spec.ts"}, {"name": "titlePath", "value": " > Mobile Safari > tests\\localization-error-handling.spec.ts > Localization and Error Handling > Responsive Design and Mobile Testing"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "Mobile Safari"}, {"name": "suite", "value": "tests\\localization-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Responsive Design and Mobile Testing"}], "links": [], "start": 1751869837912, "testCaseId": "34e5c03677316b34185e1faa5f2be1a2", "fullName": "tests/localization-error-handling.spec.ts:386:9", "titlePath": ["tests", "localization-error-handling.spec.ts", "Localization and Error Handling", "Responsive Design and Mobile Testing"], "stop": 1751869837912}