{"uuid": "fafcb3cf-6504-46fc-93cd-9b1bc75824b4", "name": "should support keyboard navigation in both languages", "historyId": "aee5bd5575f86026d8fc1ae7daee4e54:f6a8753cfef08377165401815dd911d6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-error-handling.spec.ts"}, {"name": "titlePath", "value": " > firefox-ar > tests\\localization-error-handling.spec.ts > Localization and Error Handling > Performance and Accessibility"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "firefox-ar"}, {"name": "suite", "value": "tests\\localization-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Performance and Accessibility"}], "links": [], "start": 1751869837208, "testCaseId": "aee5bd5575f86026d8fc1ae7daee4e54", "fullName": "tests/localization-error-handling.spec.ts:454:9", "titlePath": ["tests", "localization-error-handling.spec.ts", "Localization and Error Handling", "Performance and Accessibility"], "stop": 1751869837208}