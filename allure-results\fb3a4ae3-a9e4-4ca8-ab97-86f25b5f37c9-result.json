{"uuid": "fb3a4ae3-a9e4-4ca8-ab97-86f25b5f37c9", "name": "Verify Alternative Workflows Maintain Audit Trail", "historyId": "d56af881752b0cb969186691e258ad74:94ad96afc690c6d345a3d6e3ad9d1bce", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-alternative-workflows.spec.ts"}, {"name": "titlePath", "value": " > chromium-en > tests\\resolution-alternative-workflows.spec.ts > Resolution Alternative Workflows > Alternative Workflow Business Rules"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "chromium-en"}, {"name": "suite", "value": "tests\\resolution-alternative-workflows.spec.ts"}, {"name": "subSuite", "value": "Resolution Alternative Workflows > Alternative Workflow Business Rules"}], "links": [], "start": 1751869837491, "testCaseId": "d56af881752b0cb969186691e258ad74", "fullName": "tests/resolution-alternative-workflows.spec.ts:344:9", "titlePath": ["tests", "resolution-alternative-workflows.spec.ts", "Resolution Alternative Workflows", "Alternative Workflow Business Rules"], "stop": 1751869837492}