{"uuid": "fbab3b02-46ec-4cf7-8a1b-bd03f963a6f8", "name": "Board Member: Resolution Voting Access Only", "historyId": "012e97c0d865ff60893843ced178b493:5b3555343836716531a392c635640134", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.authentication-and-rbac.spec.ts"}, {"name": "titlePath", "value": " > firefox-en > tests\\authentication-and-rbac.spec.ts > Authentication and Role-Based Access Control > Board Member Role Access Control"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "firefox-en"}, {"name": "suite", "value": "tests\\authentication-and-rbac.spec.ts"}, {"name": "subSuite", "value": "Authentication and Role-Based Access Control > Board Member Role Access Control"}], "links": [], "start": 1751869837530, "testCaseId": "012e97c0d865ff60893843ced178b493", "fullName": "tests/authentication-and-rbac.spec.ts:345:9", "titlePath": ["tests", "authentication-and-rbac.spec.ts", "Authentication and Role-Based Access Control", "Board Member Role Access Control"], "stop": 1751869837530}