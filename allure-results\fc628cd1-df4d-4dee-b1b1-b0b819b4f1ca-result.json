{"uuid": "fc628cd1-df4d-4dee-b1b1-b0b819b4f1ca", "name": "Verify Error Message Auto-Dismiss", "historyId": "c7872de09f4435b09cb0ecad2c805a12:f0494070a4792d94c64308aac1604abb", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Safari"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-and-error-handling.spec.ts"}, {"name": "titlePath", "value": " > Mobile Safari > tests\\localization-and-error-handling.spec.ts > Localization and Error Handling > Error Message Display and UX"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "Mobile Safari"}, {"name": "suite", "value": "tests\\localization-and-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Error Message Display and UX"}], "links": [], "start": 1751856347795, "testCaseId": "c7872de09f4435b09cb0ecad2c805a12", "fullName": "tests/localization-and-error-handling.spec.ts:505:9", "titlePath": ["tests", "localization-and-error-handling.spec.ts", "Localization and Error Handling", "Error Message Display and UX"], "stop": 1751856347795}