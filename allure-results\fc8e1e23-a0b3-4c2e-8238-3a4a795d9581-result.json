{"uuid": "fc8e1e23-a0b3-4c2e-8238-3a4a795d9581", "name": "Verify Voting Methodology Enforcement", "historyId": "29b94be45a2d747b0afc622ff3e52143:94ad96afc690c6d345a3d6e3ad9d1bce", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-lifecycle.spec.ts"}, {"name": "titlePath", "value": " > chromium-en > tests\\resolution-lifecycle.spec.ts > Resolution Lifecycle Management > Business Rule Validation"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "chromium-en"}, {"name": "suite", "value": "tests\\resolution-lifecycle.spec.ts"}, {"name": "subSuite", "value": "Resolution Lifecycle Management > Business Rule Validation"}], "links": [], "start": 1751869837509, "testCaseId": "29b94be45a2d747b0afc622ff3e52143", "fullName": "tests/resolution-lifecycle.spec.ts:350:9", "titlePath": ["tests", "resolution-lifecycle.spec.ts", "Resolution Lifecycle Management", "Business Rule Validation"], "stop": 1751869837510}