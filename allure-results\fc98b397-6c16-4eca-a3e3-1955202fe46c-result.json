{"uuid": "fc98b397-6c16-4eca-a3e3-1955202fe46c", "name": "Board Secretary: <PERSON><PERSON> New Resolution from Not Approved Resolution", "historyId": "e16730958a900206a3aebb6ac229a1fc:370d56a8895ca6917b09138f18b00251", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "webkit-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-alternative-workflows.spec.ts"}, {"name": "titlePath", "value": " > webkit-ar > tests\\resolution-alternative-workflows.spec.ts > Resolution Alternative Workflows > Alternative 2: New Resolution from Approved/NotApproved"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "webkit-ar"}, {"name": "suite", "value": "tests\\resolution-alternative-workflows.spec.ts"}, {"name": "subSuite", "value": "Resolution Alternative Workflows > Alternative 2: New Resolution from Approved/NotApproved"}], "links": [], "start": 1751869837346, "testCaseId": "e16730958a900206a3aebb6ac229a1fc", "fullName": "tests/resolution-alternative-workflows.spec.ts:226:9", "titlePath": ["tests", "resolution-alternative-workflows.spec.ts", "Resolution Alternative Workflows", "Alternative 2: New Resolution from Approved/NotApproved"], "stop": 1751869837346}