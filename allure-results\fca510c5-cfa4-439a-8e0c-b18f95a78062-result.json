{"uuid": "fca510c5-cfa4-439a-8e0c-b18f95a78062", "name": "should handle form validation in webkit", "historyId": "f124693791cf1fb5b87b1781c43f0e2f:96797789f4669ba5e9de53cebde9126b", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-error-handling.spec.ts"}, {"name": "titlePath", "value": " > Mobile Chrome > tests\\localization-error-handling.spec.ts > Localization and Error Handling > Cross-Browser Compatibility"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "Mobile Chrome"}, {"name": "suite", "value": "tests\\localization-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Cross-Browser Compatibility"}], "links": [], "start": 1751869837764, "testCaseId": "f124693791cf1fb5b87b1781c43f0e2f", "fullName": "tests/localization-error-handling.spec.ts:308:11", "titlePath": ["tests", "localization-error-handling.spec.ts", "Localization and Error Handling", "Cross-Browser Compatibility"], "stop": 1751869837764}