{"uuid": "fd5f29be-b522-42b4-9519-2223bc56e5cd", "name": "should switch languages dynamically", "historyId": "7bed05209feb1cf1e925f186aecfd9d8:5b3555343836716531a392c635640134", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-error-handling.spec.ts"}, {"name": "titlePath", "value": " > firefox-en > tests\\localization-error-handling.spec.ts > Localization and Error Handling > Language Switching and Persistence"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "firefox-en"}, {"name": "suite", "value": "tests\\localization-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Language Switching and Persistence"}], "links": [], "start": 1751869837589, "testCaseId": "7bed05209feb1cf1e925f186aecfd9d8", "fullName": "tests/localization-error-handling.spec.ts:137:9", "titlePath": ["tests", "localization-error-handling.spec.ts", "Localization and Error Handling", "Language Switching and Persistence"], "stop": 1751869837589}