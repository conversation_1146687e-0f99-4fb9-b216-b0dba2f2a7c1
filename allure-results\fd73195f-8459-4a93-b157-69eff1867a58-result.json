{"uuid": "fd73195f-8459-4a93-b157-69eff1867a58", "name": "Verify Alternative Workflows Maintain Audit Trail", "historyId": "d56af881752b0cb969186691e258ad74:2d9eadd8e5698e677c4123f9b6b8cac6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Microsoft Edge"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-alternative-workflows.spec.ts"}, {"name": "titlePath", "value": " > Microsoft Edge > tests\\resolution-alternative-workflows.spec.ts > Resolution Alternative Workflows > Alternative Workflow Business Rules"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "Microsoft Edge"}, {"name": "suite", "value": "tests\\resolution-alternative-workflows.spec.ts"}, {"name": "subSuite", "value": "Resolution Alternative Workflows > Alternative Workflow Business Rules"}], "links": [], "start": 1751856347970, "testCaseId": "d56af881752b0cb969186691e258ad74", "fullName": "tests/resolution-alternative-workflows.spec.ts:344:9", "titlePath": ["tests", "resolution-alternative-workflows.spec.ts", "Resolution Alternative Workflows", "Alternative Workflow Business Rules"], "stop": 1751856347970}