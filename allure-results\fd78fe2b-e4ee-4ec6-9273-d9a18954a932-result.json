{"uuid": "fd78fe2b-e4ee-4ec6-9273-d9a18954a932", "name": "should render Arabic fonts correctly", "historyId": "5c866da23a7153e8bc18755f338a9a9e:370d56a8895ca6917b09138f18b00251", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "webkit-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-error-handling.spec.ts"}, {"name": "titlePath", "value": " > webkit-ar > tests\\localization-error-handling.spec.ts > Localization and Error Handling > Font Rendering and Unicode Support"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "webkit-ar"}, {"name": "suite", "value": "tests\\localization-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Font Rendering and Unicode Support"}], "links": [], "start": 1751869837331, "testCaseId": "5c866da23a7153e8bc18755f338a9a9e", "fullName": "tests/localization-error-handling.spec.ts:332:9", "titlePath": ["tests", "localization-error-handling.spec.ts", "Localization and Error Handling", "Font Rendering and Unicode Support"], "stop": 1751869837331}