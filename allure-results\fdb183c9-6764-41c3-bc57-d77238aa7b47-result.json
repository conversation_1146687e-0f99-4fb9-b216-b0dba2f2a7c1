{"uuid": "fdb183c9-6764-41c3-bc57-d77238aa7b47", "name": "Verify Voting Methodology Enforcement", "historyId": "29b94be45a2d747b0afc622ff3e52143:f6a8753cfef08377165401815dd911d6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-lifecycle.spec.ts"}, {"name": "titlePath", "value": " > firefox-ar > tests\\resolution-lifecycle.spec.ts > Resolution Lifecycle Management > Business Rule Validation"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "firefox-ar"}, {"name": "suite", "value": "tests\\resolution-lifecycle.spec.ts"}, {"name": "subSuite", "value": "Resolution Lifecycle Management > Business Rule Validation"}], "links": [], "start": 1751856347095, "testCaseId": "29b94be45a2d747b0afc622ff3e52143", "fullName": "tests/resolution-lifecycle.spec.ts:350:9", "titlePath": ["tests", "resolution-lifecycle.spec.ts", "Resolution Lifecycle Management", "Business Rule Validation"], "stop": 1751856347095}