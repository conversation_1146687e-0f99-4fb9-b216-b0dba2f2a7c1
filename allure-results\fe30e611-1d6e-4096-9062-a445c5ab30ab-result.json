{"uuid": "fe30e611-1d6e-4096-9062-a445c5ab30ab", "name": "should load Arabic pages within performance thresholds", "historyId": "27fdf9af7907a785516d58ed3e82032a:f0494070a4792d94c64308aac1604abb", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Safari"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-error-handling.spec.ts"}, {"name": "titlePath", "value": " > Mobile Safari > tests\\localization-error-handling.spec.ts > Localization and Error Handling > Performance and Accessibility"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "Mobile Safari"}, {"name": "suite", "value": "tests\\localization-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Performance and Accessibility"}], "links": [], "start": 1751869837914, "testCaseId": "27fdf9af7907a785516d58ed3e82032a", "fullName": "tests/localization-error-handling.spec.ts:424:9", "titlePath": ["tests", "localization-error-handling.spec.ts", "Localization and Error Handling", "Performance and Accessibility"], "stop": 1751869837914}