{"uuid": "fe5a7aab-d4d4-4608-9726-5d3f591a9be6", "name": "should display English dashboard with proper navigation", "historyId": "eb1b7da02e3df43cd8839020f4c61c78:2d9eadd8e5698e677c4123f9b6b8cac6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Microsoft Edge"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-error-handling.spec.ts"}, {"name": "titlePath", "value": " > Microsoft Edge > tests\\localization-error-handling.spec.ts > Localization and Error Handling > English Language and LTR Layout"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "Microsoft Edge"}, {"name": "suite", "value": "tests\\localization-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > English Language and LTR Layout"}], "links": [], "start": 1751869838043, "testCaseId": "eb1b7da02e3df43cd8839020f4c61c78", "fullName": "tests/localization-error-handling.spec.ts:118:9", "titlePath": ["tests", "localization-error-handling.spec.ts", "Localization and Error Handling", "English Language and LTR Layout"], "stop": 1751869838043}