{"uuid": "fe69246a-bd9e-4f05-a791-d7e9737ce45f", "name": "should load Arabic pages within performance thresholds", "historyId": "27fdf9af7907a785516d58ed3e82032a:f6a8753cfef08377165401815dd911d6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-error-handling.spec.ts"}, {"name": "titlePath", "value": " > firefox-ar > tests\\localization-error-handling.spec.ts > Localization and Error Handling > Performance and Accessibility"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "firefox-ar"}, {"name": "suite", "value": "tests\\localization-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Performance and Accessibility"}], "links": [], "start": 1751869837205, "testCaseId": "27fdf9af7907a785516d58ed3e82032a", "fullName": "tests/localization-error-handling.spec.ts:424:9", "titlePath": ["tests", "localization-error-handling.spec.ts", "Localization and Error Handling", "Performance and Accessibility"], "stop": 1751869837205}