{"uuid": "feaf4ff0-0c44-4316-aa56-d62e3262bc13", "name": "Board Secretary: Full CRUD Access", "historyId": "d9332574ca81e17b036a5289a8ab15c1:94ad96afc690c6d345a3d6e3ad9d1bce", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.board-member-management.spec.ts"}, {"name": "titlePath", "value": " > chromium-en > tests\\board-member-management.spec.ts > Board Member Management > Role-Based Access Control"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "chromium-en"}, {"name": "suite", "value": "tests\\board-member-management.spec.ts"}, {"name": "subSuite", "value": "Board Member Management > Role-Based Access Control"}], "links": [], "start": 1751856347294, "testCaseId": "d9332574ca81e17b036a5289a8ab15c1", "fullName": "tests/board-member-management.spec.ts:342:9", "titlePath": ["tests", "board-member-management.spec.ts", "Board Member Management", "Role-Based Access Control"], "stop": 1751856347294}