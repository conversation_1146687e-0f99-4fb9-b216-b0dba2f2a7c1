{"uuid": "ff3320a0-6b51-47d0-83b9-caa952b12ff3", "name": "Verify Error Message Accessibility", "historyId": "c932fc02771bc5be9ba3b7d4c47ee461:94ad96afc690c6d345a3d6e3ad9d1bce", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-en"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-and-error-handling.spec.ts"}, {"name": "titlePath", "value": " > chromium-en > tests\\localization-and-error-handling.spec.ts > Localization and Error Handling > Error Message Display and UX"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "chromium-en"}, {"name": "suite", "value": "tests\\localization-and-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > Error Message Display and UX"}], "links": [], "start": 1751869837442, "testCaseId": "c932fc02771bc5be9ba3b7d4c47ee461", "fullName": "tests/localization-and-error-handling.spec.ts:482:9", "titlePath": ["tests", "localization-and-error-handling.spec.ts", "Localization and Error Handling", "Error Message Display and UX"], "stop": 1751869837442}