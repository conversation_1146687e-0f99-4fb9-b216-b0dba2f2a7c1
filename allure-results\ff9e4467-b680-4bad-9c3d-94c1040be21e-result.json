{"uuid": "ff9e4467-b680-4bad-9c3d-94c1040be21e", "name": "Verify Fund Must Have 2 Independent Members for Voting", "historyId": "6505d0ee1a546a7e904daf965c8edb07:f0494070a4792d94c64308aac1604abb", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Safari"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.resolution-lifecycle.spec.ts"}, {"name": "titlePath", "value": " > Mobile Safari > tests\\resolution-lifecycle.spec.ts > Resolution Lifecycle Management > Business Rule Validation"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-25408-worker-0"}, {"name": "parentSuite", "value": "Mobile Safari"}, {"name": "suite", "value": "tests\\resolution-lifecycle.spec.ts"}, {"name": "subSuite", "value": "Resolution Lifecycle Management > Business Rule Validation"}], "links": [], "start": 1751856347838, "testCaseId": "6505d0ee1a546a7e904daf965c8edb07", "fullName": "tests/resolution-lifecycle.spec.ts:302:9", "titlePath": ["tests", "resolution-lifecycle.spec.ts", "Resolution Lifecycle Management", "Business Rule Validation"], "stop": 1751856347838}