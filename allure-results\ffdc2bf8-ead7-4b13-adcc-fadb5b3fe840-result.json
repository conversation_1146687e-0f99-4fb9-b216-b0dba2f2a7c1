{"uuid": "ffdc2bf8-ead7-4b13-adcc-fadb5b3fe840", "name": "Board Secretary: Full CRUD Access", "historyId": "d9332574ca81e17b036a5289a8ab15c1:73db7be642bfc9fe252d7c3eec44a098", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium-ar"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.board-member-management.spec.ts"}, {"name": "titlePath", "value": " > chromium-ar > tests\\board-member-management.spec.ts > Board Member Management > Role-Based Access Control"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "chromium-ar"}, {"name": "suite", "value": "tests\\board-member-management.spec.ts"}, {"name": "subSuite", "value": "Board Member Management > Role-Based Access Control"}], "links": [], "start": 1751869836999, "testCaseId": "d9332574ca81e17b036a5289a8ab15c1", "fullName": "tests/board-member-management.spec.ts:342:9", "titlePath": ["tests", "board-member-management.spec.ts", "Board Member Management", "Role-Based Access Control"], "stop": 1751869837000}