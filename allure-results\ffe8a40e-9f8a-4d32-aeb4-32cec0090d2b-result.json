{"uuid": "ffe8a40e-9f8a-4d32-aeb4-32cec0090d2b", "name": "should display English dashboard with proper navigation", "historyId": "eb1b7da02e3df43cd8839020f4c61c78:96797789f4669ba5e9de53cebde9126b", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "tests.localization-error-handling.spec.ts"}, {"name": "titlePath", "value": " > Mobile Chrome > tests\\localization-error-handling.spec.ts > Localization and Error Handling > English Language and LTR Layout"}, {"name": "host", "value": "DESKTOP-RS2IBJM"}, {"name": "thread", "value": "pid-35584-worker-0"}, {"name": "parentSuite", "value": "Mobile Chrome"}, {"name": "suite", "value": "tests\\localization-error-handling.spec.ts"}, {"name": "subSuite", "value": "Localization and Error Handling > English Language and LTR Layout"}], "links": [], "start": 1751869837743, "testCaseId": "eb1b7da02e3df43cd8839020f4c61c78", "fullName": "tests/localization-error-handling.spec.ts:118:9", "titlePath": ["tests", "localization-error-handling.spec.ts", "Localization and Error Handling", "English Language and LTR Layout"], "stop": 1751869837743}