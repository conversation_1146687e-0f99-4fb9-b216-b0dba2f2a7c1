# List View Development Standards for JadwaUI

## 📋 Overview

This document establishes mandatory development standards for all list-based user stories in JadwaUI. These standards are derived from the successful resolutions implementation and must be followed consistently across all modules.

## 🎯 Core Requirements

### 1. Card Layout Requirements ✅ MANDATORY

**All list views MUST use individual card components (not grid items) matching Figma design specifications.**

#### Implementation Standards:
- **Individual Cards**: Each list item must be rendered as a separate card component
- **Figma Compliance**: Design must match provided Figma specifications exactly
- **Responsive Design**: Cards must adapt to different screen sizes using CSS Grid
- **Hover Effects**: Implement smooth hover transitions and visual feedback
- **RTL/LTR Support**: Proper support for both Arabic and English layouts

#### Example Structure:
```html
<div class="items-grid" *ngIf="!isLoading && !hasError">
  <div class="item-card" *ngFor="let item of filteredItems">
    <div class="card-header">
      <div class="item-identifier">{{ item.code }}</div>
      <div class="card-actions">
        <button class="action-btn edit-btn" 
                *ngIf="item.canEdit"
                (click)="editItem(item)">
          <i class="fas fa-edit"></i>
        </button>
        <button class="action-btn delete-btn" 
                *ngIf="item.canDelete"
                (click)="deleteItem(item)">
          <i class="fas fa-trash"></i>
        </button>
      </div>
    </div>
    <div class="card-content">
      <h3 class="item-title">{{ item.title }}</h3>
      <p class="item-description">{{ item.description }}</p>
      <div class="item-meta">
        <div class="meta-item">
          <span class="meta-label">{{ 'COMMON.STATUS' | translate }}:</span>
          <span class="meta-value status" [ngClass]="'status-' + item.status">
            {{ getStatusText(item.status) | translate }}
          </span>
        </div>
      </div>
    </div>
    <div class="card-footer">
      <div class="footer-info">
        <span class="creation-date">{{ item.createdDate | date:'dd/MM/yyyy' }}</span>
        <span class="creator">{{ item.createdBy }}</span>
      </div>
    </div>
  </div>
</div>
```

### 2. API Integration Standards ✅ MANDATORY

**No mock data allowed - all components must integrate directly with NSwag-generated service proxies.**

#### Implementation Requirements:
- **Direct API Integration**: Use NSwag-generated service proxies directly
- **No Mock Data**: Remove all mock data arrays and methods
- **Proper Error Handling**: Implement comprehensive error handling with user-friendly messages
- **Loading States**: Show loading indicators during API calls
- **Real-time Data**: Always fetch fresh data from API

#### Example Implementation:
```typescript
// ❌ WRONG - Mock data
private loadMockData(): void {
  this.items = [/* mock data */];
}

// ✅ CORRECT - Real API integration
loadItems(search: string = '', status?: StatusEnum): void {
  if (this.currentEntityId <= 0) {
    this.hasError = true;
    this.errorMessage = 'COMMON.INVALID_ENTITY_ID';
    return;
  }

  this.isLoading = true;
  this.hasError = false;

  this.serviceProxy.itemsList(
    this.currentEntityId,
    status,
    undefined, // additional filters
    this.currentPage - 1,
    this.pageSize,
    search,
    'createdAt desc'
  ).subscribe({
    next: (response) => {
      this.isLoading = false;
      if (response.successed && response.data) {
        this.items = response.data.map(item => this.mapToDisplayFormat(item));
        this.filteredItems = [...this.items];
        this.totalCount = response.totalCount || 0;
        this.totalPages = response.totalPages || 0;
      } else {
        this.hasError = true;
        this.errorMessage = response.message || 'COMMON.LOAD_ERROR';
      }
    },
    error: (error) => {
      this.isLoading = false;
      this.hasError = true;
      this.errorMessage = 'COMMON.LOAD_ERROR';
      console.error('Error loading items:', error);
    }
  });
}
```

### 3. Advanced Search Implementation ✅ MANDATORY

**All list views must include advanced search modals following the fund filter-dialog pattern.**

#### Required Features:
- **Search Input**: Basic text search functionality
- **Advanced Filters**: Modal dialog with multiple filter options
- **Filter Persistence**: Maintain filter state during navigation
- **Clear Filters**: Option to reset all filters
- **Real-time Search**: Search triggers API calls, not client-side filtering

#### Example Advanced Search Dialog:
```typescript
openFilter(): void {
  const dialogRef = this.dialog.open(AdvancedSearchDialogComponent, {
    width: '600px',
    data: {
      search: '',
      status: '',
      fromDate: '',
      toDate: '',
      createdBy: ''
    }
  });

  dialogRef.afterClosed().subscribe(result => {
    if (result) {
      this.applyFilters(result);
    }
  });
}

private applyFilters(filters: any): void {
  let statusEnum: StatusEnum | undefined;
  if (filters.status) {
    statusEnum = this.getStatusEnum(filters.status);
  }
  this.loadItems(filters.search || '', statusEnum);
}
```

### 4. Pagination Requirements ✅ MANDATORY

**Implement proper pagination using the API's pageNo/pageSize parameters.**

#### Implementation Standards:
- **Server-side Pagination**: Use API pagination, not client-side
- **Page Size Options**: Provide configurable page sizes (10, 25, 50, 100)
- **Navigation Controls**: Previous/Next buttons and page numbers
- **Total Count Display**: Show "X of Y items" information
- **URL State**: Maintain pagination state in URL parameters

#### Example Pagination:
```typescript
// Pagination properties
currentPage = 1;
pageSize = 10;
totalCount = 0;
totalPages = 0;

onPageChange(page: number): void {
  this.currentPage = page;
  this.loadItems();
}

onPageSizeChange(size: number): void {
  this.pageSize = size;
  this.currentPage = 1; // Reset to first page
  this.loadItems();
}
```

### 5. State Management Requirements ✅ MANDATORY

**Implement proper loading, error, and empty states.**

#### Required States:
- **Loading State**: Show spinner during API calls
- **Error State**: Display error message with retry option
- **Empty State**: Show when no data is available
- **Success State**: Normal data display

#### Example State Management:
```typescript
// State properties
isLoading = false;
hasError = false;
errorMessage = '';

// Template conditions
*ngIf="isLoading"                           // Loading state
*ngIf="hasError && !isLoading"             // Error state  
*ngIf="!isLoading && !hasError && items.length === 0"  // Empty state
*ngIf="!isLoading && !hasError && items.length > 0"    // Success state
```

## 🏗️ Architectural Patterns

### Component Structure Template

```typescript
export interface ItemDisplay {
  id: number;
  code: string;
  title: string;
  description: string;
  status: string;
  createdDate: string;
  createdBy: string;
  canEdit: boolean;
  canDelete: boolean;
}

@Component({
  selector: 'app-items-list',
  standalone: true,
  imports: [
    CommonModule,
    PageHeaderComponent,
    BreadcrumbComponent,
    MatIconModule,
    MatButtonModule,
    TranslateModule
  ],
  templateUrl: './items-list.component.html',
  styleUrl: './items-list.component.scss'
})
export class ItemsListComponent implements OnInit {
  // Data properties
  items: ItemDisplay[] = [];
  filteredItems: ItemDisplay[] = [];
  currentEntityId = 0;
  
  // Pagination properties
  currentPage = 1;
  pageSize = 10;
  totalCount = 0;
  totalPages = 0;
  
  // State properties
  isLoading = false;
  hasError = false;
  errorMessage = '';
  
  // UI properties
  isHasPermissionAdd: boolean = false;
  breadcrumbSizeEnum = SizeEnum;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private dialog: MatDialog,
    private translateService: TranslateService,
    private tokenService: TokenService,
    private serviceProxy: ServiceProxy
  ) {}

  ngOnInit(): void {
    this.isHasPermissionAdd = this.tokenService.hasPermission('EntityName');
    
    this.route.queryParams.subscribe(params => {
      this.currentEntityId = +params['entityId'] || 0;
      if (this.currentEntityId) {
        this.loadItems();
      } else {
        this.hasError = true;
        this.errorMessage = 'COMMON.INVALID_ENTITY_ID';
      }
    });
  }

  loadItems(search: string = '', status?: StatusEnum): void {
    // Implementation as shown above
  }

  private mapToDisplayFormat(item: ApiResponseItem): ItemDisplay {
    return {
      id: item.id || 0,
      code: item.code || '',
      title: item.title || '',
      description: item.description || '',
      status: this.getStatusKey(item.status),
      createdDate: item.createdAt ? new Date(item.createdAt.toString()).toLocaleDateString('ar-SA') : '',
      createdBy: item.createdBy || '',
      canEdit: item.canEdit || false,
      canDelete: item.canDelete || false
    };
  }

  onSearch(event: any): void {
    const searchTerm = event.target.value?.trim() || '';
    this.loadItems(searchTerm);
  }

  openFilter(): void {
    // Advanced search implementation
  }

  editItem(item: ItemDisplay): void {
    this.router.navigate(['/admin/entity/edit'], {
      queryParams: { id: item.id, entityId: this.currentEntityId }
    });
  }

  deleteItem(item: ItemDisplay): void {
    // Delete implementation with confirmation
  }
}
```

## 🎨 CSS/SCSS Standards

### Required CSS Structure

All list view components must follow this CSS structure for consistency:

```scss
.items-page {
  padding: 24px;
  background-color: #f8f9fa;
  min-height: 100vh;

  .breadcrumb-section {
    margin-bottom: 20px;
  }

  .page-header-section {
    margin-bottom: 32px;
  }

  .content-section {
    .search-filters-section {
      margin-bottom: 32px;

      .search-container {
        display: flex;
        gap: 16px;
        align-items: center;
        flex-wrap: wrap;

        .search-field {
          position: relative;
          flex: 1;
          min-width: 300px;

          .search-input {
            width: 100%;
            height: 48px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 0 48px 0 16px;
            font-size: 14px;
            background-color: white;
            transition: all 0.2s ease;

            &:focus {
              border-color: #00205a;
              outline: none;
              box-shadow: 0 0 0 3px rgba(0, 32, 90, 0.1);
            }
          }

          .search-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #9e9e9e;
            font-size: 16px;
          }
        }

        .filter-button {
          height: 48px;
          padding: 0 20px;
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          background-color: white;
          color: #666;
          font-size: 14px;
          font-weight: 500;
          display: flex;
          align-items: center;
          gap: 8px;
          transition: all 0.2s ease;

          &:hover {
            border-color: #00205a;
            color: #00205a;
            background-color: #f8f9ff;
          }
        }
      }
    }

    .items-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 24px;
      margin-bottom: 32px;

      .item-card {
        background: white;
        border-radius: 12px;
        border: 1px solid #e0e0e0;
        overflow: hidden;
        transition: all 0.2s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          transform: translateY(-2px);
        }

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20px 24px 16px;
          border-bottom: 1px solid #f0f0f0;

          .item-identifier {
            background: #00205a;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 16px;
            min-width: 60px;
            text-align: center;
          }

          .card-actions {
            display: flex;
            gap: 8px;

            .action-btn {
              width: 36px;
              height: 36px;
              border: none;
              border-radius: 6px;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              transition: all 0.2s ease;
              font-size: 14px;

              &.edit-btn {
                background-color: #fff3cd;
                color: #856404;

                &:hover {
                  background-color: #ffeaa7;
                  transform: scale(1.05);
                }
              }

              &.delete-btn {
                background-color: #f8d7da;
                color: #721c24;

                &:hover {
                  background-color: #f5c6cb;
                  transform: scale(1.05);
                }
              }
            }
          }
        }

        .card-content {
          padding: 20px 24px;

          .item-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            line-height: 1.4;
          }

          .item-description {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
            margin-bottom: 20px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
        }

        .card-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px 24px 20px;
          background-color: #fafafa;
          border-top: 1px solid #f0f0f0;

          .footer-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
            font-size: 12px;
            color: #888;

            .creation-date {
              font-weight: 500;
            }

            .creator {
              color: #666;
            }
          }
        }
      }
    }
  }
}
```

## 📝 Translation Standards

### Required Translation Keys

All list view components must include these translation keys:

```json
{
  "MODULE_NAME": {
    "ITEMS": {
      "TITLE": "Items",
      "ADD": "Add Item",
      "SEARCH": "Search",
      "SEARCH_PLACEHOLDER": "Search by item code...",
      "ADVANCED_SEARCH": "Advanced Search",
      "RESET": "Reset",
      "FILTER_BY_STATUS": "Filter by Status",
      "FROM_DATE": "From Date",
      "TO_DATE": "To Date",
      "ALL_STATUSES": "All Statuses",
      "APPLY_FILTERS": "Apply Filters",
      "CLEAR_FILTERS": "Clear Filters",
      "STATUS": "Status",
      "STATUS_DRAFT": "Draft",
      "STATUS_PENDING": "Pending",
      "STATUS_APPROVED": "Approved",
      "STATUS_REJECTED": "Rejected",
      "STATUS_UNKNOWN": "Unknown",
      "NO_ITEMS": "No Items",
      "NO_ITEMS_MESSAGE": "No items have been created yet",
      "CREATE_FIRST": "Create First Item",
      "DELETE_CONFIRM": "Are you sure you want to delete this item?",
      "DELETED_SUCCESS": "Item deleted successfully",
      "DELETE_NOT_ALLOWED": "You don't have permission to delete this item",
      "DELETE_ERROR": "Error occurred while deleting item",
      "LOAD_ERROR": "Error occurred while loading items",
      "LOADING": "Loading items..."
    }
  },
  "COMMON": {
    "EDIT": "Edit",
    "DELETE": "Delete",
    "CANCEL": "Cancel",
    "CONFIRM_DELETE": "Confirm Delete",
    "DELETED": "Deleted",
    "RETRY": "Retry",
    "ERROR": "Error",
    "OK": "OK"
  }
}
```

## 🧪 Testing Requirements

### Unit Testing Standards

Each list view component must include comprehensive unit tests:

```typescript
describe('ItemsListComponent', () => {
  let component: ItemsListComponent;
  let fixture: ComponentFixture<ItemsListComponent>;
  let mockServiceProxy: jasmine.SpyObj<ServiceProxy>;

  beforeEach(async () => {
    const spy = jasmine.createSpyObj('ServiceProxy', ['itemsList', 'cancelItem']);

    await TestBed.configureTestingModule({
      imports: [ItemsListComponent, TranslateModule.forRoot()],
      providers: [
        { provide: ServiceProxy, useValue: spy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ItemsListComponent);
    component = fixture.componentInstance;
    mockServiceProxy = TestBed.inject(ServiceProxy) as jasmine.SpyObj<ServiceProxy>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load items on init', () => {
    const mockResponse = {
      successed: true,
      data: [/* mock data */],
      totalCount: 1
    };
    mockServiceProxy.itemsList.and.returnValue(of(mockResponse));

    component.ngOnInit();

    expect(mockServiceProxy.itemsList).toHaveBeenCalled();
    expect(component.items.length).toBe(1);
  });

  it('should handle search', () => {
    const searchEvent = { target: { value: 'test' } };
    spyOn(component, 'loadItems');

    component.onSearch(searchEvent);

    expect(component.loadItems).toHaveBeenCalledWith('test');
  });

  it('should handle delete confirmation', () => {
    const mockItem = { id: 1, canDelete: true } as ItemDisplay;
    spyOn(window, 'confirm').and.returnValue(true);
    mockServiceProxy.cancelItem.and.returnValue(of({ successed: true }));

    component.deleteItem(mockItem);

    expect(mockServiceProxy.cancelItem).toHaveBeenCalledWith(1);
  });
});
```

### Integration Testing

- **API Integration**: Test real API calls with proper error handling
- **User Interactions**: Test search, filter, pagination, and CRUD operations
- **State Management**: Verify loading, error, and empty states
- **Responsive Design**: Test on different screen sizes

## 📋 Task Breakdown Standards

### Required Task Structure

For any list-based user story, create tasks following this structure:

#### 1. **Setup and Architecture** (Estimated: 20 minutes)
- Create component structure with proper imports
- Set up routing and navigation
- Configure breadcrumb and page header
- Implement basic component skeleton

#### 2. **API Integration** (Estimated: 30 minutes)
- Integrate with NSwag-generated service proxy
- Implement data loading with proper error handling
- Add loading states and error states
- Map API response to display format

#### 3. **Card Layout Implementation** (Estimated: 40 minutes)
- Create individual card components matching Figma design
- Implement responsive grid layout
- Add hover effects and transitions
- Ensure RTL/LTR support

#### 4. **Search and Filter Functionality** (Estimated: 30 minutes)
- Implement basic search functionality
- Create advanced search dialog component
- Add filter application logic
- Implement search state management

#### 5. **CRUD Operations** (Estimated: 25 minutes)
- Implement edit navigation
- Add delete functionality with confirmation
- Handle permissions and access control
- Add success/error notifications

#### 6. **Pagination Implementation** (Estimated: 20 minutes)
- Add pagination controls
- Implement page size selection
- Handle pagination state in URL
- Add total count display

#### 7. **Styling and Responsive Design** (Estimated: 30 minutes)
- Apply consistent CSS styling
- Implement responsive breakpoints
- Add loading/error/empty state styling
- Ensure cross-browser compatibility

#### 8. **Localization and Accessibility** (Estimated: 15 minutes)
- Add all required translation keys
- Implement proper ARIA labels
- Test keyboard navigation
- Verify screen reader compatibility

#### 9. **Testing and Quality Assurance** (Estimated: 30 minutes)
- Write unit tests for component logic
- Test API integration scenarios
- Verify responsive design on different devices
- Test localization in both languages

#### 10. **Documentation and Code Review** (Estimated: 20 minutes)
- Update component documentation
- Add code comments for complex logic
- Prepare code review checklist
- Update architectural documentation

**Total Estimated Time: 4 hours 20 minutes**

## ✅ Quality Checklist

Before marking any list view implementation as complete, verify:

### Functional Requirements
- [ ] **Card Layout**: Individual cards matching Figma design exactly
- [ ] **API Integration**: Real data from NSwag-generated proxies (no mock data)
- [ ] **Advanced Search**: Modal dialog with multiple filter options
- [ ] **Pagination**: Server-side pagination with proper controls
- [ ] **CRUD Operations**: Edit/Delete with proper permissions
- [ ] **Error Handling**: Comprehensive error states with retry options
- [ ] **Loading States**: Proper loading indicators during API calls

### Technical Requirements
- [ ] **TypeScript**: Proper typing with interfaces for all data
- [ ] **Reactive Forms**: If applicable, use Angular reactive forms
- [ ] **Standalone Components**: Use standalone component architecture
- [ ] **Dependency Injection**: Proper service injection and usage
- [ ] **Memory Management**: Proper subscription cleanup
- [ ] **Performance**: Efficient change detection and rendering

### Design Requirements
- [ ] **Figma Compliance**: Exact match with provided designs
- [ ] **Responsive Design**: Works on mobile, tablet, and desktop
- [ ] **RTL/LTR Support**: Proper layout for both Arabic and English
- [ ] **Hover Effects**: Smooth transitions and visual feedback
- [ ] **Color Scheme**: Consistent with JadwaUI design system
- [ ] **Typography**: Proper font sizes and weights

### Localization Requirements
- [ ] **Translation Keys**: All text properly translated
- [ ] **Arabic Support**: Proper RTL layout and Arabic text
- [ ] **English Support**: Proper LTR layout and English text
- [ ] **Date Formatting**: Locale-appropriate date formats
- [ ] **Number Formatting**: Locale-appropriate number formats

### Testing Requirements
- [ ] **Unit Tests**: Comprehensive component testing
- [ ] **Integration Tests**: API integration testing
- [ ] **E2E Tests**: End-to-end user workflow testing
- [ ] **Accessibility Tests**: Screen reader and keyboard navigation
- [ ] **Performance Tests**: Load time and rendering performance

## 🔄 Continuous Improvement

This document should be updated whenever:
- New patterns are discovered during implementation
- Figma designs introduce new requirements
- API structures change significantly
- User feedback suggests improvements
- Performance optimizations are identified

## 📚 Reference Implementation

The **Resolutions feature** serves as the reference implementation for all future list-based features. Developers should:

1. **Study the Implementation**: Review the resolutions component code thoroughly
2. **Copy the Patterns**: Use the same architectural patterns and code structure
3. **Adapt for Context**: Modify only the data-specific parts for your feature
4. **Maintain Consistency**: Follow the same naming conventions and file organization
5. **Update Documentation**: Add any new patterns discovered to this document

---

**Last Updated**: December 2024
**Version**: 1.0
**Status**: Active Standard
