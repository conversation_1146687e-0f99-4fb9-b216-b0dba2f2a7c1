{"name": "j<PERSON>wa", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build:prod": "ng build --configuration production", "build:test": "ng build --configuration test", "serve:prod": "ng serve --configuration production", "serve:test": "ng serve --configuration test", "serve:local": "ng serve", "watch": "ng build --watch --configuration development", "test": "ng test", "nswag": "nswag run"}, "private": true, "dependencies": {"@angular/animations": "^18.0.0", "@angular/cdk": "^18.2.14", "@angular/common": "^18.0.0", "@angular/compiler": "^18.0.0", "@angular/core": "^18.0.0", "@angular/forms": "^18.0.0", "@angular/localize": "^18.2.13", "@angular/material": "^18.2.5", "@angular/platform-browser": "^18.0.0", "@angular/platform-browser-dynamic": "^18.0.0", "@angular/router": "^18.0.0", "@microsoft/signalr": "^8.0.7", "@ng-bootstrap/ng-bootstrap": "^17.0.1", "@ng-select/ng-select": "^14.9.0", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@popperjs/core": "^2.11.8", "@types/luxon": "^3.6.2", "bootstrap": "^5.3.6", "bootstrap-icons": "^1.13.1", "docx": "^9.0.2", "file-saver": "^2.0.5", "hammerjs": "^2.0.8", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "jwt-decode": "^4.0.0", "luxon": "^3.6.1", "moment": "^2.30.1", "moment-hijri": "^3.0.0", "ngx-pipes": "^3.2.2", "ngx-toastr": "^19.0.0", "rxjs": "~7.8.0", "sweetalert2": "^11.22.0", "tslib": "^2.3.0", "xlsx": "^0.18.5", "zone.js": "~0.14.3", "@angular/fire": "^18.0.0", "firebase": "^10.8.1"}, "devDependencies": {"@angular-devkit/build-angular": "^18.0.7", "@angular/cli": "^18.2.19", "@angular/compiler-cli": "^18.0.0", "@types/file-saver": "^2.0.7", "@types/jasmine": "~5.1.0", "@types/moment-hijri": "^2.1.4", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "nswag": "^14.4.0", "typescript": "~5.4.2"}, "overrides": {}}