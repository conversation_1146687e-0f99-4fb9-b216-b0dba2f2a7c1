import { Routes, CanActivate } from '@angular/router';
import { AuthGuard } from '@core/guards/auth.guard';
import { Component } from '@angular/core';
import { FundDetailsComponent } from './features/investment-funds/components/fund-details/fund-details.component';

export const routes: Routes = [
  {
    path: 'auth',
    loadComponent: () =>
      import('./core/layout/components/auth-layout/auth-layout.component').then(
        (m) => m.AuthLayoutComponent
      ),
    children: [
      {
        path: 'login',
        loadComponent: () =>
          import('./features/auth/components/login/login.component').then(
            (m) => m.LoginComponent
          ),
      },
      { path: '**', redirectTo: 'login' },
    ],
  },
  {
    path: 'admin',
    loadComponent: () =>
      import(
        './core/layout/components/admin-layout/admin-layout.component'
      ).then((m) => m.AdminLayoutComponent),
    children: [
      {
        path: 'dashboard',
        loadComponent: () =>
          import('./features/dashboard/dashboard.component').then(
            (m) => m.DashboardComponent
          ),
        canActivate: [AuthGuard],
      },
      // { path: 'investment-funds', loadComponent: () => import('./features/investment-funds/components/funds-list/investment-funds.component').then(m => m.InvestmentFundsComponent) },
      {
        path: 'fund-strategies',
        loadComponent: () =>
          import('./features/fund-strategies/fund-strategies.component').then(
            (m) => m.FundStrategiesComponent
          ),
        canActivate: [AuthGuard],
      },
      {
        path: 'investment-funds',
        loadChildren: () =>
          import('./features/investment-funds/investment-funds.routes').then(
            (m) => m.INVESTMENT_FUNDS_ROUTES
          ),
        canActivate: [AuthGuard],
      },
      {
        path: 'user-management',
        loadChildren: () =>
          import('./features/user-management/user-management.routes').then(
            (m) => m.USER_MANAGEMENT_ROUTES
          ),
        canActivate: [AuthGuard],
      },

      { path: '**', redirectTo: '/login' },
    ],
  },
  {
    path: 'not-found',
    loadComponent: () =>
      import('./shared/components/not-found/not-found.component').then(
        (m) => m.NotFoundComponent
      ),
  },

  { path: '**', redirectTo: 'auth' },

  {
    path: 'home',
    loadComponent: () =>
      import(
        './core/layout/components/admin-layout/admin-layout.component'
      ).then((m) => m.AdminLayoutComponent),
    data: { breadcrumb: 'Home' },
    children: [
      // {
      //   path: 'dashboard',
      //   loadComponent: () =>
      //     import('./features/dashboard/dashboard.component').then(m => m.DashboardComponent),
      //   data: { breadcrumb: 'Dashboard' }
      // },
      // {
      //   path: 'reports',
      //   loadComponent: () =>
      //     import('./features/reports/reports.component').then(m => m.ReportsComponent),
      //   data: { breadcrumb: 'Reports' }
      // }
    ],
  },
];
