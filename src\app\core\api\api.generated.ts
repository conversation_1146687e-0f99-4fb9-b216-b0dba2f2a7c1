//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v14.4.0.0 (NJsonSchema v11.3.2.0 (Newtonsoft.Json v13.0.0.0)) (http://NSwag.org)
// </auto-generated>
//----------------------

/* tslint:disable */
/* eslint-disable */
// ReSharper disable InconsistentNaming

import { mergeMap as _observableMergeMap, catchError as _observableCatch } from 'rxjs/operators';
import { Observable, throwError as _observableThrow, of as _observableOf } from 'rxjs';
import { Injectable, Inject, Optional, InjectionToken } from '@angular/core';
import { HttpClient, HttpHeaders, HttpResponse, HttpResponseBase } from '@angular/common/http';

import { DateTime, Duration } from "luxon";

export const API_BASE_URL = new InjectionToken<string>('API_BASE_URL');

@Injectable()
export class AuthenticationServiceProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    signIn(body: SignInCommand | undefined): Observable<JwtAuthResponseBaseResponse> {
        let url_ = this.baseUrl + "/api/Users/<USER>/Sign-In";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "application/json"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processSignIn(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processSignIn(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<JwtAuthResponseBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<JwtAuthResponseBaseResponse>;
        }));
    }

    protected processSignIn(response: HttpResponseBase): Observable<JwtAuthResponseBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = JwtAuthResponseBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<JwtAuthResponseBaseResponse>(null as any);
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    updateFCMToken(body: UpdateFCMTokenCommand | undefined): Observable<StringBaseResponse> {
        let url_ = this.baseUrl + "/api/Users/<USER>/Update-FCMToken";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "application/json"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processUpdateFCMToken(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processUpdateFCMToken(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<StringBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<StringBaseResponse>;
        }));
    }

    protected processUpdateFCMToken(response: HttpResponseBase): Observable<StringBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = StringBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<StringBaseResponse>(null as any);
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    refreshToken(body: RefreshTokenCommand | undefined): Observable<JwtAuthResponseBaseResponse> {
        let url_ = this.baseUrl + "/api/Users/<USER>/Refresh-Token";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "application/json"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processRefreshToken(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processRefreshToken(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<JwtAuthResponseBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<JwtAuthResponseBaseResponse>;
        }));
    }

    protected processRefreshToken(response: HttpResponseBase): Observable<JwtAuthResponseBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = JwtAuthResponseBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<JwtAuthResponseBaseResponse>(null as any);
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    isValid_Token(body: AccessTokenQuery | undefined): Observable<StringBaseResponse> {
        let url_ = this.baseUrl + "/api/Users/<USER>/Is-Valid_Token";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processIsValid_Token(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processIsValid_Token(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<StringBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<StringBaseResponse>;
        }));
    }

    protected processIsValid_Token(response: HttpResponseBase): Observable<StringBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = StringBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<StringBaseResponse>(null as any);
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    signOut(body: SignOutCommand | undefined): Observable<StringBaseResponse> {
        let url_ = this.baseUrl + "/api/Users/<USER>/Sign-Out";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "application/json"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processSignOut(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processSignOut(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<StringBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<StringBaseResponse>;
        }));
    }

    protected processSignOut(response: HttpResponseBase): Observable<StringBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = StringBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<StringBaseResponse>(null as any);
    }
}

@Injectable()
export class AuthorzationServiceProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    roleList(): Observable<void> {
        let url_ = this.baseUrl + "/api/Users/<USER>/RoleList";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processRoleList(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processRoleList(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processRoleList(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<void>(null as any);
    }

    calimList(): Observable<void> {
        let url_ = this.baseUrl + "/api/Users/<USER>/CalimList";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCalimList(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCalimList(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processCalimList(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<void>(null as any);
    }

    create(body: AddRoleCommand | undefined): Observable<void> {
        let url_ = this.baseUrl + "/api/Users/<USER>/Create";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCreate(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCreate(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processCreate(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<void>(null as any);
    }

    update(body: EditRoleCommand | undefined): Observable<void> {
        let url_ = this.baseUrl + "/api/Users/<USER>/Update";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
            })
        };

        return this.http.request("put", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processUpdate(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processUpdate(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processUpdate(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<void>(null as any);
    }
}

@Injectable()
export class UsersServiceProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    authorzationGet(id: number | undefined): Observable<void> {
        let url_ = this.baseUrl + "/api/Users/<USER>";
        if (id === null)
            throw new Error("The parameter 'id' cannot be null.");
        else if (id !== undefined)
            url_ += "id=" + encodeURIComponent("" + id) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processAuthorzationGet(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processAuthorzationGet(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processAuthorzationGet(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<void>(null as any);
    }

    authorzationDelete(id: number | undefined): Observable<void> {
        let url_ = this.baseUrl + "/api/Users/<USER>";
        if (id === null)
            throw new Error("The parameter 'id' cannot be null.");
        else if (id !== undefined)
            url_ += "id=" + encodeURIComponent("" + id) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
            })
        };

        return this.http.request("delete", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processAuthorzationDelete(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processAuthorzationDelete(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processAuthorzationDelete(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<void>(null as any);
    }
}

@Injectable()
export class BoardMembersServiceProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @param fundId (optional) 
     * @param pageNumber (optional) 
     * @param pageSize (optional) 
     * @param search (optional) 
     * @param orderBy (optional) 
     * @return OK
     */
    boardMembersList(fundId: number | undefined, pageNumber: number | undefined, pageSize: number | undefined, search: string | undefined, orderBy: string | undefined): Observable<BoardMemberResponsePaginatedResult> {
        let url_ = this.baseUrl + "/api/BoardMembers/BoardMembersList?";
        if (fundId === null)
            throw new Error("The parameter 'fundId' cannot be null.");
        else if (fundId !== undefined)
            url_ += "FundId=" + encodeURIComponent("" + fundId) + "&";
        if (pageNumber === null)
            throw new Error("The parameter 'pageNumber' cannot be null.");
        else if (pageNumber !== undefined)
            url_ += "PageNumber=" + encodeURIComponent("" + pageNumber) + "&";
        if (pageSize === null)
            throw new Error("The parameter 'pageSize' cannot be null.");
        else if (pageSize !== undefined)
            url_ += "PageSize=" + encodeURIComponent("" + pageSize) + "&";
        if (search === null)
            throw new Error("The parameter 'search' cannot be null.");
        else if (search !== undefined)
            url_ += "Search=" + encodeURIComponent("" + search) + "&";
        if (orderBy === null)
            throw new Error("The parameter 'orderBy' cannot be null.");
        else if (orderBy !== undefined)
            url_ += "OrderBy=" + encodeURIComponent("" + orderBy) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processBoardMembersList(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processBoardMembersList(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<BoardMemberResponsePaginatedResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<BoardMemberResponsePaginatedResult>;
        }));
    }

    protected processBoardMembersList(response: HttpResponseBase): Observable<BoardMemberResponsePaginatedResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = BoardMemberResponsePaginatedResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<BoardMemberResponsePaginatedResult>(null as any);
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    addBoardMember(body: AddBoardMemberCommand | undefined): Observable<StringBaseResponse> {
        let url_ = this.baseUrl + "/api/BoardMembers/AddBoardMember";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "application/json"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processAddBoardMember(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processAddBoardMember(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<StringBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<StringBaseResponse>;
        }));
    }

    protected processAddBoardMember(response: HttpResponseBase): Observable<StringBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = StringBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<StringBaseResponse>(null as any);
    }
}

@Injectable()
export class FileManagmentServiceProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    uploadFile(fileName: string | undefined, moduleId: number | undefined, body: Body | undefined, file: FileParameter | undefined): Observable<void> {
        let url_ = this.baseUrl + "/api/Users/<USER>/UploadFile?";
        if (fileName === null)
            throw new Error("The parameter 'fileName' cannot be null.");
        else if (fileName !== undefined)
            url_ += "FileName=" + encodeURIComponent("" + fileName) + "&";
        if (moduleId === null)
            throw new Error("The parameter 'moduleId' cannot be null.");
        else if (moduleId !== undefined)
            url_ += "ModuleId=" + encodeURIComponent("" + moduleId) + "&";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = new FormData();
        if (file === null || file === undefined)
            throw new Error("The parameter 'file' cannot be null.");
        else
            content_.append("File", file.data, file.fileName ? file.fileName : "File");

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "multipart/form-data",
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processUploadFile(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processUploadFile(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processUploadFile(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<void>(null as any);
    }

    downloadFile(body: DownloadAttachmentCommand | undefined): Observable<void> {
        let url_ = this.baseUrl + "/api/Users/<USER>/DownloadFile";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processDownloadFile(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processDownloadFile(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processDownloadFile(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<void>(null as any);
    }
}

@Injectable()
export class FundsServiceProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @param creationDateFrom (optional) 
     * @param creationDateTo (optional) 
     * @param strategyId (optional) 
     * @param pageNumber (optional) 
     * @param pageSize (optional) 
     * @param search (optional) 
     * @param orderBy (optional) 
     * @return OK
     */
    fundsList(creationDateFrom: DateTime | undefined, creationDateTo: DateTime | undefined, strategyId: number | undefined, pageNumber: number | undefined, pageSize: number | undefined, search: string | undefined, orderBy: string | undefined): Observable<FundGroupDtoPaginatedResult> {
        let url_ = this.baseUrl + "/api/Funds/FundsList?";
        if (creationDateFrom === null)
            throw new Error("The parameter 'creationDateFrom' cannot be null.");
        else if (creationDateFrom !== undefined)
            url_ += "CreationDateFrom=" + encodeURIComponent(creationDateFrom ? "" + creationDateFrom.toString() : "") + "&";
        if (creationDateTo === null)
            throw new Error("The parameter 'creationDateTo' cannot be null.");
        else if (creationDateTo !== undefined)
            url_ += "CreationDateTo=" + encodeURIComponent(creationDateTo ? "" + creationDateTo.toString() : "") + "&";
        if (strategyId === null)
            throw new Error("The parameter 'strategyId' cannot be null.");
        else if (strategyId !== undefined)
            url_ += "StrategyId=" + encodeURIComponent("" + strategyId) + "&";
        if (pageNumber === null)
            throw new Error("The parameter 'pageNumber' cannot be null.");
        else if (pageNumber !== undefined)
            url_ += "PageNumber=" + encodeURIComponent("" + pageNumber) + "&";
        if (pageSize === null)
            throw new Error("The parameter 'pageSize' cannot be null.");
        else if (pageSize !== undefined)
            url_ += "PageSize=" + encodeURIComponent("" + pageSize) + "&";
        if (search === null)
            throw new Error("The parameter 'search' cannot be null.");
        else if (search !== undefined)
            url_ += "Search=" + encodeURIComponent("" + search) + "&";
        if (orderBy === null)
            throw new Error("The parameter 'orderBy' cannot be null.");
        else if (orderBy !== undefined)
            url_ += "OrderBy=" + encodeURIComponent("" + orderBy) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processFundsList(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processFundsList(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<FundGroupDtoPaginatedResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<FundGroupDtoPaginatedResult>;
        }));
    }

    protected processFundsList(response: HttpResponseBase): Observable<FundGroupDtoPaginatedResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = FundGroupDtoPaginatedResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<FundGroupDtoPaginatedResult>(null as any);
    }

    /**
     * @param id (optional) 
     * @return OK
     */
    getFundsById(id: number | undefined): Observable<EditFundResponseBaseResponse> {
        let url_ = this.baseUrl + "/api/Funds/GetFundsById?";
        if (id === null)
            throw new Error("The parameter 'id' cannot be null.");
        else if (id !== undefined)
            url_ += "id=" + encodeURIComponent("" + id) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetFundsById(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetFundsById(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<EditFundResponseBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<EditFundResponseBaseResponse>;
        }));
    }

    protected processGetFundsById(response: HttpResponseBase): Observable<EditFundResponseBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = EditFundResponseBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<EditFundResponseBaseResponse>(null as any);
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    addFund(body: AddFundCommand | undefined): Observable<StringBaseResponse> {
        let url_ = this.baseUrl + "/api/Funds/AddFund";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "application/json"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processAddFund(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processAddFund(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<StringBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<StringBaseResponse>;
        }));
    }

    protected processAddFund(response: HttpResponseBase): Observable<StringBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = StringBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<StringBaseResponse>(null as any);
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    editFund(body: SaveFundCommand | undefined): Observable<StringBaseResponse> {
        let url_ = this.baseUrl + "/api/Funds/EditFund";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "application/json"
            })
        };

        return this.http.request("put", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processEditFund(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processEditFund(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<StringBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<StringBaseResponse>;
        }));
    }

    protected processEditFund(response: HttpResponseBase): Observable<StringBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = StringBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<StringBaseResponse>(null as any);
    }

    /**
     * @param id (optional) 
     * @return OK
     */
    getFunDetails(id: number | undefined): Observable<FundDetailsResponseBaseResponse> {
        let url_ = this.baseUrl + "/api/Funds/GetFunDetails?";
        if (id === null)
            throw new Error("The parameter 'id' cannot be null.");
        else if (id !== undefined)
            url_ += "id=" + encodeURIComponent("" + id) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetFunDetails(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetFunDetails(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<FundDetailsResponseBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<FundDetailsResponseBaseResponse>;
        }));
    }

    protected processGetFunDetails(response: HttpResponseBase): Observable<FundDetailsResponseBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = FundDetailsResponseBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<FundDetailsResponseBaseResponse>(null as any);
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    editFundExitDate(body: EditExitDateCommand | undefined): Observable<StringBaseResponse> {
        let url_ = this.baseUrl + "/api/Funds/EditFundExitDate";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "application/json"
            })
        };

        return this.http.request("put", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processEditFundExitDate(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processEditFundExitDate(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<StringBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<StringBaseResponse>;
        }));
    }

    protected processEditFundExitDate(response: HttpResponseBase): Observable<StringBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = StringBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<StringBaseResponse>(null as any);
    }

    fundActivityCounters(fundId: number): Observable<void> {
        let url_ = this.baseUrl + "/api/Funds/FundActivityCounters";
        if (fundId === undefined || fundId === null)
            throw new Error("The parameter 'fundId' must be defined.");
        url_ = url_.replace("{fundId}", encodeURIComponent("" + fundId));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processFundActivityCounters(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processFundActivityCounters(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processFundActivityCounters(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<void>(null as any);
    }

    /**
     * @return OK
     */
    voteType(fundId: number): Observable<FundVoteTypeDtoBaseResponse> {
        let url_ = this.baseUrl + "/api/Funds/VoteType/{fundId}";
        if (fundId === undefined || fundId === null)
            throw new Error("The parameter 'fundId' must be defined.");
        url_ = url_.replace("{fundId}", encodeURIComponent("" + fundId));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processVoteType(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processVoteType(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<FundVoteTypeDtoBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<FundVoteTypeDtoBaseResponse>;
        }));
    }

    protected processVoteType(response: HttpResponseBase): Observable<FundVoteTypeDtoBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = FundVoteTypeDtoBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<FundVoteTypeDtoBaseResponse>(null as any);
    }

    /**
     * @return OK
     */
    initiationDate(fundId: number): Observable<FundInitiationDateDtoBaseResponse> {
        let url_ = this.baseUrl + "/api/Funds/InitiationDate/{fundId}";
        if (fundId === undefined || fundId === null)
            throw new Error("The parameter 'fundId' must be defined.");
        url_ = url_.replace("{fundId}", encodeURIComponent("" + fundId));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processInitiationDate(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processInitiationDate(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<FundInitiationDateDtoBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<FundInitiationDateDtoBaseResponse>;
        }));
    }

    protected processInitiationDate(response: HttpResponseBase): Observable<FundInitiationDateDtoBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = FundInitiationDateDtoBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<FundInitiationDateDtoBaseResponse>(null as any);
    }
}

@Injectable()
export class NotificationServiceProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @param isRead (optional) 
     * @param pageNumber (optional) 
     * @param pageSize (optional) 
     * @param search (optional) 
     * @param orderBy (optional) 
     * @return OK
     */
    notitficationList(isRead: boolean | undefined, pageNumber: number | undefined, pageSize: number | undefined, search: string | undefined, orderBy: string | undefined): Observable<NotificationDtoPaginatedResult> {
        let url_ = this.baseUrl + "/api/Notification/NotitficationList?";
        if (isRead === null)
            throw new Error("The parameter 'isRead' cannot be null.");
        else if (isRead !== undefined)
            url_ += "IsRead=" + encodeURIComponent("" + isRead) + "&";
        if (pageNumber === null)
            throw new Error("The parameter 'pageNumber' cannot be null.");
        else if (pageNumber !== undefined)
            url_ += "PageNumber=" + encodeURIComponent("" + pageNumber) + "&";
        if (pageSize === null)
            throw new Error("The parameter 'pageSize' cannot be null.");
        else if (pageSize !== undefined)
            url_ += "PageSize=" + encodeURIComponent("" + pageSize) + "&";
        if (search === null)
            throw new Error("The parameter 'search' cannot be null.");
        else if (search !== undefined)
            url_ += "Search=" + encodeURIComponent("" + search) + "&";
        if (orderBy === null)
            throw new Error("The parameter 'orderBy' cannot be null.");
        else if (orderBy !== undefined)
            url_ += "OrderBy=" + encodeURIComponent("" + orderBy) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processNotitficationList(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processNotitficationList(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<NotificationDtoPaginatedResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<NotificationDtoPaginatedResult>;
        }));
    }

    protected processNotitficationList(response: HttpResponseBase): Observable<NotificationDtoPaginatedResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = NotificationDtoPaginatedResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<NotificationDtoPaginatedResult>(null as any);
    }

    /**
     * @param pageNumber (optional) 
     * @param pageSize (optional) 
     * @param search (optional) 
     * @param orderBy (optional) 
     * @return OK
     */
    unReadedNotificationList(pageNumber: number | undefined, pageSize: number | undefined, search: string | undefined, orderBy: string | undefined): Observable<NotificationDtoPaginatedResult> {
        let url_ = this.baseUrl + "/api/Notification/UnReadedNotificationList?";
        if (pageNumber === null)
            throw new Error("The parameter 'pageNumber' cannot be null.");
        else if (pageNumber !== undefined)
            url_ += "PageNumber=" + encodeURIComponent("" + pageNumber) + "&";
        if (pageSize === null)
            throw new Error("The parameter 'pageSize' cannot be null.");
        else if (pageSize !== undefined)
            url_ += "PageSize=" + encodeURIComponent("" + pageSize) + "&";
        if (search === null)
            throw new Error("The parameter 'search' cannot be null.");
        else if (search !== undefined)
            url_ += "Search=" + encodeURIComponent("" + search) + "&";
        if (orderBy === null)
            throw new Error("The parameter 'orderBy' cannot be null.");
        else if (orderBy !== undefined)
            url_ += "OrderBy=" + encodeURIComponent("" + orderBy) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processUnReadedNotificationList(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processUnReadedNotificationList(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<NotificationDtoPaginatedResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<NotificationDtoPaginatedResult>;
        }));
    }

    protected processUnReadedNotificationList(response: HttpResponseBase): Observable<NotificationDtoPaginatedResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = NotificationDtoPaginatedResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<NotificationDtoPaginatedResult>(null as any);
    }
}

@Injectable()
export class ResolutionsServiceProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @param fundId (optional) 
     * @param status (optional) 
     * @param resolutionTypeId (optional) 
     * @param fromDate (optional) 
     * @param toDate (optional) 
     * @param createdBy (optional) 
     * @param pageNumber (optional) 
     * @param pageSize (optional) 
     * @param search (optional) 
     * @param orderBy (optional) 
     * @return OK
     */
    resolutionsList(fundId: number | undefined, status: ResolutionStatusEnum | undefined, resolutionTypeId: number | undefined, fromDate: DateTime | undefined, toDate: DateTime | undefined, createdBy: number | undefined, pageNumber: number | undefined, pageSize: number | undefined, search: string | undefined, orderBy: string | undefined): Observable<SingleResolutionResponsePaginatedResult> {
        let url_ = this.baseUrl + "/api/Resolutions/ResolutionsList?";
        if (fundId === null)
            throw new Error("The parameter 'fundId' cannot be null.");
        else if (fundId !== undefined)
            url_ += "FundId=" + encodeURIComponent("" + fundId) + "&";
        if (status === null)
            throw new Error("The parameter 'status' cannot be null.");
        else if (status !== undefined)
            url_ += "Status=" + encodeURIComponent("" + status) + "&";
        if (resolutionTypeId === null)
            throw new Error("The parameter 'resolutionTypeId' cannot be null.");
        else if (resolutionTypeId !== undefined)
            url_ += "ResolutionTypeId=" + encodeURIComponent("" + resolutionTypeId) + "&";
        if (fromDate === null)
            throw new Error("The parameter 'fromDate' cannot be null.");
        else if (fromDate !== undefined)
            url_ += "FromDate=" + encodeURIComponent(fromDate ? "" + fromDate.toString() : "") + "&";
        if (toDate === null)
            throw new Error("The parameter 'toDate' cannot be null.");
        else if (toDate !== undefined)
            url_ += "ToDate=" + encodeURIComponent(toDate ? "" + toDate.toString() : "") + "&";
        if (createdBy === null)
            throw new Error("The parameter 'createdBy' cannot be null.");
        else if (createdBy !== undefined)
            url_ += "CreatedBy=" + encodeURIComponent("" + createdBy) + "&";
        if (pageNumber === null)
            throw new Error("The parameter 'pageNumber' cannot be null.");
        else if (pageNumber !== undefined)
            url_ += "PageNumber=" + encodeURIComponent("" + pageNumber) + "&";
        if (pageSize === null)
            throw new Error("The parameter 'pageSize' cannot be null.");
        else if (pageSize !== undefined)
            url_ += "PageSize=" + encodeURIComponent("" + pageSize) + "&";
        if (search === null)
            throw new Error("The parameter 'search' cannot be null.");
        else if (search !== undefined)
            url_ += "Search=" + encodeURIComponent("" + search) + "&";
        if (orderBy === null)
            throw new Error("The parameter 'orderBy' cannot be null.");
        else if (orderBy !== undefined)
            url_ += "OrderBy=" + encodeURIComponent("" + orderBy) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processResolutionsList(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processResolutionsList(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<SingleResolutionResponsePaginatedResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<SingleResolutionResponsePaginatedResult>;
        }));
    }

    protected processResolutionsList(response: HttpResponseBase): Observable<SingleResolutionResponsePaginatedResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = SingleResolutionResponsePaginatedResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<SingleResolutionResponsePaginatedResult>(null as any);
    }

    /**
     * @return OK
     */
    getResolutionById(id: number): Observable<SingleResolutionResponseBaseResponse> {
        let url_ = this.baseUrl + "/api/Resolutions/GetResolutionById/{id}";
        if (id === undefined || id === null)
            throw new Error("The parameter 'id' must be defined.");
        url_ = url_.replace("{id}", encodeURIComponent("" + id));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetResolutionById(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetResolutionById(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<SingleResolutionResponseBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<SingleResolutionResponseBaseResponse>;
        }));
    }

    protected processGetResolutionById(response: HttpResponseBase): Observable<SingleResolutionResponseBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = SingleResolutionResponseBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<SingleResolutionResponseBaseResponse>(null as any);
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    addResolution(body: AddResolutionCommand | undefined): Observable<StringBaseResponse> {
        let url_ = this.baseUrl + "/api/Resolutions/AddResolution";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "application/json"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processAddResolution(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processAddResolution(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<StringBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<StringBaseResponse>;
        }));
    }

    protected processAddResolution(response: HttpResponseBase): Observable<StringBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = StringBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 422) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result422: any = null;
            let resultData422 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result422 = StringBaseResponse.fromJS(resultData422);
            return throwException("Unprocessable Content", status, _responseText, _headers, result422);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<StringBaseResponse>(null as any);
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    editResolution(body: EditResolutionCommand | undefined): Observable<StringBaseResponse> {
        let url_ = this.baseUrl + "/api/Resolutions/EditResolution";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "application/json"
            })
        };

        return this.http.request("put", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processEditResolution(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processEditResolution(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<StringBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<StringBaseResponse>;
        }));
    }

    protected processEditResolution(response: HttpResponseBase): Observable<StringBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = StringBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 422) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result422: any = null;
            let resultData422 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result422 = StringBaseResponse.fromJS(resultData422);
            return throwException("Unprocessable Content", status, _responseText, _headers, result422);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<StringBaseResponse>(null as any);
    }

    /**
     * @return OK
     */
    deleteResolution(id: number): Observable<StringBaseResponse> {
        let url_ = this.baseUrl + "/api/Resolutions/DeleteResolution/{id}";
        if (id === undefined || id === null)
            throw new Error("The parameter 'id' must be defined.");
        url_ = url_.replace("{id}", encodeURIComponent("" + id));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("delete", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processDeleteResolution(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processDeleteResolution(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<StringBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<StringBaseResponse>;
        }));
    }

    protected processDeleteResolution(response: HttpResponseBase): Observable<StringBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = StringBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<StringBaseResponse>(null as any);
    }

    /**
     * @return OK
     */
    cancelResolution(id: number): Observable<StringBaseResponse> {
        let url_ = this.baseUrl + "/api/Resolutions/CancelResolution/{id}";
        if (id === undefined || id === null)
            throw new Error("The parameter 'id' must be defined.");
        url_ = url_.replace("{id}", encodeURIComponent("" + id));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("patch", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCancelResolution(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCancelResolution(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<StringBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<StringBaseResponse>;
        }));
    }

    protected processCancelResolution(response: HttpResponseBase): Observable<StringBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = StringBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<StringBaseResponse>(null as any);
    }

    /**
     * @return OK
     */
    confirmResolution(id: number): Observable<StringBaseResponse> {
        let url_ = this.baseUrl + "/api/Resolutions/ConfirmResolution/{id}";
        if (id === undefined || id === null)
            throw new Error("The parameter 'id' must be defined.");
        url_ = url_.replace("{id}", encodeURIComponent("" + id));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("patch", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processConfirmResolution(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processConfirmResolution(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<StringBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<StringBaseResponse>;
        }));
    }

    protected processConfirmResolution(response: HttpResponseBase): Observable<StringBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = StringBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<StringBaseResponse>(null as any);
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    rejectResolution(id: number, body: RejectResolutionCommand | undefined): Observable<StringBaseResponse> {
        let url_ = this.baseUrl + "/api/Resolutions/RejectResolution/{id}";
        if (id === undefined || id === null)
            throw new Error("The parameter 'id' must be defined.");
        url_ = url_.replace("{id}", encodeURIComponent("" + id));
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "application/json"
            })
        };

        return this.http.request("patch", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processRejectResolution(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processRejectResolution(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<StringBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<StringBaseResponse>;
        }));
    }

    protected processRejectResolution(response: HttpResponseBase): Observable<StringBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = StringBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<StringBaseResponse>(null as any);
    }

    /**
     * @return OK
     */
    sendToVote(id: number): Observable<StringBaseResponse> {
        let url_ = this.baseUrl + "/api/Resolutions/SendToVote/{id}";
        if (id === undefined || id === null)
            throw new Error("The parameter 'id' must be defined.");
        url_ = url_.replace("{id}", encodeURIComponent("" + id));
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("patch", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processSendToVote(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processSendToVote(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<StringBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<StringBaseResponse>;
        }));
    }

    protected processSendToVote(response: HttpResponseBase): Observable<StringBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = StringBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<StringBaseResponse>(null as any);
    }

    /**
     * @return OK
     */
    activeTypes(): Observable<ResolutionTypeDtoIEnumerableBaseResponse> {
        let url_ = this.baseUrl + "/api/Resolutions/ActiveTypes";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processActiveTypes(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processActiveTypes(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResolutionTypeDtoIEnumerableBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResolutionTypeDtoIEnumerableBaseResponse>;
        }));
    }

    protected processActiveTypes(response: HttpResponseBase): Observable<ResolutionTypeDtoIEnumerableBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResolutionTypeDtoIEnumerableBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<ResolutionTypeDtoIEnumerableBaseResponse>(null as any);
    }

    /**
     * @return OK
     */
    statuses(): Observable<ResolutionStatusDtoListBaseResponse> {
        let url_ = this.baseUrl + "/api/Resolutions/Statuses";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processStatuses(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processStatuses(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResolutionStatusDtoListBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResolutionStatusDtoListBaseResponse>;
        }));
    }

    protected processStatuses(response: HttpResponseBase): Observable<ResolutionStatusDtoListBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResolutionStatusDtoListBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<ResolutionStatusDtoListBaseResponse>(null as any);
    }
}

@Injectable()
export class TypesServiceProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @return OK
     */
    all(): Observable<ResolutionTypeDtoIEnumerableBaseResponse> {
        let url_ = this.baseUrl + "/api/Resolutions/Types/All";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processAll(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processAll(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<ResolutionTypeDtoIEnumerableBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<ResolutionTypeDtoIEnumerableBaseResponse>;
        }));
    }

    protected processAll(response: HttpResponseBase): Observable<ResolutionTypeDtoIEnumerableBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = ResolutionTypeDtoIEnumerableBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<ResolutionTypeDtoIEnumerableBaseResponse>(null as any);
    }
}

@Injectable()
export class StrategiesServiceProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    createStrategy(body: StrategyDto | undefined): Observable<StringBaseResponse> {
        let url_ = this.baseUrl + "/api/Strategies/CreateStrategy";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "application/json"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCreateStrategy(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCreateStrategy(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<StringBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<StringBaseResponse>;
        }));
    }

    protected processCreateStrategy(response: HttpResponseBase): Observable<StringBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = StringBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 422) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result422: any = null;
            let resultData422 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result422 = StringBaseResponse.fromJS(resultData422);
            return throwException("Unprocessable Content", status, _responseText, _headers, result422);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<StringBaseResponse>(null as any);
    }

    /**
     * @param body (optional) 
     * @return OK
     */
    updateStrategy(body: StrategyDto | undefined): Observable<StringBaseResponse> {
        let url_ = this.baseUrl + "/api/Strategies/UpdateStrategy";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "application/json"
            })
        };

        return this.http.request("put", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processUpdateStrategy(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processUpdateStrategy(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<StringBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<StringBaseResponse>;
        }));
    }

    protected processUpdateStrategy(response: HttpResponseBase): Observable<StringBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = StringBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status === 422) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result422: any = null;
            let resultData422 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result422 = StringBaseResponse.fromJS(resultData422);
            return throwException("Unprocessable Content", status, _responseText, _headers, result422);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<StringBaseResponse>(null as any);
    }

    /**
     * @param id (optional) 
     * @return OK
     */
    getStrategyById(id: number | undefined): Observable<StrategyDtoBaseResponse> {
        let url_ = this.baseUrl + "/api/Strategies/GetStrategyById?";
        if (id === null)
            throw new Error("The parameter 'id' cannot be null.");
        else if (id !== undefined)
            url_ += "id=" + encodeURIComponent("" + id) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetStrategyById(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetStrategyById(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<StrategyDtoBaseResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<StrategyDtoBaseResponse>;
        }));
    }

    protected processGetStrategyById(response: HttpResponseBase): Observable<StrategyDtoBaseResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = StrategyDtoBaseResponse.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<StrategyDtoBaseResponse>(null as any);
    }

    /**
     * @param pageNumber (optional) 
     * @param pageSize (optional) 
     * @param search (optional) 
     * @param orderBy (optional) 
     * @return OK
     */
    strategyList(pageNumber: number | undefined, pageSize: number | undefined, search: string | undefined, orderBy: string | undefined): Observable<StrategyDtoPaginatedResult> {
        let url_ = this.baseUrl + "/api/Strategies/StrategyList?";
        if (pageNumber === null)
            throw new Error("The parameter 'pageNumber' cannot be null.");
        else if (pageNumber !== undefined)
            url_ += "PageNumber=" + encodeURIComponent("" + pageNumber) + "&";
        if (pageSize === null)
            throw new Error("The parameter 'pageSize' cannot be null.");
        else if (pageSize !== undefined)
            url_ += "PageSize=" + encodeURIComponent("" + pageSize) + "&";
        if (search === null)
            throw new Error("The parameter 'search' cannot be null.");
        else if (search !== undefined)
            url_ += "Search=" + encodeURIComponent("" + search) + "&";
        if (orderBy === null)
            throw new Error("The parameter 'orderBy' cannot be null.");
        else if (orderBy !== undefined)
            url_ += "OrderBy=" + encodeURIComponent("" + orderBy) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processStrategyList(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processStrategyList(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<StrategyDtoPaginatedResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<StrategyDtoPaginatedResult>;
        }));
    }

    protected processStrategyList(response: HttpResponseBase): Observable<StrategyDtoPaginatedResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = StrategyDtoPaginatedResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<StrategyDtoPaginatedResult>(null as any);
    }
}

@Injectable()
export class SystemConfigurationServiceProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    getConfigs(): Observable<void> {
        let url_ = this.baseUrl + "/api/SystemConfiguration/GetConfigs";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetConfigs(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetConfigs(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processGetConfigs(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<void>(null as any);
    }
}

@Injectable()
export class UserManagementServiceProxy {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    userList(pageNumber: number | undefined, pageSize: number | undefined, search: string | undefined, orderBy: string | undefined): Observable<void> {
        let url_ = this.baseUrl + "/api/Users/<USER>/UserList?";
        if (pageNumber === null)
            throw new Error("The parameter 'pageNumber' cannot be null.");
        else if (pageNumber !== undefined)
            url_ += "PageNumber=" + encodeURIComponent("" + pageNumber) + "&";
        if (pageSize === null)
            throw new Error("The parameter 'pageSize' cannot be null.");
        else if (pageSize !== undefined)
            url_ += "PageSize=" + encodeURIComponent("" + pageSize) + "&";
        if (search === null)
            throw new Error("The parameter 'search' cannot be null.");
        else if (search !== undefined)
            url_ += "Search=" + encodeURIComponent("" + search) + "&";
        if (orderBy === null)
            throw new Error("The parameter 'orderBy' cannot be null.");
        else if (orderBy !== undefined)
            url_ += "OrderBy=" + encodeURIComponent("" + orderBy) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processUserList(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processUserList(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processUserList(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<void>(null as any);
    }

    getUserById(id: number | undefined): Observable<void> {
        let url_ = this.baseUrl + "/api/Users/<USER>/GetUserById?";
        if (id === null)
            throw new Error("The parameter 'id' cannot be null.");
        else if (id !== undefined)
            url_ += "id=" + encodeURIComponent("" + id) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetUserById(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetUserById(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processGetUserById(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<void>(null as any);
    }

    addUser(body: AddUserCommand | undefined): Observable<void> {
        let url_ = this.baseUrl + "/api/Users/<USER>/AddUser";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processAddUser(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processAddUser(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processAddUser(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<void>(null as any);
    }

    updateUser(body: EditUserCommand | undefined): Observable<void> {
        let url_ = this.baseUrl + "/api/Users/<USER>/UpdateUser";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
            })
        };

        return this.http.request("put", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processUpdateUser(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processUpdateUser(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processUpdateUser(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<void>(null as any);
    }

    userRoles(id: number | undefined): Observable<void> {
        let url_ = this.baseUrl + "/api/Users/<USER>/UserRoles?";
        if (id === null)
            throw new Error("The parameter 'id' cannot be null.");
        else if (id !== undefined)
            url_ += "id=" + encodeURIComponent("" + id) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processUserRoles(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processUserRoles(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processUserRoles(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<void>(null as any);
    }

    updateUserRoles(body: EditUserRolesCommand | undefined): Observable<void> {
        let url_ = this.baseUrl + "/api/Users/<USER>/UpdateUserRoles";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
            })
        };

        return this.http.request("put", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processUpdateUserRoles(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processUpdateUserRoles(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processUpdateUserRoles(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<void>(null as any);
    }

    deleteUser(id: number | undefined): Observable<void> {
        let url_ = this.baseUrl + "/api/Users/<USER>/DeleteUser?";
        if (id === null)
            throw new Error("The parameter 'id' cannot be null.");
        else if (id !== undefined)
            url_ += "id=" + encodeURIComponent("" + id) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
            })
        };

        return this.http.request("delete", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processDeleteUser(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processDeleteUser(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processDeleteUser(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<void>(null as any);
    }

    changePasswordForUser(body: ChangePasswordCommand | undefined): Observable<void> {
        let url_ = this.baseUrl + "/api/Users/<USER>/ChangePasswordForUser";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
            })
        };

        return this.http.request("put", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processChangePasswordForUser(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processChangePasswordForUser(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processChangePasswordForUser(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<void>(null as any);
    }

    /**
     * @return OK
     */
    getFundManagerUsers(): Observable<GetUserListResponsePaginatedResult> {
        let url_ = this.baseUrl + "/api/Users/<USER>/GetFundManagerUsers";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetFundManagerUsers(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetFundManagerUsers(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetUserListResponsePaginatedResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetUserListResponsePaginatedResult>;
        }));
    }

    protected processGetFundManagerUsers(response: HttpResponseBase): Observable<GetUserListResponsePaginatedResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = GetUserListResponsePaginatedResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<GetUserListResponsePaginatedResult>(null as any);
    }

    /**
     * @return OK
     */
    getBoardSecretaryUsers(): Observable<GetUserListResponsePaginatedResult> {
        let url_ = this.baseUrl + "/api/Users/<USER>/GetBoardSecretaryUsers";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetBoardSecretaryUsers(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetBoardSecretaryUsers(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetUserListResponsePaginatedResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetUserListResponsePaginatedResult>;
        }));
    }

    protected processGetBoardSecretaryUsers(response: HttpResponseBase): Observable<GetUserListResponsePaginatedResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = GetUserListResponsePaginatedResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<GetUserListResponsePaginatedResult>(null as any);
    }

    /**
     * @return OK
     */
    getLegalCouncilUsers(): Observable<GetUserListResponsePaginatedResult> {
        let url_ = this.baseUrl + "/api/Users/<USER>/GetLegalCouncilUsers";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetLegalCouncilUsers(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetLegalCouncilUsers(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<GetUserListResponsePaginatedResult>;
                }
            } else
                return _observableThrow(response_) as any as Observable<GetUserListResponsePaginatedResult>;
        }));
    }

    protected processGetLegalCouncilUsers(response: HttpResponseBase): Observable<GetUserListResponsePaginatedResult> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result200: any = null;
            let resultData200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result200 = GetUserListResponsePaginatedResult.fromJS(resultData200);
            return _observableOf(result200);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<GetUserListResponsePaginatedResult>(null as any);
    }

    userListForBoardMembers(fundId: number | undefined, pageNumber: number | undefined, pageSize: number | undefined, search: string | undefined, orderBy: string | undefined): Observable<void> {
        let url_ = this.baseUrl + "/api/Users/<USER>/UserListForBoardMembers?";
        if (fundId === null)
            throw new Error("The parameter 'fundId' cannot be null.");
        else if (fundId !== undefined)
            url_ += "FundId=" + encodeURIComponent("" + fundId) + "&";
        if (pageNumber === null)
            throw new Error("The parameter 'pageNumber' cannot be null.");
        else if (pageNumber !== undefined)
            url_ += "PageNumber=" + encodeURIComponent("" + pageNumber) + "&";
        if (pageSize === null)
            throw new Error("The parameter 'pageSize' cannot be null.");
        else if (pageSize !== undefined)
            url_ += "PageSize=" + encodeURIComponent("" + pageSize) + "&";
        if (search === null)
            throw new Error("The parameter 'search' cannot be null.");
        else if (search !== undefined)
            url_ += "Search=" + encodeURIComponent("" + search) + "&";
        if (orderBy === null)
            throw new Error("The parameter 'orderBy' cannot be null.");
        else if (orderBy !== undefined)
            url_ += "OrderBy=" + encodeURIComponent("" + orderBy) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processUserListForBoardMembers(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processUserListForBoardMembers(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<void>;
                }
            } else
                return _observableThrow(response_) as any as Observable<void>;
        }));
    }

    protected processUserListForBoardMembers(response: HttpResponseBase): Observable<void> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result404: any = null;
            let resultData404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result404 = ProblemDetails.fromJS(resultData404);
            return throwException("Not Found", status, _responseText, _headers, result404);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result400: any = null;
            let resultData400 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result400 = ProblemDetails.fromJS(resultData400);
            return throwException("Bad Request", status, _responseText, _headers, result400);
            }));
        } else if (status === 500) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("Internal Server Error", status, _responseText, _headers);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result401: any = null;
            let resultData401 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result401 = ProblemDetails.fromJS(resultData401);
            return throwException("Unauthorized", status, _responseText, _headers, result401);
            }));
        } else if (status === 403) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            let result403: any = null;
            let resultData403 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver);
            result403 = ProblemDetails.fromJS(resultData403);
            return throwException("Forbidden", status, _responseText, _headers, result403);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap(_responseText => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf<void>(null as any);
    }
}

export class AccessTokenQuery implements IAccessTokenQuery {
    accesstoken!: string | undefined;

    constructor(data?: IAccessTokenQuery) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.accesstoken = _data["accesstoken"];
        }
    }

    static fromJS(data: any): AccessTokenQuery {
        data = typeof data === 'object' ? data : {};
        let result = new AccessTokenQuery();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["accesstoken"] = this.accesstoken;
        return data;
    }
}

export interface IAccessTokenQuery {
    accesstoken: string | undefined;
}

export class AddBoardMemberCommand implements IAddBoardMemberCommand {
    id!: number;
    fundId!: number;
    userId!: number;
    memberType!: BoardMemberType;
    isChairman!: boolean;
    isActive!: boolean | undefined;
    memberName!: string | undefined;
    lastUpdateDate!: DateTime | undefined;
    memberTypeDisplay!: string | undefined;
    statusDisplay!: string | undefined;
    roleDisplay!: string | undefined;

    constructor(data?: IAddBoardMemberCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.fundId = _data["fundId"];
            this.userId = _data["userId"];
            this.memberType = _data["memberType"];
            this.isChairman = _data["isChairman"];
            this.isActive = _data["isActive"];
            this.memberName = _data["memberName"];
            this.lastUpdateDate = _data["lastUpdateDate"] ? DateTime.fromISO(_data["lastUpdateDate"].toString()) : <any>undefined;
            this.memberTypeDisplay = _data["memberTypeDisplay"];
            this.statusDisplay = _data["statusDisplay"];
            this.roleDisplay = _data["roleDisplay"];
        }
    }

    static fromJS(data: any): AddBoardMemberCommand {
        data = typeof data === 'object' ? data : {};
        let result = new AddBoardMemberCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["fundId"] = this.fundId;
        data["userId"] = this.userId;
        data["memberType"] = this.memberType;
        data["isChairman"] = this.isChairman;
        data["isActive"] = this.isActive;
        data["memberName"] = this.memberName;
        data["lastUpdateDate"] = this.lastUpdateDate ? this.lastUpdateDate.toString() : <any>undefined;
        data["memberTypeDisplay"] = this.memberTypeDisplay;
        data["statusDisplay"] = this.statusDisplay;
        data["roleDisplay"] = this.roleDisplay;
        return data;
    }
}

export interface IAddBoardMemberCommand {
    id: number;
    fundId: number;
    userId: number;
    memberType: BoardMemberType;
    isChairman: boolean;
    isActive: boolean | undefined;
    memberName: string | undefined;
    lastUpdateDate: DateTime | undefined;
    memberTypeDisplay: string | undefined;
    statusDisplay: string | undefined;
    roleDisplay: string | undefined;
}

export class AddFundCommand implements IAddFundCommand {
    id!: number;
    name!: string | undefined;
    strategyId!: number;
    strategyName!: string | undefined;
    status!: string | undefined;
    statusId!: number | undefined;
    initiationDate!: DateTime;
    updatedAt!: DateTime | undefined;
    oldCode!: string | undefined;
    propertiesNumber!: number;
    attachmentId!: number;
    votingTypeId!: number;
    legalCouncilId!: number;
    exitDate!: DateTime | undefined;
    fundManagers!: number[] | undefined;
    fundBoardSecretaries!: number[] | undefined;

    constructor(data?: IAddFundCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.name = _data["name"];
            this.strategyId = _data["strategyId"];
            this.strategyName = _data["strategyName"];
            this.status = _data["status"];
            this.statusId = _data["statusId"];
            this.initiationDate = _data["initiationDate"] ? DateTime.fromISO(_data["initiationDate"].toString()) : <any>undefined;
            this.updatedAt = _data["updatedAt"] ? DateTime.fromISO(_data["updatedAt"].toString()) : <any>undefined;
            this.oldCode = _data["oldCode"];
            this.propertiesNumber = _data["propertiesNumber"];
            this.attachmentId = _data["attachmentId"];
            this.votingTypeId = _data["votingTypeId"];
            this.legalCouncilId = _data["legalCouncilId"];
            this.exitDate = _data["exitDate"] ? DateTime.fromISO(_data["exitDate"].toString()) : <any>undefined;
            if (Array.isArray(_data["fundManagers"])) {
                this.fundManagers = [] as any;
                for (let item of _data["fundManagers"])
                    this.fundManagers!.push(item);
            }
            if (Array.isArray(_data["fundBoardSecretaries"])) {
                this.fundBoardSecretaries = [] as any;
                for (let item of _data["fundBoardSecretaries"])
                    this.fundBoardSecretaries!.push(item);
            }
        }
    }

    static fromJS(data: any): AddFundCommand {
        data = typeof data === 'object' ? data : {};
        let result = new AddFundCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["name"] = this.name;
        data["strategyId"] = this.strategyId;
        data["strategyName"] = this.strategyName;
        data["status"] = this.status;
        data["statusId"] = this.statusId;
        data["initiationDate"] = this.initiationDate ? this.initiationDate.toString() : <any>undefined;
        data["updatedAt"] = this.updatedAt ? this.updatedAt.toString() : <any>undefined;
        data["oldCode"] = this.oldCode;
        data["propertiesNumber"] = this.propertiesNumber;
        data["attachmentId"] = this.attachmentId;
        data["votingTypeId"] = this.votingTypeId;
        data["legalCouncilId"] = this.legalCouncilId;
        data["exitDate"] = this.exitDate ? this.exitDate.toString() : <any>undefined;
        if (Array.isArray(this.fundManagers)) {
            data["fundManagers"] = [];
            for (let item of this.fundManagers)
                data["fundManagers"].push(item);
        }
        if (Array.isArray(this.fundBoardSecretaries)) {
            data["fundBoardSecretaries"] = [];
            for (let item of this.fundBoardSecretaries)
                data["fundBoardSecretaries"].push(item);
        }
        return data;
    }
}

export interface IAddFundCommand {
    id: number;
    name: string | undefined;
    strategyId: number;
    strategyName: string | undefined;
    status: string | undefined;
    statusId: number | undefined;
    initiationDate: DateTime;
    updatedAt: DateTime | undefined;
    oldCode: string | undefined;
    propertiesNumber: number;
    attachmentId: number;
    votingTypeId: number;
    legalCouncilId: number;
    exitDate: DateTime | undefined;
    fundManagers: number[] | undefined;
    fundBoardSecretaries: number[] | undefined;
}

export class AddResolutionCommand implements IAddResolutionCommand {
    id!: number;
    code!: string | undefined;
    resolutionDate!: DateTime;
    description!: string | undefined;
    resolutionTypeId!: number;
    newType!: string | undefined;
    attachmentId!: number;
    votingType!: VotingType;
    memberVotingResult!: MemberVotingResult;
    status!: ResolutionStatusEnum;
    fundId!: number;
    parentResolutionId!: number | undefined;
    oldResolutionCode!: string | undefined;
    saveAsDraft!: boolean;
    originalResolutionId!: number | undefined;

    constructor(data?: IAddResolutionCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.code = _data["code"];
            this.resolutionDate = _data["resolutionDate"] ? DateTime.fromISO(_data["resolutionDate"].toString()) : <any>undefined;
            this.description = _data["description"];
            this.resolutionTypeId = _data["resolutionTypeId"];
            this.newType = _data["newType"];
            this.attachmentId = _data["attachmentId"];
            this.votingType = _data["votingType"];
            this.memberVotingResult = _data["memberVotingResult"];
            this.status = _data["status"];
            this.fundId = _data["fundId"];
            this.parentResolutionId = _data["parentResolutionId"];
            this.oldResolutionCode = _data["oldResolutionCode"];
            this.saveAsDraft = _data["saveAsDraft"];
            this.originalResolutionId = _data["originalResolutionId"];
        }
    }

    static fromJS(data: any): AddResolutionCommand {
        data = typeof data === 'object' ? data : {};
        let result = new AddResolutionCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["code"] = this.code;
        data["resolutionDate"] = this.resolutionDate ? this.resolutionDate.toString() : <any>undefined;
        data["description"] = this.description;
        data["resolutionTypeId"] = this.resolutionTypeId;
        data["newType"] = this.newType;
        data["attachmentId"] = this.attachmentId;
        data["votingType"] = this.votingType;
        data["memberVotingResult"] = this.memberVotingResult;
        data["status"] = this.status;
        data["fundId"] = this.fundId;
        data["parentResolutionId"] = this.parentResolutionId;
        data["oldResolutionCode"] = this.oldResolutionCode;
        data["saveAsDraft"] = this.saveAsDraft;
        data["originalResolutionId"] = this.originalResolutionId;
        return data;
    }
}

export interface IAddResolutionCommand {
    id: number;
    code: string | undefined;
    resolutionDate: DateTime;
    description: string | undefined;
    resolutionTypeId: number;
    newType: string | undefined;
    attachmentId: number;
    votingType: VotingType;
    memberVotingResult: MemberVotingResult;
    status: ResolutionStatusEnum;
    fundId: number;
    parentResolutionId: number | undefined;
    oldResolutionCode: string | undefined;
    saveAsDraft: boolean;
    originalResolutionId: number | undefined;
}

export class AddRoleCommand implements IAddRoleCommand {
    roleName!: string | undefined;
    roleClaims!: RoleClaims[] | undefined;

    constructor(data?: IAddRoleCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.roleName = _data["roleName"];
            if (Array.isArray(_data["roleClaims"])) {
                this.roleClaims = [] as any;
                for (let item of _data["roleClaims"])
                    this.roleClaims!.push(RoleClaims.fromJS(item));
            }
        }
    }

    static fromJS(data: any): AddRoleCommand {
        data = typeof data === 'object' ? data : {};
        let result = new AddRoleCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["roleName"] = this.roleName;
        if (Array.isArray(this.roleClaims)) {
            data["roleClaims"] = [];
            for (let item of this.roleClaims)
                data["roleClaims"].push(item ? item.toJSON() : <any>undefined);
        }
        return data;
    }
}

export interface IAddRoleCommand {
    roleName: string | undefined;
    roleClaims: RoleClaims[] | undefined;
}

export class AddUserCommand implements IAddUserCommand {
    fullName!: string | undefined;
    userName!: string | undefined;
    email!: string | undefined;
    password!: string | undefined;
    confirmPassword!: string | undefined;
    phoneNumber!: string | undefined;
    country!: string | undefined;
    address!: string | undefined;
    preferredLanguage!: string | undefined;

    constructor(data?: IAddUserCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.fullName = _data["fullName"];
            this.userName = _data["userName"];
            this.email = _data["email"];
            this.password = _data["password"];
            this.confirmPassword = _data["confirmPassword"];
            this.phoneNumber = _data["phoneNumber"];
            this.country = _data["country"];
            this.address = _data["address"];
            this.preferredLanguage = _data["preferredLanguage"];
        }
    }

    static fromJS(data: any): AddUserCommand {
        data = typeof data === 'object' ? data : {};
        let result = new AddUserCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["fullName"] = this.fullName;
        data["userName"] = this.userName;
        data["email"] = this.email;
        data["password"] = this.password;
        data["confirmPassword"] = this.confirmPassword;
        data["phoneNumber"] = this.phoneNumber;
        data["country"] = this.country;
        data["address"] = this.address;
        data["preferredLanguage"] = this.preferredLanguage;
        return data;
    }
}

export interface IAddUserCommand {
    fullName: string | undefined;
    userName: string | undefined;
    email: string | undefined;
    password: string | undefined;
    confirmPassword: string | undefined;
    phoneNumber: string | undefined;
    country: string | undefined;
    address: string | undefined;
    preferredLanguage: string | undefined;
}

export class BoardMemberResponse implements IBoardMemberResponse {
    id!: number;
    fundId!: number;
    userId!: number;
    memberType!: BoardMemberType;
    isChairman!: boolean;
    isActive!: boolean | undefined;
    memberName!: string | undefined;
    lastUpdateDate!: DateTime | undefined;
    memberTypeDisplay!: string | undefined;
    statusDisplay!: string | undefined;
    roleDisplay!: string | undefined;
    message!: string | undefined;

    constructor(data?: IBoardMemberResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.fundId = _data["fundId"];
            this.userId = _data["userId"];
            this.memberType = _data["memberType"];
            this.isChairman = _data["isChairman"];
            this.isActive = _data["isActive"];
            this.memberName = _data["memberName"];
            this.lastUpdateDate = _data["lastUpdateDate"] ? DateTime.fromISO(_data["lastUpdateDate"].toString()) : <any>undefined;
            this.memberTypeDisplay = _data["memberTypeDisplay"];
            this.statusDisplay = _data["statusDisplay"];
            this.roleDisplay = _data["roleDisplay"];
            this.message = _data["message"];
        }
    }

    static fromJS(data: any): BoardMemberResponse {
        data = typeof data === 'object' ? data : {};
        let result = new BoardMemberResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["fundId"] = this.fundId;
        data["userId"] = this.userId;
        data["memberType"] = this.memberType;
        data["isChairman"] = this.isChairman;
        data["isActive"] = this.isActive;
        data["memberName"] = this.memberName;
        data["lastUpdateDate"] = this.lastUpdateDate ? this.lastUpdateDate.toString() : <any>undefined;
        data["memberTypeDisplay"] = this.memberTypeDisplay;
        data["statusDisplay"] = this.statusDisplay;
        data["roleDisplay"] = this.roleDisplay;
        data["message"] = this.message;
        return data;
    }
}

export interface IBoardMemberResponse {
    id: number;
    fundId: number;
    userId: number;
    memberType: BoardMemberType;
    isChairman: boolean;
    isActive: boolean | undefined;
    memberName: string | undefined;
    lastUpdateDate: DateTime | undefined;
    memberTypeDisplay: string | undefined;
    statusDisplay: string | undefined;
    roleDisplay: string | undefined;
    message: string | undefined;
}

export class BoardMemberResponsePaginatedResult implements IBoardMemberResponsePaginatedResult {
    statusCode!: HttpStatusCode;
    successed!: boolean;
    message!: string | undefined;
    data!: BoardMemberResponse[] | undefined;
    errors!: any[] | undefined;
    currentPage!: number;
    totalCount!: number;
    totalPages!: number;
    pageSize!: number;
    readonly hasPreviousPage!: boolean;
    readonly hasNextPage!: boolean;

    constructor(data?: IBoardMemberResponsePaginatedResult) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.statusCode = _data["statusCode"];
            this.successed = _data["successed"];
            this.message = _data["message"];
            if (Array.isArray(_data["data"])) {
                this.data = [] as any;
                for (let item of _data["data"])
                    this.data!.push(BoardMemberResponse.fromJS(item));
            }
            if (Array.isArray(_data["errors"])) {
                this.errors = [] as any;
                for (let item of _data["errors"])
                    this.errors!.push(item);
            }
            this.currentPage = _data["currentPage"];
            this.totalCount = _data["totalCount"];
            this.totalPages = _data["totalPages"];
            this.pageSize = _data["pageSize"];
            (<any>this).hasPreviousPage = _data["hasPreviousPage"];
            (<any>this).hasNextPage = _data["hasNextPage"];
        }
    }

    static fromJS(data: any): BoardMemberResponsePaginatedResult {
        data = typeof data === 'object' ? data : {};
        let result = new BoardMemberResponsePaginatedResult();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["statusCode"] = this.statusCode;
        data["successed"] = this.successed;
        data["message"] = this.message;
        if (Array.isArray(this.data)) {
            data["data"] = [];
            for (let item of this.data)
                data["data"].push(item ? item.toJSON() : <any>undefined);
        }
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item);
        }
        data["currentPage"] = this.currentPage;
        data["totalCount"] = this.totalCount;
        data["totalPages"] = this.totalPages;
        data["pageSize"] = this.pageSize;
        data["hasPreviousPage"] = this.hasPreviousPage;
        data["hasNextPage"] = this.hasNextPage;
        return data;
    }
}

export interface IBoardMemberResponsePaginatedResult {
    statusCode: HttpStatusCode;
    successed: boolean;
    message: string | undefined;
    data: BoardMemberResponse[] | undefined;
    errors: any[] | undefined;
    currentPage: number;
    totalCount: number;
    totalPages: number;
    pageSize: number;
    hasPreviousPage: boolean;
    hasNextPage: boolean;
}

export enum BoardMemberType {
    _1 = 1,
    _2 = 2,
}

export class ChangePasswordCommand implements IChangePasswordCommand {
    id!: number;
    currentPassword!: string | undefined;
    newPassword!: string | undefined;
    confirmPassword!: string | undefined;

    constructor(data?: IChangePasswordCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.currentPassword = _data["currentPassword"];
            this.newPassword = _data["newPassword"];
            this.confirmPassword = _data["confirmPassword"];
        }
    }

    static fromJS(data: any): ChangePasswordCommand {
        data = typeof data === 'object' ? data : {};
        let result = new ChangePasswordCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["currentPassword"] = this.currentPassword;
        data["newPassword"] = this.newPassword;
        data["confirmPassword"] = this.confirmPassword;
        return data;
    }
}

export interface IChangePasswordCommand {
    id: number;
    currentPassword: string | undefined;
    newPassword: string | undefined;
    confirmPassword: string | undefined;
}

export class DownloadAttachmentCommand implements IDownloadAttachmentCommand {
    id!: number | undefined;

    constructor(data?: IDownloadAttachmentCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
        }
    }

    static fromJS(data: any): DownloadAttachmentCommand {
        data = typeof data === 'object' ? data : {};
        let result = new DownloadAttachmentCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        return data;
    }
}

export interface IDownloadAttachmentCommand {
    id: number | undefined;
}

export class EditExitDateCommand implements IEditExitDateCommand {
    id!: number;
    exitDate!: DateTime;

    constructor(data?: IEditExitDateCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.exitDate = _data["exitDate"] ? DateTime.fromISO(_data["exitDate"].toString()) : <any>undefined;
        }
    }

    static fromJS(data: any): EditExitDateCommand {
        data = typeof data === 'object' ? data : {};
        let result = new EditExitDateCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["exitDate"] = this.exitDate ? this.exitDate.toString() : <any>undefined;
        return data;
    }
}

export interface IEditExitDateCommand {
    id: number;
    exitDate: DateTime;
}

export class EditFundResponse implements IEditFundResponse {
    id!: number;
    name!: string | undefined;
    strategyId!: number;
    strategyName!: string | undefined;
    status!: string | undefined;
    statusId!: number | undefined;
    initiationDate!: DateTime;
    updatedAt!: DateTime | undefined;
    oldCode!: string | undefined;
    propertiesNumber!: number;
    attachmentId!: number;
    votingTypeId!: number;
    legalCouncilId!: number;
    exitDate!: DateTime | undefined;
    fundManagers!: number[] | undefined;
    fundBoardSecretaries!: number[] | undefined;
    attachmentPath!: string | undefined;
    attachmentName!: string | undefined;

    constructor(data?: IEditFundResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.name = _data["name"];
            this.strategyId = _data["strategyId"];
            this.strategyName = _data["strategyName"];
            this.status = _data["status"];
            this.statusId = _data["statusId"];
            this.initiationDate = _data["initiationDate"] ? DateTime.fromISO(_data["initiationDate"].toString()) : <any>undefined;
            this.updatedAt = _data["updatedAt"] ? DateTime.fromISO(_data["updatedAt"].toString()) : <any>undefined;
            this.oldCode = _data["oldCode"];
            this.propertiesNumber = _data["propertiesNumber"];
            this.attachmentId = _data["attachmentId"];
            this.votingTypeId = _data["votingTypeId"];
            this.legalCouncilId = _data["legalCouncilId"];
            this.exitDate = _data["exitDate"] ? DateTime.fromISO(_data["exitDate"].toString()) : <any>undefined;
            if (Array.isArray(_data["fundManagers"])) {
                this.fundManagers = [] as any;
                for (let item of _data["fundManagers"])
                    this.fundManagers!.push(item);
            }
            if (Array.isArray(_data["fundBoardSecretaries"])) {
                this.fundBoardSecretaries = [] as any;
                for (let item of _data["fundBoardSecretaries"])
                    this.fundBoardSecretaries!.push(item);
            }
            this.attachmentPath = _data["attachmentPath"];
            this.attachmentName = _data["attachmentName"];
        }
    }

    static fromJS(data: any): EditFundResponse {
        data = typeof data === 'object' ? data : {};
        let result = new EditFundResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["name"] = this.name;
        data["strategyId"] = this.strategyId;
        data["strategyName"] = this.strategyName;
        data["status"] = this.status;
        data["statusId"] = this.statusId;
        data["initiationDate"] = this.initiationDate ? this.initiationDate.toString() : <any>undefined;
        data["updatedAt"] = this.updatedAt ? this.updatedAt.toString() : <any>undefined;
        data["oldCode"] = this.oldCode;
        data["propertiesNumber"] = this.propertiesNumber;
        data["attachmentId"] = this.attachmentId;
        data["votingTypeId"] = this.votingTypeId;
        data["legalCouncilId"] = this.legalCouncilId;
        data["exitDate"] = this.exitDate ? this.exitDate.toString() : <any>undefined;
        if (Array.isArray(this.fundManagers)) {
            data["fundManagers"] = [];
            for (let item of this.fundManagers)
                data["fundManagers"].push(item);
        }
        if (Array.isArray(this.fundBoardSecretaries)) {
            data["fundBoardSecretaries"] = [];
            for (let item of this.fundBoardSecretaries)
                data["fundBoardSecretaries"].push(item);
        }
        data["attachmentPath"] = this.attachmentPath;
        data["attachmentName"] = this.attachmentName;
        return data;
    }
}

export interface IEditFundResponse {
    id: number;
    name: string | undefined;
    strategyId: number;
    strategyName: string | undefined;
    status: string | undefined;
    statusId: number | undefined;
    initiationDate: DateTime;
    updatedAt: DateTime | undefined;
    oldCode: string | undefined;
    propertiesNumber: number;
    attachmentId: number;
    votingTypeId: number;
    legalCouncilId: number;
    exitDate: DateTime | undefined;
    fundManagers: number[] | undefined;
    fundBoardSecretaries: number[] | undefined;
    attachmentPath: string | undefined;
    attachmentName: string | undefined;
}

export class EditFundResponseBaseResponse implements IEditFundResponseBaseResponse {
    statusCode!: HttpStatusCode;
    successed!: boolean;
    message!: string | undefined;
    data!: EditFundResponse;
    errors!: any[] | undefined;

    constructor(data?: IEditFundResponseBaseResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.statusCode = _data["statusCode"];
            this.successed = _data["successed"];
            this.message = _data["message"];
            this.data = _data["data"] ? EditFundResponse.fromJS(_data["data"]) : <any>undefined;
            if (Array.isArray(_data["errors"])) {
                this.errors = [] as any;
                for (let item of _data["errors"])
                    this.errors!.push(item);
            }
        }
    }

    static fromJS(data: any): EditFundResponseBaseResponse {
        data = typeof data === 'object' ? data : {};
        let result = new EditFundResponseBaseResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["statusCode"] = this.statusCode;
        data["successed"] = this.successed;
        data["message"] = this.message;
        data["data"] = this.data ? this.data.toJSON() : <any>undefined;
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item);
        }
        return data;
    }
}

export interface IEditFundResponseBaseResponse {
    statusCode: HttpStatusCode;
    successed: boolean;
    message: string | undefined;
    data: EditFundResponse;
    errors: any[] | undefined;
}

export class EditResolutionCommand implements IEditResolutionCommand {
    id!: number;
    code!: string | undefined;
    resolutionDate!: DateTime;
    description!: string | undefined;
    resolutionTypeId!: number;
    newType!: string | undefined;
    attachmentId!: number;
    votingType!: VotingType;
    memberVotingResult!: MemberVotingResult;
    status!: ResolutionStatusEnum;
    fundId!: number;
    parentResolutionId!: number | undefined;
    oldResolutionCode!: string | undefined;
    saveAsDraft!: boolean;
    resolutionItems!: ResolutionItemDto[] | undefined;
    attachmentIds!: number[] | undefined;

    constructor(data?: IEditResolutionCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.code = _data["code"];
            this.resolutionDate = _data["resolutionDate"] ? DateTime.fromISO(_data["resolutionDate"].toString()) : <any>undefined;
            this.description = _data["description"];
            this.resolutionTypeId = _data["resolutionTypeId"];
            this.newType = _data["newType"];
            this.attachmentId = _data["attachmentId"];
            this.votingType = _data["votingType"];
            this.memberVotingResult = _data["memberVotingResult"];
            this.status = _data["status"];
            this.fundId = _data["fundId"];
            this.parentResolutionId = _data["parentResolutionId"];
            this.oldResolutionCode = _data["oldResolutionCode"];
            this.saveAsDraft = _data["saveAsDraft"];
            if (Array.isArray(_data["resolutionItems"])) {
                this.resolutionItems = [] as any;
                for (let item of _data["resolutionItems"])
                    this.resolutionItems!.push(ResolutionItemDto.fromJS(item));
            }
            if (Array.isArray(_data["attachmentIds"])) {
                this.attachmentIds = [] as any;
                for (let item of _data["attachmentIds"])
                    this.attachmentIds!.push(item);
            }
        }
    }

    static fromJS(data: any): EditResolutionCommand {
        data = typeof data === 'object' ? data : {};
        let result = new EditResolutionCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["code"] = this.code;
        data["resolutionDate"] = this.resolutionDate ? this.resolutionDate.toString() : <any>undefined;
        data["description"] = this.description;
        data["resolutionTypeId"] = this.resolutionTypeId;
        data["newType"] = this.newType;
        data["attachmentId"] = this.attachmentId;
        data["votingType"] = this.votingType;
        data["memberVotingResult"] = this.memberVotingResult;
        data["status"] = this.status;
        data["fundId"] = this.fundId;
        data["parentResolutionId"] = this.parentResolutionId;
        data["oldResolutionCode"] = this.oldResolutionCode;
        data["saveAsDraft"] = this.saveAsDraft;
        if (Array.isArray(this.resolutionItems)) {
            data["resolutionItems"] = [];
            for (let item of this.resolutionItems)
                data["resolutionItems"].push(item ? item.toJSON() : <any>undefined);
        }
        if (Array.isArray(this.attachmentIds)) {
            data["attachmentIds"] = [];
            for (let item of this.attachmentIds)
                data["attachmentIds"].push(item);
        }
        return data;
    }
}

export interface IEditResolutionCommand {
    id: number;
    code: string | undefined;
    resolutionDate: DateTime;
    description: string | undefined;
    resolutionTypeId: number;
    newType: string | undefined;
    attachmentId: number;
    votingType: VotingType;
    memberVotingResult: MemberVotingResult;
    status: ResolutionStatusEnum;
    fundId: number;
    parentResolutionId: number | undefined;
    oldResolutionCode: string | undefined;
    saveAsDraft: boolean;
    resolutionItems: ResolutionItemDto[] | undefined;
    attachmentIds: number[] | undefined;
}

export class EditRoleCommand implements IEditRoleCommand {
    id!: number;
    roleName!: string | undefined;
    roleClaims!: RoleClaims[] | undefined;

    constructor(data?: IEditRoleCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.roleName = _data["roleName"];
            if (Array.isArray(_data["roleClaims"])) {
                this.roleClaims = [] as any;
                for (let item of _data["roleClaims"])
                    this.roleClaims!.push(RoleClaims.fromJS(item));
            }
        }
    }

    static fromJS(data: any): EditRoleCommand {
        data = typeof data === 'object' ? data : {};
        let result = new EditRoleCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["roleName"] = this.roleName;
        if (Array.isArray(this.roleClaims)) {
            data["roleClaims"] = [];
            for (let item of this.roleClaims)
                data["roleClaims"].push(item ? item.toJSON() : <any>undefined);
        }
        return data;
    }
}

export interface IEditRoleCommand {
    id: number;
    roleName: string | undefined;
    roleClaims: RoleClaims[] | undefined;
}

export class EditUserCommand implements IEditUserCommand {
    id!: number;
    fullName!: string | undefined;
    userName!: string | undefined;
    email!: string | undefined;
    country!: string | undefined;
    address!: string | undefined;

    constructor(data?: IEditUserCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.fullName = _data["fullName"];
            this.userName = _data["userName"];
            this.email = _data["email"];
            this.country = _data["country"];
            this.address = _data["address"];
        }
    }

    static fromJS(data: any): EditUserCommand {
        data = typeof data === 'object' ? data : {};
        let result = new EditUserCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["fullName"] = this.fullName;
        data["userName"] = this.userName;
        data["email"] = this.email;
        data["country"] = this.country;
        data["address"] = this.address;
        return data;
    }
}

export interface IEditUserCommand {
    id: number;
    fullName: string | undefined;
    userName: string | undefined;
    email: string | undefined;
    country: string | undefined;
    address: string | undefined;
}

export class EditUserRolesCommand implements IEditUserRolesCommand {
    userId!: number;
    userRoles!: UserRoles[] | undefined;

    constructor(data?: IEditUserRolesCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.userId = _data["userId"];
            if (Array.isArray(_data["userRoles"])) {
                this.userRoles = [] as any;
                for (let item of _data["userRoles"])
                    this.userRoles!.push(UserRoles.fromJS(item));
            }
        }
    }

    static fromJS(data: any): EditUserRolesCommand {
        data = typeof data === 'object' ? data : {};
        let result = new EditUserRolesCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["userId"] = this.userId;
        if (Array.isArray(this.userRoles)) {
            data["userRoles"] = [];
            for (let item of this.userRoles)
                data["userRoles"].push(item ? item.toJSON() : <any>undefined);
        }
        return data;
    }
}

export interface IEditUserRolesCommand {
    userId: number;
    userRoles: UserRoles[] | undefined;
}

export class FundDetailsResponse implements IFundDetailsResponse {
    id!: number;
    name!: string | undefined;
    strategyId!: number;
    strategyName!: string | undefined;
    status!: string | undefined;
    statusId!: number | undefined;
    initiationDate!: DateTime;
    updatedAt!: DateTime | undefined;
    exitDate!: DateTime | undefined;
    propertiesNumber!: number;
    decisionCount!: number;
    evaluationCount!: number;
    documentCount!: number;
    meetingCount!: number;
    memberCount!: number;
    fundNotifications!: FundNotificationDto[] | undefined;
    fundHistory!: FundHistoryDto[] | undefined;

    constructor(data?: IFundDetailsResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.name = _data["name"];
            this.strategyId = _data["strategyId"];
            this.strategyName = _data["strategyName"];
            this.status = _data["status"];
            this.statusId = _data["statusId"];
            this.initiationDate = _data["initiationDate"] ? DateTime.fromISO(_data["initiationDate"].toString()) : <any>undefined;
            this.updatedAt = _data["updatedAt"] ? DateTime.fromISO(_data["updatedAt"].toString()) : <any>undefined;
            this.exitDate = _data["exitDate"] ? DateTime.fromISO(_data["exitDate"].toString()) : <any>undefined;
            this.propertiesNumber = _data["propertiesNumber"];
            this.decisionCount = _data["decisionCount"];
            this.evaluationCount = _data["evaluationCount"];
            this.documentCount = _data["documentCount"];
            this.meetingCount = _data["meetingCount"];
            this.memberCount = _data["memberCount"];
            if (Array.isArray(_data["fundNotifications"])) {
                this.fundNotifications = [] as any;
                for (let item of _data["fundNotifications"])
                    this.fundNotifications!.push(FundNotificationDto.fromJS(item));
            }
            if (Array.isArray(_data["fundHistory"])) {
                this.fundHistory = [] as any;
                for (let item of _data["fundHistory"])
                    this.fundHistory!.push(FundHistoryDto.fromJS(item));
            }
        }
    }

    static fromJS(data: any): FundDetailsResponse {
        data = typeof data === 'object' ? data : {};
        let result = new FundDetailsResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["name"] = this.name;
        data["strategyId"] = this.strategyId;
        data["strategyName"] = this.strategyName;
        data["status"] = this.status;
        data["statusId"] = this.statusId;
        data["initiationDate"] = this.initiationDate ? this.initiationDate.toString() : <any>undefined;
        data["updatedAt"] = this.updatedAt ? this.updatedAt.toString() : <any>undefined;
        data["exitDate"] = this.exitDate ? this.exitDate.toString() : <any>undefined;
        data["propertiesNumber"] = this.propertiesNumber;
        data["decisionCount"] = this.decisionCount;
        data["evaluationCount"] = this.evaluationCount;
        data["documentCount"] = this.documentCount;
        data["meetingCount"] = this.meetingCount;
        data["memberCount"] = this.memberCount;
        if (Array.isArray(this.fundNotifications)) {
            data["fundNotifications"] = [];
            for (let item of this.fundNotifications)
                data["fundNotifications"].push(item ? item.toJSON() : <any>undefined);
        }
        if (Array.isArray(this.fundHistory)) {
            data["fundHistory"] = [];
            for (let item of this.fundHistory)
                data["fundHistory"].push(item ? item.toJSON() : <any>undefined);
        }
        return data;
    }
}

export interface IFundDetailsResponse {
    id: number;
    name: string | undefined;
    strategyId: number;
    strategyName: string | undefined;
    status: string | undefined;
    statusId: number | undefined;
    initiationDate: DateTime;
    updatedAt: DateTime | undefined;
    exitDate: DateTime | undefined;
    propertiesNumber: number;
    decisionCount: number;
    evaluationCount: number;
    documentCount: number;
    meetingCount: number;
    memberCount: number;
    fundNotifications: FundNotificationDto[] | undefined;
    fundHistory: FundHistoryDto[] | undefined;
}

export class FundDetailsResponseBaseResponse implements IFundDetailsResponseBaseResponse {
    statusCode!: HttpStatusCode;
    successed!: boolean;
    message!: string | undefined;
    data!: FundDetailsResponse;
    errors!: any[] | undefined;

    constructor(data?: IFundDetailsResponseBaseResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.statusCode = _data["statusCode"];
            this.successed = _data["successed"];
            this.message = _data["message"];
            this.data = _data["data"] ? FundDetailsResponse.fromJS(_data["data"]) : <any>undefined;
            if (Array.isArray(_data["errors"])) {
                this.errors = [] as any;
                for (let item of _data["errors"])
                    this.errors!.push(item);
            }
        }
    }

    static fromJS(data: any): FundDetailsResponseBaseResponse {
        data = typeof data === 'object' ? data : {};
        let result = new FundDetailsResponseBaseResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["statusCode"] = this.statusCode;
        data["successed"] = this.successed;
        data["message"] = this.message;
        data["data"] = this.data ? this.data.toJSON() : <any>undefined;
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item);
        }
        return data;
    }
}

export interface IFundDetailsResponseBaseResponse {
    statusCode: HttpStatusCode;
    successed: boolean;
    message: string | undefined;
    data: FundDetailsResponse;
    errors: any[] | undefined;
}

export class FundGroupDto implements IFundGroupDto {
    title!: string | undefined;
    hasNotification!: boolean;
    funds!: SingleFundResponse[] | undefined;
    count!: number;

    constructor(data?: IFundGroupDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.title = _data["title"];
            this.hasNotification = _data["hasNotification"];
            if (Array.isArray(_data["funds"])) {
                this.funds = [] as any;
                for (let item of _data["funds"])
                    this.funds!.push(SingleFundResponse.fromJS(item));
            }
            this.count = _data["count"];
        }
    }

    static fromJS(data: any): FundGroupDto {
        data = typeof data === 'object' ? data : {};
        let result = new FundGroupDto();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["title"] = this.title;
        data["hasNotification"] = this.hasNotification;
        if (Array.isArray(this.funds)) {
            data["funds"] = [];
            for (let item of this.funds)
                data["funds"].push(item ? item.toJSON() : <any>undefined);
        }
        data["count"] = this.count;
        return data;
    }
}

export interface IFundGroupDto {
    title: string | undefined;
    hasNotification: boolean;
    funds: SingleFundResponse[] | undefined;
    count: number;
}

export class FundGroupDtoPaginatedResult implements IFundGroupDtoPaginatedResult {
    statusCode!: HttpStatusCode;
    successed!: boolean;
    message!: string | undefined;
    data!: FundGroupDto[] | undefined;
    errors!: any[] | undefined;
    currentPage!: number;
    totalCount!: number;
    totalPages!: number;
    pageSize!: number;
    readonly hasPreviousPage!: boolean;
    readonly hasNextPage!: boolean;

    constructor(data?: IFundGroupDtoPaginatedResult) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.statusCode = _data["statusCode"];
            this.successed = _data["successed"];
            this.message = _data["message"];
            if (Array.isArray(_data["data"])) {
                this.data = [] as any;
                for (let item of _data["data"])
                    this.data!.push(FundGroupDto.fromJS(item));
            }
            if (Array.isArray(_data["errors"])) {
                this.errors = [] as any;
                for (let item of _data["errors"])
                    this.errors!.push(item);
            }
            this.currentPage = _data["currentPage"];
            this.totalCount = _data["totalCount"];
            this.totalPages = _data["totalPages"];
            this.pageSize = _data["pageSize"];
            (<any>this).hasPreviousPage = _data["hasPreviousPage"];
            (<any>this).hasNextPage = _data["hasNextPage"];
        }
    }

    static fromJS(data: any): FundGroupDtoPaginatedResult {
        data = typeof data === 'object' ? data : {};
        let result = new FundGroupDtoPaginatedResult();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["statusCode"] = this.statusCode;
        data["successed"] = this.successed;
        data["message"] = this.message;
        if (Array.isArray(this.data)) {
            data["data"] = [];
            for (let item of this.data)
                data["data"].push(item ? item.toJSON() : <any>undefined);
        }
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item);
        }
        data["currentPage"] = this.currentPage;
        data["totalCount"] = this.totalCount;
        data["totalPages"] = this.totalPages;
        data["pageSize"] = this.pageSize;
        data["hasPreviousPage"] = this.hasPreviousPage;
        data["hasNextPage"] = this.hasNextPage;
        return data;
    }
}

export interface IFundGroupDtoPaginatedResult {
    statusCode: HttpStatusCode;
    successed: boolean;
    message: string | undefined;
    data: FundGroupDto[] | undefined;
    errors: any[] | undefined;
    currentPage: number;
    totalCount: number;
    totalPages: number;
    pageSize: number;
    hasPreviousPage: boolean;
    hasNextPage: boolean;
}

export class FundHistoryDto implements IFundHistoryDto {
    id!: number;
    userId!: number;
    userName!: string | undefined;
    roleName!: string | undefined;
    statusId!: number;
    statusName!: string | undefined;
    createdAt!: DateTime;

    constructor(data?: IFundHistoryDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.userId = _data["userId"];
            this.userName = _data["userName"];
            this.roleName = _data["roleName"];
            this.statusId = _data["statusId"];
            this.statusName = _data["statusName"];
            this.createdAt = _data["createdAt"] ? DateTime.fromISO(_data["createdAt"].toString()) : <any>undefined;
        }
    }

    static fromJS(data: any): FundHistoryDto {
        data = typeof data === 'object' ? data : {};
        let result = new FundHistoryDto();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["userId"] = this.userId;
        data["userName"] = this.userName;
        data["roleName"] = this.roleName;
        data["statusId"] = this.statusId;
        data["statusName"] = this.statusName;
        data["createdAt"] = this.createdAt ? this.createdAt.toString() : <any>undefined;
        return data;
    }
}

export interface IFundHistoryDto {
    id: number;
    userId: number;
    userName: string | undefined;
    roleName: string | undefined;
    statusId: number;
    statusName: string | undefined;
    createdAt: DateTime;
}

export class FundInitiationDateDto implements IFundInitiationDateDto {
    fundId!: number;
    fundName!: string | undefined;
    initiationDate!: DateTime;
    readonly initiationDateAr!: string | undefined;
    readonly initiationDateEn!: string | undefined;
    readonly initiationDateIso!: string | undefined;
    readonly localizedInitiationDate!: string | undefined;
    readonly formattedInitiationDate!: string | undefined;

    constructor(data?: IFundInitiationDateDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.fundId = _data["fundId"];
            this.fundName = _data["fundName"];
            this.initiationDate = _data["initiationDate"] ? DateTime.fromISO(_data["initiationDate"].toString()) : <any>undefined;
            (<any>this).initiationDateAr = _data["initiationDateAr"];
            (<any>this).initiationDateEn = _data["initiationDateEn"];
            (<any>this).initiationDateIso = _data["initiationDateIso"];
            (<any>this).localizedInitiationDate = _data["localizedInitiationDate"];
            (<any>this).formattedInitiationDate = _data["formattedInitiationDate"];
        }
    }

    static fromJS(data: any): FundInitiationDateDto {
        data = typeof data === 'object' ? data : {};
        let result = new FundInitiationDateDto();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["fundId"] = this.fundId;
        data["fundName"] = this.fundName;
        data["initiationDate"] = this.initiationDate ? this.initiationDate.toString() : <any>undefined;
        data["initiationDateAr"] = this.initiationDateAr;
        data["initiationDateEn"] = this.initiationDateEn;
        data["initiationDateIso"] = this.initiationDateIso;
        data["localizedInitiationDate"] = this.localizedInitiationDate;
        data["formattedInitiationDate"] = this.formattedInitiationDate;
        return data;
    }
}

export interface IFundInitiationDateDto {
    fundId: number;
    fundName: string | undefined;
    initiationDate: DateTime;
    initiationDateAr: string | undefined;
    initiationDateEn: string | undefined;
    initiationDateIso: string | undefined;
    localizedInitiationDate: string | undefined;
    formattedInitiationDate: string | undefined;
}

export class FundInitiationDateDtoBaseResponse implements IFundInitiationDateDtoBaseResponse {
    statusCode!: HttpStatusCode;
    successed!: boolean;
    message!: string | undefined;
    data!: FundInitiationDateDto;
    errors!: any[] | undefined;

    constructor(data?: IFundInitiationDateDtoBaseResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.statusCode = _data["statusCode"];
            this.successed = _data["successed"];
            this.message = _data["message"];
            this.data = _data["data"] ? FundInitiationDateDto.fromJS(_data["data"]) : <any>undefined;
            if (Array.isArray(_data["errors"])) {
                this.errors = [] as any;
                for (let item of _data["errors"])
                    this.errors!.push(item);
            }
        }
    }

    static fromJS(data: any): FundInitiationDateDtoBaseResponse {
        data = typeof data === 'object' ? data : {};
        let result = new FundInitiationDateDtoBaseResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["statusCode"] = this.statusCode;
        data["successed"] = this.successed;
        data["message"] = this.message;
        data["data"] = this.data ? this.data.toJSON() : <any>undefined;
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item);
        }
        return data;
    }
}

export interface IFundInitiationDateDtoBaseResponse {
    statusCode: HttpStatusCode;
    successed: boolean;
    message: string | undefined;
    data: FundInitiationDateDto;
    errors: any[] | undefined;
}

export class FundNotificationDto implements IFundNotificationDto {
    id!: number;
    title!: string | undefined;
    message!: string | undefined;
    createdAt!: DateTime;

    constructor(data?: IFundNotificationDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.title = _data["title"];
            this.message = _data["message"];
            this.createdAt = _data["createdAt"] ? DateTime.fromISO(_data["createdAt"].toString()) : <any>undefined;
        }
    }

    static fromJS(data: any): FundNotificationDto {
        data = typeof data === 'object' ? data : {};
        let result = new FundNotificationDto();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["title"] = this.title;
        data["message"] = this.message;
        data["createdAt"] = this.createdAt ? this.createdAt.toString() : <any>undefined;
        return data;
    }
}

export interface IFundNotificationDto {
    id: number;
    title: string | undefined;
    message: string | undefined;
    createdAt: DateTime;
}

export class FundVoteTypeDto implements IFundVoteTypeDto {
    fundId!: number;
    fundName!: string | undefined;
    votingType!: VotingType;
    votingTypeId!: number;
    votingTypeNameAr!: string | undefined;
    votingTypeNameEn!: string | undefined;
    readonly localizedVotingTypeName!: string | undefined;

    constructor(data?: IFundVoteTypeDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.fundId = _data["fundId"];
            this.fundName = _data["fundName"];
            this.votingType = _data["votingType"];
            this.votingTypeId = _data["votingTypeId"];
            this.votingTypeNameAr = _data["votingTypeNameAr"];
            this.votingTypeNameEn = _data["votingTypeNameEn"];
            (<any>this).localizedVotingTypeName = _data["localizedVotingTypeName"];
        }
    }

    static fromJS(data: any): FundVoteTypeDto {
        data = typeof data === 'object' ? data : {};
        let result = new FundVoteTypeDto();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["fundId"] = this.fundId;
        data["fundName"] = this.fundName;
        data["votingType"] = this.votingType;
        data["votingTypeId"] = this.votingTypeId;
        data["votingTypeNameAr"] = this.votingTypeNameAr;
        data["votingTypeNameEn"] = this.votingTypeNameEn;
        data["localizedVotingTypeName"] = this.localizedVotingTypeName;
        return data;
    }
}

export interface IFundVoteTypeDto {
    fundId: number;
    fundName: string | undefined;
    votingType: VotingType;
    votingTypeId: number;
    votingTypeNameAr: string | undefined;
    votingTypeNameEn: string | undefined;
    localizedVotingTypeName: string | undefined;
}

export class FundVoteTypeDtoBaseResponse implements IFundVoteTypeDtoBaseResponse {
    statusCode!: HttpStatusCode;
    successed!: boolean;
    message!: string | undefined;
    data!: FundVoteTypeDto;
    errors!: any[] | undefined;

    constructor(data?: IFundVoteTypeDtoBaseResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.statusCode = _data["statusCode"];
            this.successed = _data["successed"];
            this.message = _data["message"];
            this.data = _data["data"] ? FundVoteTypeDto.fromJS(_data["data"]) : <any>undefined;
            if (Array.isArray(_data["errors"])) {
                this.errors = [] as any;
                for (let item of _data["errors"])
                    this.errors!.push(item);
            }
        }
    }

    static fromJS(data: any): FundVoteTypeDtoBaseResponse {
        data = typeof data === 'object' ? data : {};
        let result = new FundVoteTypeDtoBaseResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["statusCode"] = this.statusCode;
        data["successed"] = this.successed;
        data["message"] = this.message;
        data["data"] = this.data ? this.data.toJSON() : <any>undefined;
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item);
        }
        return data;
    }
}

export interface IFundVoteTypeDtoBaseResponse {
    statusCode: HttpStatusCode;
    successed: boolean;
    message: string | undefined;
    data: FundVoteTypeDto;
    errors: any[] | undefined;
}

export class GetUserListResponse implements IGetUserListResponse {
    id!: number;
    fullName!: string | undefined;
    email!: string | undefined;
    country!: string | undefined;
    address!: string | undefined;

    constructor(data?: IGetUserListResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.fullName = _data["fullName"];
            this.email = _data["email"];
            this.country = _data["country"];
            this.address = _data["address"];
        }
    }

    static fromJS(data: any): GetUserListResponse {
        data = typeof data === 'object' ? data : {};
        let result = new GetUserListResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["fullName"] = this.fullName;
        data["email"] = this.email;
        data["country"] = this.country;
        data["address"] = this.address;
        return data;
    }
}

export interface IGetUserListResponse {
    id: number;
    fullName: string | undefined;
    email: string | undefined;
    country: string | undefined;
    address: string | undefined;
}

export class GetUserListResponsePaginatedResult implements IGetUserListResponsePaginatedResult {
    statusCode!: HttpStatusCode;
    successed!: boolean;
    message!: string | undefined;
    data!: GetUserListResponse[] | undefined;
    errors!: any[] | undefined;
    currentPage!: number;
    totalCount!: number;
    totalPages!: number;
    pageSize!: number;
    readonly hasPreviousPage!: boolean;
    readonly hasNextPage!: boolean;

    constructor(data?: IGetUserListResponsePaginatedResult) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.statusCode = _data["statusCode"];
            this.successed = _data["successed"];
            this.message = _data["message"];
            if (Array.isArray(_data["data"])) {
                this.data = [] as any;
                for (let item of _data["data"])
                    this.data!.push(GetUserListResponse.fromJS(item));
            }
            if (Array.isArray(_data["errors"])) {
                this.errors = [] as any;
                for (let item of _data["errors"])
                    this.errors!.push(item);
            }
            this.currentPage = _data["currentPage"];
            this.totalCount = _data["totalCount"];
            this.totalPages = _data["totalPages"];
            this.pageSize = _data["pageSize"];
            (<any>this).hasPreviousPage = _data["hasPreviousPage"];
            (<any>this).hasNextPage = _data["hasNextPage"];
        }
    }

    static fromJS(data: any): GetUserListResponsePaginatedResult {
        data = typeof data === 'object' ? data : {};
        let result = new GetUserListResponsePaginatedResult();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["statusCode"] = this.statusCode;
        data["successed"] = this.successed;
        data["message"] = this.message;
        if (Array.isArray(this.data)) {
            data["data"] = [];
            for (let item of this.data)
                data["data"].push(item ? item.toJSON() : <any>undefined);
        }
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item);
        }
        data["currentPage"] = this.currentPage;
        data["totalCount"] = this.totalCount;
        data["totalPages"] = this.totalPages;
        data["pageSize"] = this.pageSize;
        data["hasPreviousPage"] = this.hasPreviousPage;
        data["hasNextPage"] = this.hasNextPage;
        return data;
    }
}

export interface IGetUserListResponsePaginatedResult {
    statusCode: HttpStatusCode;
    successed: boolean;
    message: string | undefined;
    data: GetUserListResponse[] | undefined;
    errors: any[] | undefined;
    currentPage: number;
    totalCount: number;
    totalPages: number;
    pageSize: number;
    hasPreviousPage: boolean;
    hasNextPage: boolean;
}

export enum HttpStatusCode {
    _100 = 100,
    _101 = 101,
    _102 = 102,
    _103 = 103,
    _200 = 200,
    _201 = 201,
    _202 = 202,
    _203 = 203,
    _204 = 204,
    _205 = 205,
    _206 = 206,
    _207 = 207,
    _208 = 208,
    _226 = 226,
    _300 = 300,
    _301 = 301,
    _302 = 302,
    _303 = 303,
    _304 = 304,
    _305 = 305,
    _306 = 306,
    _307 = 307,
    _308 = 308,
    _400 = 400,
    _401 = 401,
    _402 = 402,
    _403 = 403,
    _404 = 404,
    _405 = 405,
    _406 = 406,
    _407 = 407,
    _408 = 408,
    _409 = 409,
    _410 = 410,
    _411 = 411,
    _412 = 412,
    _413 = 413,
    _414 = 414,
    _415 = 415,
    _416 = 416,
    _417 = 417,
    _421 = 421,
    _422 = 422,
    _423 = 423,
    _424 = 424,
    _426 = 426,
    _428 = 428,
    _429 = 429,
    _431 = 431,
    _451 = 451,
    _500 = 500,
    _501 = 501,
    _502 = 502,
    _503 = 503,
    _504 = 504,
    _505 = 505,
    _506 = 506,
    _507 = 507,
    _508 = 508,
    _510 = 510,
    _511 = 511,
}

export class JwtAuthResponse implements IJwtAuthResponse {
    accessToken!: string | undefined;
    refreshToken!: RefreshToken;

    constructor(data?: IJwtAuthResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.accessToken = _data["accessToken"];
            this.refreshToken = _data["refreshToken"] ? RefreshToken.fromJS(_data["refreshToken"]) : <any>undefined;
        }
    }

    static fromJS(data: any): JwtAuthResponse {
        data = typeof data === 'object' ? data : {};
        let result = new JwtAuthResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["accessToken"] = this.accessToken;
        data["refreshToken"] = this.refreshToken ? this.refreshToken.toJSON() : <any>undefined;
        return data;
    }
}

export interface IJwtAuthResponse {
    accessToken: string | undefined;
    refreshToken: RefreshToken;
}

export class JwtAuthResponseBaseResponse implements IJwtAuthResponseBaseResponse {
    statusCode!: HttpStatusCode;
    successed!: boolean;
    message!: string | undefined;
    data!: JwtAuthResponse;
    errors!: any[] | undefined;

    constructor(data?: IJwtAuthResponseBaseResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.statusCode = _data["statusCode"];
            this.successed = _data["successed"];
            this.message = _data["message"];
            this.data = _data["data"] ? JwtAuthResponse.fromJS(_data["data"]) : <any>undefined;
            if (Array.isArray(_data["errors"])) {
                this.errors = [] as any;
                for (let item of _data["errors"])
                    this.errors!.push(item);
            }
        }
    }

    static fromJS(data: any): JwtAuthResponseBaseResponse {
        data = typeof data === 'object' ? data : {};
        let result = new JwtAuthResponseBaseResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["statusCode"] = this.statusCode;
        data["successed"] = this.successed;
        data["message"] = this.message;
        data["data"] = this.data ? this.data.toJSON() : <any>undefined;
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item);
        }
        return data;
    }
}

export interface IJwtAuthResponseBaseResponse {
    statusCode: HttpStatusCode;
    successed: boolean;
    message: string | undefined;
    data: JwtAuthResponse;
    errors: any[] | undefined;
}

export enum MemberVotingResult {
    _1 = 1,
    _2 = 2,
}

export class NotificationDto implements INotificationDto {
    isRead!: boolean;
    title!: string | undefined;
    body!: string | undefined;
    createdAt!: DateTime;

    constructor(data?: INotificationDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.isRead = _data["isRead"];
            this.title = _data["title"];
            this.body = _data["body"];
            this.createdAt = _data["createdAt"] ? DateTime.fromISO(_data["createdAt"].toString()) : <any>undefined;
        }
    }

    static fromJS(data: any): NotificationDto {
        data = typeof data === 'object' ? data : {};
        let result = new NotificationDto();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["isRead"] = this.isRead;
        data["title"] = this.title;
        data["body"] = this.body;
        data["createdAt"] = this.createdAt ? this.createdAt.toString() : <any>undefined;
        return data;
    }
}

export interface INotificationDto {
    isRead: boolean;
    title: string | undefined;
    body: string | undefined;
    createdAt: DateTime;
}

export class NotificationDtoPaginatedResult implements INotificationDtoPaginatedResult {
    statusCode!: HttpStatusCode;
    successed!: boolean;
    message!: string | undefined;
    data!: NotificationDto[] | undefined;
    errors!: any[] | undefined;
    currentPage!: number;
    totalCount!: number;
    totalPages!: number;
    pageSize!: number;
    readonly hasPreviousPage!: boolean;
    readonly hasNextPage!: boolean;

    constructor(data?: INotificationDtoPaginatedResult) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.statusCode = _data["statusCode"];
            this.successed = _data["successed"];
            this.message = _data["message"];
            if (Array.isArray(_data["data"])) {
                this.data = [] as any;
                for (let item of _data["data"])
                    this.data!.push(NotificationDto.fromJS(item));
            }
            if (Array.isArray(_data["errors"])) {
                this.errors = [] as any;
                for (let item of _data["errors"])
                    this.errors!.push(item);
            }
            this.currentPage = _data["currentPage"];
            this.totalCount = _data["totalCount"];
            this.totalPages = _data["totalPages"];
            this.pageSize = _data["pageSize"];
            (<any>this).hasPreviousPage = _data["hasPreviousPage"];
            (<any>this).hasNextPage = _data["hasNextPage"];
        }
    }

    static fromJS(data: any): NotificationDtoPaginatedResult {
        data = typeof data === 'object' ? data : {};
        let result = new NotificationDtoPaginatedResult();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["statusCode"] = this.statusCode;
        data["successed"] = this.successed;
        data["message"] = this.message;
        if (Array.isArray(this.data)) {
            data["data"] = [];
            for (let item of this.data)
                data["data"].push(item ? item.toJSON() : <any>undefined);
        }
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item);
        }
        data["currentPage"] = this.currentPage;
        data["totalCount"] = this.totalCount;
        data["totalPages"] = this.totalPages;
        data["pageSize"] = this.pageSize;
        data["hasPreviousPage"] = this.hasPreviousPage;
        data["hasNextPage"] = this.hasNextPage;
        return data;
    }
}

export interface INotificationDtoPaginatedResult {
    statusCode: HttpStatusCode;
    successed: boolean;
    message: string | undefined;
    data: NotificationDto[] | undefined;
    errors: any[] | undefined;
    currentPage: number;
    totalCount: number;
    totalPages: number;
    pageSize: number;
    hasPreviousPage: boolean;
    hasNextPage: boolean;
}

export class ProblemDetails implements IProblemDetails {
    type!: string | undefined;
    title!: string | undefined;
    status!: number | undefined;
    detail!: string | undefined;
    instance!: string | undefined;

    [key: string]: any;

    constructor(data?: IProblemDetails) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            for (var property in _data) {
                if (_data.hasOwnProperty(property))
                    this[property] = _data[property];
            }
            this.type = _data["type"];
            this.title = _data["title"];
            this.status = _data["status"];
            this.detail = _data["detail"];
            this.instance = _data["instance"];
        }
    }

    static fromJS(data: any): ProblemDetails {
        data = typeof data === 'object' ? data : {};
        let result = new ProblemDetails();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        for (var property in this) {
            if (this.hasOwnProperty(property))
                data[property] = this[property];
        }
        data["type"] = this.type;
        data["title"] = this.title;
        data["status"] = this.status;
        data["detail"] = this.detail;
        data["instance"] = this.instance;
        return data;
    }
}

export interface IProblemDetails {
    type: string | undefined;
    title: string | undefined;
    status: number | undefined;
    detail: string | undefined;
    instance: string | undefined;

    [key: string]: any;
}

export class RefreshToken implements IRefreshToken {
    userName!: string | undefined;
    expireAt!: DateTime;
    tokenString!: string | undefined;

    constructor(data?: IRefreshToken) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.userName = _data["userName"];
            this.expireAt = _data["expireAt"] ? DateTime.fromISO(_data["expireAt"].toString()) : <any>undefined;
            this.tokenString = _data["tokenString"];
        }
    }

    static fromJS(data: any): RefreshToken {
        data = typeof data === 'object' ? data : {};
        let result = new RefreshToken();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["userName"] = this.userName;
        data["expireAt"] = this.expireAt ? this.expireAt.toString() : <any>undefined;
        data["tokenString"] = this.tokenString;
        return data;
    }
}

export interface IRefreshToken {
    userName: string | undefined;
    expireAt: DateTime;
    tokenString: string | undefined;
}

export class RefreshTokenCommand implements IRefreshTokenCommand {
    accessToken!: string | undefined;
    refreshToken!: string | undefined;

    constructor(data?: IRefreshTokenCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.accessToken = _data["accessToken"];
            this.refreshToken = _data["refreshToken"];
        }
    }

    static fromJS(data: any): RefreshTokenCommand {
        data = typeof data === 'object' ? data : {};
        let result = new RefreshTokenCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["accessToken"] = this.accessToken;
        data["refreshToken"] = this.refreshToken;
        return data;
    }
}

export interface IRefreshTokenCommand {
    accessToken: string | undefined;
    refreshToken: string | undefined;
}

export class RejectResolutionCommand implements IRejectResolutionCommand {
    id!: number;
    rejectionReason!: string;

    constructor(data?: IRejectResolutionCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.rejectionReason = _data["rejectionReason"];
        }
    }

    static fromJS(data: any): RejectResolutionCommand {
        data = typeof data === 'object' ? data : {};
        let result = new RejectResolutionCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["rejectionReason"] = this.rejectionReason;
        return data;
    }
}

export interface IRejectResolutionCommand {
    id: number;
    rejectionReason: string;
}

export class ResolutionItemConflictDto implements IResolutionItemConflictDto {
    id!: number;
    resolutionItemId!: number;
    boardMemberId!: number;
    conflictNotes!: string | undefined;
    boardMemberName!: string | undefined;
    userName!: string | undefined;
    memberType!: string | undefined;

    constructor(data?: IResolutionItemConflictDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.resolutionItemId = _data["resolutionItemId"];
            this.boardMemberId = _data["boardMemberId"];
            this.conflictNotes = _data["conflictNotes"];
            this.boardMemberName = _data["boardMemberName"];
            this.userName = _data["userName"];
            this.memberType = _data["memberType"];
        }
    }

    static fromJS(data: any): ResolutionItemConflictDto {
        data = typeof data === 'object' ? data : {};
        let result = new ResolutionItemConflictDto();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["resolutionItemId"] = this.resolutionItemId;
        data["boardMemberId"] = this.boardMemberId;
        data["conflictNotes"] = this.conflictNotes;
        data["boardMemberName"] = this.boardMemberName;
        data["userName"] = this.userName;
        data["memberType"] = this.memberType;
        return data;
    }
}

export interface IResolutionItemConflictDto {
    id: number;
    resolutionItemId: number;
    boardMemberId: number;
    conflictNotes: string | undefined;
    boardMemberName: string | undefined;
    userName: string | undefined;
    memberType: string | undefined;
}

export class ResolutionItemDto implements IResolutionItemDto {
    id!: number;
    resolutionId!: number;
    title!: string | undefined;
    description!: string | undefined;
    hasConflict!: boolean;
    displayOrder!: number;
    conflictMembers!: ResolutionItemConflictDto[] | undefined;
    conflictMembersCount!: number;

    constructor(data?: IResolutionItemDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.resolutionId = _data["resolutionId"];
            this.title = _data["title"];
            this.description = _data["description"];
            this.hasConflict = _data["hasConflict"];
            this.displayOrder = _data["displayOrder"];
            if (Array.isArray(_data["conflictMembers"])) {
                this.conflictMembers = [] as any;
                for (let item of _data["conflictMembers"])
                    this.conflictMembers!.push(ResolutionItemConflictDto.fromJS(item));
            }
            this.conflictMembersCount = _data["conflictMembersCount"];
        }
    }

    static fromJS(data: any): ResolutionItemDto {
        data = typeof data === 'object' ? data : {};
        let result = new ResolutionItemDto();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["resolutionId"] = this.resolutionId;
        data["title"] = this.title;
        data["description"] = this.description;
        data["hasConflict"] = this.hasConflict;
        data["displayOrder"] = this.displayOrder;
        if (Array.isArray(this.conflictMembers)) {
            data["conflictMembers"] = [];
            for (let item of this.conflictMembers)
                data["conflictMembers"].push(item ? item.toJSON() : <any>undefined);
        }
        data["conflictMembersCount"] = this.conflictMembersCount;
        return data;
    }
}

export interface IResolutionItemDto {
    id: number;
    resolutionId: number;
    title: string | undefined;
    description: string | undefined;
    hasConflict: boolean;
    displayOrder: number;
    conflictMembers: ResolutionItemConflictDto[] | undefined;
    conflictMembersCount: number;
}

export class ResolutionStatusDto implements IResolutionStatusDto {
    id!: number;
    nameAr!: string | undefined;
    nameEn!: string | undefined;
    readonly localizedName!: string | undefined;
    value!: ResolutionStatusEnum;
    description!: string | undefined;

    constructor(data?: IResolutionStatusDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.nameAr = _data["nameAr"];
            this.nameEn = _data["nameEn"];
            (<any>this).localizedName = _data["localizedName"];
            this.value = _data["value"];
            this.description = _data["description"];
        }
    }

    static fromJS(data: any): ResolutionStatusDto {
        data = typeof data === 'object' ? data : {};
        let result = new ResolutionStatusDto();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["nameAr"] = this.nameAr;
        data["nameEn"] = this.nameEn;
        data["localizedName"] = this.localizedName;
        data["value"] = this.value;
        data["description"] = this.description;
        return data;
    }
}

export interface IResolutionStatusDto {
    id: number;
    nameAr: string | undefined;
    nameEn: string | undefined;
    localizedName: string | undefined;
    value: ResolutionStatusEnum;
    description: string | undefined;
}

export class ResolutionStatusDtoListBaseResponse implements IResolutionStatusDtoListBaseResponse {
    statusCode!: HttpStatusCode;
    successed!: boolean;
    message!: string | undefined;
    data!: ResolutionStatusDto[] | undefined;
    errors!: any[] | undefined;

    constructor(data?: IResolutionStatusDtoListBaseResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.statusCode = _data["statusCode"];
            this.successed = _data["successed"];
            this.message = _data["message"];
            if (Array.isArray(_data["data"])) {
                this.data = [] as any;
                for (let item of _data["data"])
                    this.data!.push(ResolutionStatusDto.fromJS(item));
            }
            if (Array.isArray(_data["errors"])) {
                this.errors = [] as any;
                for (let item of _data["errors"])
                    this.errors!.push(item);
            }
        }
    }

    static fromJS(data: any): ResolutionStatusDtoListBaseResponse {
        data = typeof data === 'object' ? data : {};
        let result = new ResolutionStatusDtoListBaseResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["statusCode"] = this.statusCode;
        data["successed"] = this.successed;
        data["message"] = this.message;
        if (Array.isArray(this.data)) {
            data["data"] = [];
            for (let item of this.data)
                data["data"].push(item ? item.toJSON() : <any>undefined);
        }
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item);
        }
        return data;
    }
}

export interface IResolutionStatusDtoListBaseResponse {
    statusCode: HttpStatusCode;
    successed: boolean;
    message: string | undefined;
    data: ResolutionStatusDto[] | undefined;
    errors: any[] | undefined;
}

export enum ResolutionStatusEnum {
    _1 = 1,
    _2 = 2,
    _3 = 3,
    _4 = 4,
    _5 = 5,
    _6 = 6,
    _7 = 7,
    _8 = 8,
    _9 = 9,
    _10 = 10,
}

export class ResolutionTypeDto implements IResolutionTypeDto {
    id!: number;
    nameAr!: string | undefined;
    nameEn!: string | undefined;
    readonly localizedName!: string | undefined;
    isActive!: boolean;
    isOther!: boolean;
    displayOrder!: number;

    constructor(data?: IResolutionTypeDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.nameAr = _data["nameAr"];
            this.nameEn = _data["nameEn"];
            (<any>this).localizedName = _data["localizedName"];
            this.isActive = _data["isActive"];
            this.isOther = _data["isOther"];
            this.displayOrder = _data["displayOrder"];
        }
    }

    static fromJS(data: any): ResolutionTypeDto {
        data = typeof data === 'object' ? data : {};
        let result = new ResolutionTypeDto();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["nameAr"] = this.nameAr;
        data["nameEn"] = this.nameEn;
        data["localizedName"] = this.localizedName;
        data["isActive"] = this.isActive;
        data["isOther"] = this.isOther;
        data["displayOrder"] = this.displayOrder;
        return data;
    }
}

export interface IResolutionTypeDto {
    id: number;
    nameAr: string | undefined;
    nameEn: string | undefined;
    localizedName: string | undefined;
    isActive: boolean;
    isOther: boolean;
    displayOrder: number;
}

export class ResolutionTypeDtoIEnumerableBaseResponse implements IResolutionTypeDtoIEnumerableBaseResponse {
    statusCode!: HttpStatusCode;
    successed!: boolean;
    message!: string | undefined;
    data!: ResolutionTypeDto[] | undefined;
    errors!: any[] | undefined;

    constructor(data?: IResolutionTypeDtoIEnumerableBaseResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.statusCode = _data["statusCode"];
            this.successed = _data["successed"];
            this.message = _data["message"];
            if (Array.isArray(_data["data"])) {
                this.data = [] as any;
                for (let item of _data["data"])
                    this.data!.push(ResolutionTypeDto.fromJS(item));
            }
            if (Array.isArray(_data["errors"])) {
                this.errors = [] as any;
                for (let item of _data["errors"])
                    this.errors!.push(item);
            }
        }
    }

    static fromJS(data: any): ResolutionTypeDtoIEnumerableBaseResponse {
        data = typeof data === 'object' ? data : {};
        let result = new ResolutionTypeDtoIEnumerableBaseResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["statusCode"] = this.statusCode;
        data["successed"] = this.successed;
        data["message"] = this.message;
        if (Array.isArray(this.data)) {
            data["data"] = [];
            for (let item of this.data)
                data["data"].push(item ? item.toJSON() : <any>undefined);
        }
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item);
        }
        return data;
    }
}

export interface IResolutionTypeDtoIEnumerableBaseResponse {
    statusCode: HttpStatusCode;
    successed: boolean;
    message: string | undefined;
    data: ResolutionTypeDto[] | undefined;
    errors: any[] | undefined;
}

export class RoleClaims implements IRoleClaims {
    type!: string | undefined;
    value!: string | undefined;
    hasClaim!: boolean;

    constructor(data?: IRoleClaims) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.type = _data["type"];
            this.value = _data["value"];
            this.hasClaim = _data["hasClaim"];
        }
    }

    static fromJS(data: any): RoleClaims {
        data = typeof data === 'object' ? data : {};
        let result = new RoleClaims();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["type"] = this.type;
        data["value"] = this.value;
        data["hasClaim"] = this.hasClaim;
        return data;
    }
}

export interface IRoleClaims {
    type: string | undefined;
    value: string | undefined;
    hasClaim: boolean;
}

export class SaveFundCommand implements ISaveFundCommand {
    id!: number;
    name!: string | undefined;
    strategyId!: number;
    strategyName!: string | undefined;
    status!: string | undefined;
    statusId!: number | undefined;
    initiationDate!: DateTime;
    updatedAt!: DateTime | undefined;
    oldCode!: string | undefined;
    propertiesNumber!: number;
    attachmentId!: number;
    votingTypeId!: number;
    legalCouncilId!: number;
    exitDate!: DateTime | undefined;
    fundManagers!: number[] | undefined;
    fundBoardSecretaries!: number[] | undefined;

    constructor(data?: ISaveFundCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.name = _data["name"];
            this.strategyId = _data["strategyId"];
            this.strategyName = _data["strategyName"];
            this.status = _data["status"];
            this.statusId = _data["statusId"];
            this.initiationDate = _data["initiationDate"] ? DateTime.fromISO(_data["initiationDate"].toString()) : <any>undefined;
            this.updatedAt = _data["updatedAt"] ? DateTime.fromISO(_data["updatedAt"].toString()) : <any>undefined;
            this.oldCode = _data["oldCode"];
            this.propertiesNumber = _data["propertiesNumber"];
            this.attachmentId = _data["attachmentId"];
            this.votingTypeId = _data["votingTypeId"];
            this.legalCouncilId = _data["legalCouncilId"];
            this.exitDate = _data["exitDate"] ? DateTime.fromISO(_data["exitDate"].toString()) : <any>undefined;
            if (Array.isArray(_data["fundManagers"])) {
                this.fundManagers = [] as any;
                for (let item of _data["fundManagers"])
                    this.fundManagers!.push(item);
            }
            if (Array.isArray(_data["fundBoardSecretaries"])) {
                this.fundBoardSecretaries = [] as any;
                for (let item of _data["fundBoardSecretaries"])
                    this.fundBoardSecretaries!.push(item);
            }
        }
    }

    static fromJS(data: any): SaveFundCommand {
        data = typeof data === 'object' ? data : {};
        let result = new SaveFundCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["name"] = this.name;
        data["strategyId"] = this.strategyId;
        data["strategyName"] = this.strategyName;
        data["status"] = this.status;
        data["statusId"] = this.statusId;
        data["initiationDate"] = this.initiationDate ? this.initiationDate.toString() : <any>undefined;
        data["updatedAt"] = this.updatedAt ? this.updatedAt.toString() : <any>undefined;
        data["oldCode"] = this.oldCode;
        data["propertiesNumber"] = this.propertiesNumber;
        data["attachmentId"] = this.attachmentId;
        data["votingTypeId"] = this.votingTypeId;
        data["legalCouncilId"] = this.legalCouncilId;
        data["exitDate"] = this.exitDate ? this.exitDate.toString() : <any>undefined;
        if (Array.isArray(this.fundManagers)) {
            data["fundManagers"] = [];
            for (let item of this.fundManagers)
                data["fundManagers"].push(item);
        }
        if (Array.isArray(this.fundBoardSecretaries)) {
            data["fundBoardSecretaries"] = [];
            for (let item of this.fundBoardSecretaries)
                data["fundBoardSecretaries"].push(item);
        }
        return data;
    }
}

export interface ISaveFundCommand {
    id: number;
    name: string | undefined;
    strategyId: number;
    strategyName: string | undefined;
    status: string | undefined;
    statusId: number | undefined;
    initiationDate: DateTime;
    updatedAt: DateTime | undefined;
    oldCode: string | undefined;
    propertiesNumber: number;
    attachmentId: number;
    votingTypeId: number;
    legalCouncilId: number;
    exitDate: DateTime | undefined;
    fundManagers: number[] | undefined;
    fundBoardSecretaries: number[] | undefined;
}

export class SignInCommand implements ISignInCommand {
    userName!: string | undefined;
    password!: string | undefined;

    constructor(data?: ISignInCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.userName = _data["userName"];
            this.password = _data["password"];
        }
    }

    static fromJS(data: any): SignInCommand {
        data = typeof data === 'object' ? data : {};
        let result = new SignInCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["userName"] = this.userName;
        data["password"] = this.password;
        return data;
    }
}

export interface ISignInCommand {
    userName: string | undefined;
    password: string | undefined;
}

export class SignOutCommand implements ISignOutCommand {

    constructor(data?: ISignOutCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
    }

    static fromJS(data: any): SignOutCommand {
        data = typeof data === 'object' ? data : {};
        let result = new SignOutCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        return data;
    }
}

export interface ISignOutCommand {
}

export class SingleFundResponse implements ISingleFundResponse {
    id!: number;
    name!: string | undefined;
    strategyId!: number;
    strategyName!: string | undefined;
    status!: string | undefined;
    statusId!: number | undefined;
    initiationDate!: DateTime;
    updatedAt!: DateTime | undefined;
    exitDate!: DateTime | undefined;

    constructor(data?: ISingleFundResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.name = _data["name"];
            this.strategyId = _data["strategyId"];
            this.strategyName = _data["strategyName"];
            this.status = _data["status"];
            this.statusId = _data["statusId"];
            this.initiationDate = _data["initiationDate"] ? DateTime.fromISO(_data["initiationDate"].toString()) : <any>undefined;
            this.updatedAt = _data["updatedAt"] ? DateTime.fromISO(_data["updatedAt"].toString()) : <any>undefined;
            this.exitDate = _data["exitDate"] ? DateTime.fromISO(_data["exitDate"].toString()) : <any>undefined;
        }
    }

    static fromJS(data: any): SingleFundResponse {
        data = typeof data === 'object' ? data : {};
        let result = new SingleFundResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["name"] = this.name;
        data["strategyId"] = this.strategyId;
        data["strategyName"] = this.strategyName;
        data["status"] = this.status;
        data["statusId"] = this.statusId;
        data["initiationDate"] = this.initiationDate ? this.initiationDate.toString() : <any>undefined;
        data["updatedAt"] = this.updatedAt ? this.updatedAt.toString() : <any>undefined;
        data["exitDate"] = this.exitDate ? this.exitDate.toString() : <any>undefined;
        return data;
    }
}

export interface ISingleFundResponse {
    id: number;
    name: string | undefined;
    strategyId: number;
    strategyName: string | undefined;
    status: string | undefined;
    statusId: number | undefined;
    initiationDate: DateTime;
    updatedAt: DateTime | undefined;
    exitDate: DateTime | undefined;
}

export class SingleResolutionResponse implements ISingleResolutionResponse {
    id!: number;
    fundName!: string | undefined;
    code!: string | undefined;
    resolutionDate!: DateTime;
    description!: string | undefined;
    status!: ResolutionStatusEnum;
    resolutionType!: ResolutionTypeDto;
    lastUpdated!: DateTime | undefined;
    statusId!: number;
    resolutionStatus!: ResolutionStatusDto;
    canConfirm!: boolean;
    canReject!: boolean;
    canEdit!: boolean;
    canCancel!: boolean;
    canDelete!: boolean;

    constructor(data?: ISingleResolutionResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.fundName = _data["fundName"];
            this.code = _data["code"];
            this.resolutionDate = _data["resolutionDate"] ? DateTime.fromISO(_data["resolutionDate"].toString()) : <any>undefined;
            this.description = _data["description"];
            this.status = _data["status"];
            this.resolutionType = _data["resolutionType"] ? ResolutionTypeDto.fromJS(_data["resolutionType"]) : <any>undefined;
            this.lastUpdated = _data["lastUpdated"] ? DateTime.fromISO(_data["lastUpdated"].toString()) : <any>undefined;
            this.statusId = _data["statusId"];
            this.resolutionStatus = _data["resolutionStatus"] ? ResolutionStatusDto.fromJS(_data["resolutionStatus"]) : <any>undefined;
            this.canConfirm = _data["canConfirm"];
            this.canReject = _data["canReject"];
            this.canEdit = _data["canEdit"];
            this.canCancel = _data["canCancel"];
            this.canDelete = _data["canDelete"];
        }
    }

    static fromJS(data: any): SingleResolutionResponse {
        data = typeof data === 'object' ? data : {};
        let result = new SingleResolutionResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["fundName"] = this.fundName;
        data["code"] = this.code;
        data["resolutionDate"] = this.resolutionDate ? this.resolutionDate.toString() : <any>undefined;
        data["description"] = this.description;
        data["status"] = this.status;
        data["resolutionType"] = this.resolutionType ? this.resolutionType.toJSON() : <any>undefined;
        data["lastUpdated"] = this.lastUpdated ? this.lastUpdated.toString() : <any>undefined;
        data["statusId"] = this.statusId;
        data["resolutionStatus"] = this.resolutionStatus ? this.resolutionStatus.toJSON() : <any>undefined;
        data["canConfirm"] = this.canConfirm;
        data["canReject"] = this.canReject;
        data["canEdit"] = this.canEdit;
        data["canCancel"] = this.canCancel;
        data["canDelete"] = this.canDelete;
        return data;
    }
}

export interface ISingleResolutionResponse {
    id: number;
    fundName: string | undefined;
    code: string | undefined;
    resolutionDate: DateTime;
    description: string | undefined;
    status: ResolutionStatusEnum;
    resolutionType: ResolutionTypeDto;
    lastUpdated: DateTime | undefined;
    statusId: number;
    resolutionStatus: ResolutionStatusDto;
    canConfirm: boolean;
    canReject: boolean;
    canEdit: boolean;
    canCancel: boolean;
    canDelete: boolean;
}

export class SingleResolutionResponseBaseResponse implements ISingleResolutionResponseBaseResponse {
    statusCode!: HttpStatusCode;
    successed!: boolean;
    message!: string | undefined;
    data!: SingleResolutionResponse;
    errors!: any[] | undefined;

    constructor(data?: ISingleResolutionResponseBaseResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.statusCode = _data["statusCode"];
            this.successed = _data["successed"];
            this.message = _data["message"];
            this.data = _data["data"] ? SingleResolutionResponse.fromJS(_data["data"]) : <any>undefined;
            if (Array.isArray(_data["errors"])) {
                this.errors = [] as any;
                for (let item of _data["errors"])
                    this.errors!.push(item);
            }
        }
    }

    static fromJS(data: any): SingleResolutionResponseBaseResponse {
        data = typeof data === 'object' ? data : {};
        let result = new SingleResolutionResponseBaseResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["statusCode"] = this.statusCode;
        data["successed"] = this.successed;
        data["message"] = this.message;
        data["data"] = this.data ? this.data.toJSON() : <any>undefined;
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item);
        }
        return data;
    }
}

export interface ISingleResolutionResponseBaseResponse {
    statusCode: HttpStatusCode;
    successed: boolean;
    message: string | undefined;
    data: SingleResolutionResponse;
    errors: any[] | undefined;
}

export class SingleResolutionResponsePaginatedResult implements ISingleResolutionResponsePaginatedResult {
    statusCode!: HttpStatusCode;
    successed!: boolean;
    message!: string | undefined;
    data!: SingleResolutionResponse[] | undefined;
    errors!: any[] | undefined;
    currentPage!: number;
    totalCount!: number;
    totalPages!: number;
    pageSize!: number;
    readonly hasPreviousPage!: boolean;
    readonly hasNextPage!: boolean;

    constructor(data?: ISingleResolutionResponsePaginatedResult) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.statusCode = _data["statusCode"];
            this.successed = _data["successed"];
            this.message = _data["message"];
            if (Array.isArray(_data["data"])) {
                this.data = [] as any;
                for (let item of _data["data"])
                    this.data!.push(SingleResolutionResponse.fromJS(item));
            }
            if (Array.isArray(_data["errors"])) {
                this.errors = [] as any;
                for (let item of _data["errors"])
                    this.errors!.push(item);
            }
            this.currentPage = _data["currentPage"];
            this.totalCount = _data["totalCount"];
            this.totalPages = _data["totalPages"];
            this.pageSize = _data["pageSize"];
            (<any>this).hasPreviousPage = _data["hasPreviousPage"];
            (<any>this).hasNextPage = _data["hasNextPage"];
        }
    }

    static fromJS(data: any): SingleResolutionResponsePaginatedResult {
        data = typeof data === 'object' ? data : {};
        let result = new SingleResolutionResponsePaginatedResult();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["statusCode"] = this.statusCode;
        data["successed"] = this.successed;
        data["message"] = this.message;
        if (Array.isArray(this.data)) {
            data["data"] = [];
            for (let item of this.data)
                data["data"].push(item ? item.toJSON() : <any>undefined);
        }
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item);
        }
        data["currentPage"] = this.currentPage;
        data["totalCount"] = this.totalCount;
        data["totalPages"] = this.totalPages;
        data["pageSize"] = this.pageSize;
        data["hasPreviousPage"] = this.hasPreviousPage;
        data["hasNextPage"] = this.hasNextPage;
        return data;
    }
}

export interface ISingleResolutionResponsePaginatedResult {
    statusCode: HttpStatusCode;
    successed: boolean;
    message: string | undefined;
    data: SingleResolutionResponse[] | undefined;
    errors: any[] | undefined;
    currentPage: number;
    totalCount: number;
    totalPages: number;
    pageSize: number;
    hasPreviousPage: boolean;
    hasNextPage: boolean;
}

export class StrategyDto implements IStrategyDto {
    id!: number;
    nameAr!: string | undefined;
    nameEn!: string | undefined;
    updatedAt!: DateTime | undefined;

    constructor(data?: IStrategyDto) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.nameAr = _data["nameAr"];
            this.nameEn = _data["nameEn"];
            this.updatedAt = _data["updatedAt"] ? DateTime.fromISO(_data["updatedAt"].toString()) : <any>undefined;
        }
    }

    static fromJS(data: any): StrategyDto {
        data = typeof data === 'object' ? data : {};
        let result = new StrategyDto();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["nameAr"] = this.nameAr;
        data["nameEn"] = this.nameEn;
        data["updatedAt"] = this.updatedAt ? this.updatedAt.toString() : <any>undefined;
        return data;
    }
}

export interface IStrategyDto {
    id: number;
    nameAr: string | undefined;
    nameEn: string | undefined;
    updatedAt: DateTime | undefined;
}

export class StrategyDtoBaseResponse implements IStrategyDtoBaseResponse {
    statusCode!: HttpStatusCode;
    successed!: boolean;
    message!: string | undefined;
    data!: StrategyDto;
    errors!: any[] | undefined;

    constructor(data?: IStrategyDtoBaseResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.statusCode = _data["statusCode"];
            this.successed = _data["successed"];
            this.message = _data["message"];
            this.data = _data["data"] ? StrategyDto.fromJS(_data["data"]) : <any>undefined;
            if (Array.isArray(_data["errors"])) {
                this.errors = [] as any;
                for (let item of _data["errors"])
                    this.errors!.push(item);
            }
        }
    }

    static fromJS(data: any): StrategyDtoBaseResponse {
        data = typeof data === 'object' ? data : {};
        let result = new StrategyDtoBaseResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["statusCode"] = this.statusCode;
        data["successed"] = this.successed;
        data["message"] = this.message;
        data["data"] = this.data ? this.data.toJSON() : <any>undefined;
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item);
        }
        return data;
    }
}

export interface IStrategyDtoBaseResponse {
    statusCode: HttpStatusCode;
    successed: boolean;
    message: string | undefined;
    data: StrategyDto;
    errors: any[] | undefined;
}

export class StrategyDtoPaginatedResult implements IStrategyDtoPaginatedResult {
    statusCode!: HttpStatusCode;
    successed!: boolean;
    message!: string | undefined;
    data!: StrategyDto[] | undefined;
    errors!: any[] | undefined;
    currentPage!: number;
    totalCount!: number;
    totalPages!: number;
    pageSize!: number;
    readonly hasPreviousPage!: boolean;
    readonly hasNextPage!: boolean;

    constructor(data?: IStrategyDtoPaginatedResult) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.statusCode = _data["statusCode"];
            this.successed = _data["successed"];
            this.message = _data["message"];
            if (Array.isArray(_data["data"])) {
                this.data = [] as any;
                for (let item of _data["data"])
                    this.data!.push(StrategyDto.fromJS(item));
            }
            if (Array.isArray(_data["errors"])) {
                this.errors = [] as any;
                for (let item of _data["errors"])
                    this.errors!.push(item);
            }
            this.currentPage = _data["currentPage"];
            this.totalCount = _data["totalCount"];
            this.totalPages = _data["totalPages"];
            this.pageSize = _data["pageSize"];
            (<any>this).hasPreviousPage = _data["hasPreviousPage"];
            (<any>this).hasNextPage = _data["hasNextPage"];
        }
    }

    static fromJS(data: any): StrategyDtoPaginatedResult {
        data = typeof data === 'object' ? data : {};
        let result = new StrategyDtoPaginatedResult();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["statusCode"] = this.statusCode;
        data["successed"] = this.successed;
        data["message"] = this.message;
        if (Array.isArray(this.data)) {
            data["data"] = [];
            for (let item of this.data)
                data["data"].push(item ? item.toJSON() : <any>undefined);
        }
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item);
        }
        data["currentPage"] = this.currentPage;
        data["totalCount"] = this.totalCount;
        data["totalPages"] = this.totalPages;
        data["pageSize"] = this.pageSize;
        data["hasPreviousPage"] = this.hasPreviousPage;
        data["hasNextPage"] = this.hasNextPage;
        return data;
    }
}

export interface IStrategyDtoPaginatedResult {
    statusCode: HttpStatusCode;
    successed: boolean;
    message: string | undefined;
    data: StrategyDto[] | undefined;
    errors: any[] | undefined;
    currentPage: number;
    totalCount: number;
    totalPages: number;
    pageSize: number;
    hasPreviousPage: boolean;
    hasNextPage: boolean;
}

export class StringBaseResponse implements IStringBaseResponse {
    statusCode!: HttpStatusCode;
    successed!: boolean;
    message!: string | undefined;
    data!: string | undefined;
    errors!: any[] | undefined;

    constructor(data?: IStringBaseResponse) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.statusCode = _data["statusCode"];
            this.successed = _data["successed"];
            this.message = _data["message"];
            this.data = _data["data"];
            if (Array.isArray(_data["errors"])) {
                this.errors = [] as any;
                for (let item of _data["errors"])
                    this.errors!.push(item);
            }
        }
    }

    static fromJS(data: any): StringBaseResponse {
        data = typeof data === 'object' ? data : {};
        let result = new StringBaseResponse();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["statusCode"] = this.statusCode;
        data["successed"] = this.successed;
        data["message"] = this.message;
        data["data"] = this.data;
        if (Array.isArray(this.errors)) {
            data["errors"] = [];
            for (let item of this.errors)
                data["errors"].push(item);
        }
        return data;
    }
}

export interface IStringBaseResponse {
    statusCode: HttpStatusCode;
    successed: boolean;
    message: string | undefined;
    data: string | undefined;
    errors: any[] | undefined;
}

export class UpdateFCMTokenCommand implements IUpdateFCMTokenCommand {
    userId!: string | undefined;
    fcmWebToken!: string | undefined;

    constructor(data?: IUpdateFCMTokenCommand) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.userId = _data["userId"];
            this.fcmWebToken = _data["fcmWebToken"];
        }
    }

    static fromJS(data: any): UpdateFCMTokenCommand {
        data = typeof data === 'object' ? data : {};
        let result = new UpdateFCMTokenCommand();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["userId"] = this.userId;
        data["fcmWebToken"] = this.fcmWebToken;
        return data;
    }
}

export interface IUpdateFCMTokenCommand {
    userId: string | undefined;
    fcmWebToken: string | undefined;
}

export class UserRoles implements IUserRoles {
    id!: number;
    name!: string | undefined;
    hasRole!: boolean;

    constructor(data?: IUserRoles) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            this.id = _data["id"];
            this.name = _data["name"];
            this.hasRole = _data["hasRole"];
        }
    }

    static fromJS(data: any): UserRoles {
        data = typeof data === 'object' ? data : {};
        let result = new UserRoles();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        data["id"] = this.id;
        data["name"] = this.name;
        data["hasRole"] = this.hasRole;
        return data;
    }
}

export interface IUserRoles {
    id: number;
    name: string | undefined;
    hasRole: boolean;
}

export enum VotingType {
    _1 = 1,
    _2 = 2,
}

export class Body implements IBody {
    file!: string;

    [key: string]: any;

    constructor(data?: IBody) {
        if (data) {
            for (var property in data) {
                if (data.hasOwnProperty(property))
                    (<any>this)[property] = (<any>data)[property];
            }
        }
    }

    init(_data?: any) {
        if (_data) {
            for (var property in _data) {
                if (_data.hasOwnProperty(property))
                    this[property] = _data[property];
            }
            this.file = _data["File"];
        }
    }

    static fromJS(data: any): Body {
        data = typeof data === 'object' ? data : {};
        let result = new Body();
        result.init(data);
        return result;
    }

    toJSON(data?: any) {
        data = typeof data === 'object' ? data : {};
        for (var property in this) {
            if (this.hasOwnProperty(property))
                data[property] = this[property];
        }
        data["File"] = this.file;
        return data;
    }
}

export interface IBody {
    file: string;

    [key: string]: any;
}

export interface FileParameter {
    data: any;
    fileName: string;
}

export class ApiException extends Error {
    override message: string;
    status: number;
    response: string;
    headers: { [key: string]: any; };
    result: any;

    constructor(message: string, status: number, response: string, headers: { [key: string]: any; }, result: any) {
        super();

        this.message = message;
        this.status = status;
        this.response = response;
        this.headers = headers;
        this.result = result;
    }

    protected isApiException = true;

    static isApiException(obj: any): obj is ApiException {
        return obj.isApiException === true;
    }
}

function throwException(message: string, status: number, response: string, headers: { [key: string]: any; }, result?: any): Observable<any> {
    if (result !== null && result !== undefined)
        return _observableThrow(result);
    else
        return _observableThrow(new ApiException(message, status, response, headers, null));
}

function blobToText(blob: any): Observable<string> {
    return new Observable<string>((observer: any) => {
        if (!blob) {
            observer.next("");
            observer.complete();
        } else {
            let reader = new FileReader();
            reader.onload = event => {
                observer.next((event.target as any).result);
                observer.complete();
            };
            reader.readAsText(blob);
        }
    });
}