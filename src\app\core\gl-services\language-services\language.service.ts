import { EventEmitter, Injectable } from '@angular/core';
import { LanguageEnum } from '@core/enums/language-enum/language-enum';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class LanguageService {

  currentLanguageEvent = new EventEmitter<LanguageEnum>();
  private currentLangSubject = new BehaviorSubject<string>('ar');
currentLang$ = this.currentLangSubject.asObservable();

  constructor(private translate: TranslateService) {}

  initLang(){
    let currentLang = JSON.parse(localStorage.getItem("lang") || '{}');
    if (currentLang && Object.keys(currentLang).length === 0 && currentLang.constructor === Object){
      this.switchLang(LanguageEnum.ar);
    }
    else{
      this.switchLang(currentLang as LanguageEnum);
    }
  }

  setLanguage(lang: string) {
    localStorage.setItem('lang', lang);
    document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
    this.currentLangSubject.next(lang);
  }

  switchLang(language:LanguageEnum) {
    this.switchLangCallBack(language);
   // this.auth.switchLanguage(language).subscribe(res =>{});
  }

  switchLangCallBack(language:LanguageEnum) {
    this.translate.use(language).subscribe(res => {
      this.currentLanguageEvent.emit(language);
    })
    /*used for refreshment case*/
    localStorage.setItem('lang', JSON.stringify(language));

    if (language === LanguageEnum.ar){
      document.getElementsByTagName('html')[0].setAttribute('dir', 'rtl');
      document.getElementsByTagName('html')[0].setAttribute('lang', 'ar');
    }
    else{
      // document.getElementsByTagName('html')[0].removeAttribute('dir');
      document.getElementsByTagName('html')[0].setAttribute('dir', 'ltr');
      document.getElementsByTagName('html')[0].setAttribute('lang', 'en');
    }

  }
}
