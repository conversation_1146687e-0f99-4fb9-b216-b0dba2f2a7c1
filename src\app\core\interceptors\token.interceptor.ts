import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { Router } from '@angular/router';
import { SpinnerService } from '@core/gl-services/spinner-services/spinner.service';
import { catchError, finalize, throwError } from 'rxjs';

export const tokenInterceptor: HttpInterceptorFn = (req, next) => {
  const router = inject(Router);
  const loader = inject(SpinnerService);

  const token = localStorage.getItem('auth_token');
  const lang = localStorage.getItem('lang') || 'ar';
  let language = lang === 'en' ? 'en-US' : 'ar-EG';

  // Start loader
  loader.show();

  req = req.clone({
    setHeaders: {
      ...(token && { Authorization: `Bearer ${token}` }),
      'Accept-Language': language,
    },
  });

  return next(req).pipe(
    catchError((error) => {
      if (error.status === 401) {
        localStorage.removeItem('auth_token');
        localStorage.removeItem('refresh_token');
        router.navigate(['/auth/login']);
      }
      return throwError(() => error);
    }),
    finalize(() => {
      loader.hide();
    })
  );
};
