<header class="container-fluid header-padding">
    <div class="d-flex justify-content-between align-items-center">
        <!-- Right side: Logo -->
        <div class="header-logo">
            <img src="assets/images/Logo.png" alt="Jadwa Investment" class="d-lg-none" />
        </div>

        <!-- Left side: Controls -->
        <div class="d-flex align-items-center gap-4">


            <!-- Notification -->
            <div class="position-relative">
                <div class="card-header">
             
                <div class="notification-container">
                  <img src="assets/images/notification.png" />
                  <span class="notification-badge" >
                    {{notificationCount}}
                     </span>
                </div>
                </div>
                  <!-- <svg width="25" height="28" viewBox="0 0 25 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M12.25 6.7085C9.00507 6.7085 6.32788 9.67181 6.15733 13.4523C6.15061 13.6014 6.14525 13.7593 6.13968 13.9234C6.11752 14.5767 6.09199 15.3291 5.96332 16.0204C5.79263 16.9373 5.4282 17.8547 4.62213 18.56C4.23049 18.9026 4 19.4405 4 20.0116C4 20.7291 4.47641 21.2918 5.05 21.2918H19.45C20.0236 21.2918 20.5 20.7291 20.5 20.0116C20.5 19.4405 20.2695 18.9027 19.8779 18.56C19.0718 17.8547 18.7074 16.9373 18.5367 16.0204C18.408 15.3291 18.3825 14.5767 18.3603 13.9234C18.3547 13.7593 18.3494 13.6014 18.3427 13.4523C18.1721 9.67182 15.4949 6.7085 12.25 6.7085ZM4.6594 13.3604C4.87188 8.65037 8.20728 4.9585 12.25 4.9585C16.2927 4.9585 19.6281 8.65037 19.8406 13.3604C19.8497 13.5617 19.8564 13.7544 19.8629 13.9401C19.8851 14.5719 19.9044 15.1219 20.0025 15.649C20.1198 16.2788 20.3367 16.7739 20.7779 17.16C21.5472 17.8331 22 18.8896 22 20.0116C22 21.6231 20.9128 23.0418 19.45 23.0418H5.05C3.58719 23.0418 2.5 21.6231 2.5 20.0116C2.5 18.8896 2.95278 17.8331 3.72213 17.16C4.16329 16.7739 4.38025 16.2788 4.49749 15.649C4.59561 15.1219 4.6149 14.5719 4.63705 13.9401C4.64356 13.7544 4.65032 13.5617 4.6594 13.3604Z"
                          fill="#00205A" />
                      <g opacity="0.4">
                          <path fill-rule="evenodd" clip-rule="evenodd"
                              d="M12.25 1.4585C11.767 1.4585 11.2237 1.55528 10.7786 1.89522C10.2848 2.27235 10 2.8828 10 3.646C10 4.34152 10.233 5.06787 10.5874 5.62416C10.9362 6.1717 11.5085 6.7085 12.25 6.7085C12.9915 6.7085 13.5638 6.1717 13.9126 5.62416C14.267 5.06787 14.5 4.34152 14.5 3.646C14.5 2.8828 14.2152 2.27235 13.7214 1.89522C13.2763 1.55528 12.733 1.4585 12.25 1.4585ZM11.5 3.646C11.5 3.52473 11.5204 3.46546 11.5316 3.44052C11.5423 3.41691 11.5598 3.3902 11.6001 3.3594C11.6979 3.28471 11.9046 3.2085 12.25 3.2085C12.5954 3.2085 12.8021 3.28471 12.8999 3.3594C12.9402 3.3902 12.9577 3.41691 12.9684 3.44052C12.9796 3.46546 13 3.52473 13 3.646C13 3.91697 12.8972 4.28437 12.7087 4.5802C12.5147 4.88479 12.3369 4.9585 12.25 4.9585C12.1631 4.9585 11.9853 4.88479 11.7913 4.5802C11.6028 4.28437 11.5 3.91697 11.5 3.646Z"
                              fill="#00205A" />
                          <path
                              d="M10 22.1668C10 21.6836 9.66421 21.2918 9.25 21.2918C8.83579 21.2918 8.5 21.6836 8.5 22.1668C8.5 24.5831 10.1789 26.5418 12.25 26.5418C14.3211 26.5418 16 24.5831 16 22.1668C16 21.6836 15.6642 21.2918 15.25 21.2918C14.8358 21.2918 14.5 21.6836 14.5 22.1668C14.5 23.6166 13.4926 24.7918 12.25 24.7918C11.0074 24.7918 10 23.6166 10 22.1668Z"
                              fill="#00205A" />
                      </g>
                  </svg> -->
             
                  <span class="position-absolute notification-badge bg-danger border border-light rounded-circle">
                      <span class="visually-hidden">New alerts</span>
                  </span>
              </div>

            <!-- User Info -->
            <div class="d-flex align-items-center gap-2">
                <img src="assets/images/5a88f6c30078d932a34b61c983a4185389144193.jpg" class="rounded-circle" alt="User"
                    width="32" height="32" />
                <div class="d-flex align-items-center gap-1">
                    <span class="fw-medium text-primary fs-14 d-none d-lg-block">
                        {{roleName}}
                    </span>
                    <img src="assets/images/down.png" alt="Dropdown" />

                </div>
            </div>

            <!-- Language Dropdown -->
            <div class="dropdown d-none d-lg-block">
                <button class="btn lang-btn rounded-pill" (click)="changeLanguage()" type="button">
                    <div class="d-flex">

                        <img src="assets/images/ar.png" alt="Language" class="m-1" />
                        <span> {{'LOGIN_PAGE.LANG' | translate}}
                        </span>
                    </div>
                </button>
            </div>

            <!-- Menu Toggle for Mobile -->
            <button class="btn d-lg-none" (click)="toggleSidenav()">
                <img src="assets/images/menu.png" alt="Menu" />
            </button>
        </div>
    </div>

    <!-- Dashboard Title -->
    <div class="mt-3" *ngIf="isDashboard()">
        <h3 class="header">{{'DASHBOARD.DASHBOARD'| translate }}</h3>
    </div>
</header>
