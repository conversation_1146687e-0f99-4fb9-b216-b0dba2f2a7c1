@import "../../../../../../assets/scss/variables";

.header-logo {
  img {
    height: 32px;
    width: auto;
  }
}

.header {
  color: $navy-blue;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}
.header-padding{
  padding-top: 2rem;
}
// Mobile styles
@media (max-width: 991.98px) {
}

.notification-badge {
  width: 8px;
  height: 8px;
}

.search-container {
  position: relative;

  .search-input {
    height: 40px;
    padding-right: 40px;
    padding-left: 80px;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    font-size: 14px;
    direction: rtl;
    &:focus {
      background-color: #fff;
      border-color: $navy-blue;
      box-shadow: 0 0 0 0.2rem rgba(0, 32, 90, 0.1);
    }
  }

  .filter-btn,
  .search-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    color: #757575;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      color: $navy-blue;
    }
  }

  .filter-btn {
    right: 8px;
  }

  .search-btn {
    left: 8px;
  }
}

.create-fund-btn {
  height: 40px;
  padding: 0 16px;
  background-color: $navy-blue;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  white-space: nowrap;

  @media (max-width: 768px) {
    width: 100%;
    justify-content: center;
  }

  i {
    font-size: 12px;
  }

  &:hover {
    background-color: darken($navy-blue, 5%);
  }
}

.notification-badge {
  position: absolute;
  top: 6px;
  right: 16px;
  width: 8px;
  height: 8px;
  background-color: $notification-badg;
  border-radius: 50%;
  border: 2px solid white;
}

.user-avatar {
  width: 32px;
  height: 32px;
  object-fit: cover;
}

.dropdown-icon {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.lang-btn {
  display: flex;
  align-items: self-start;
  gap: 8px;
  font-size: 20px;
  border-color: #00205a;
  padding: 3px 12px;
  border: 1px solid;
  height: 32px;

  span {
    line-height: 1rem;
  }
}

.menu-toggle {
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: white;

  &:hover {
    background-color: #f8f9fa;
  }

  img {
    width: 24px;
    height: 24px;
  }
}
