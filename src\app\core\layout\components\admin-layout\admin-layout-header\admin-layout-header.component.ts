import { TokenService } from 'src/app/features/auth/services/token.service';
import { Component, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { LanguageService } from '@core/gl-services/language-services/language.service';
import { LanguageEnum } from '@core/enums/language-enum/language-enum';
import { NotificationServiceProxy } from '@core/api/api.generated';



@Component({
  selector: 'app-admin-layout-header',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    FormsModule
  ],
  templateUrl: './admin-layout-header.component.html',
  styleUrls: ['./admin-layout-header.component.scss']
})
export class AdminLayoutHeaderComponent {
  @Output() menuToggle = new EventEmitter<void>();
  @Output() searchEvent = new EventEmitter<string>();

  storedRoles: string | null;
  notificationCount = 0;
  roleName: any;

  // Search properties
  searchQuery: string = '';

  currentLang: LanguageEnum = LanguageEnum.ar;

  constructor(private router: Router ,private languageService:LanguageService ,
     private TokenService:TokenService,
     private notificationServiceProxy : NotificationServiceProxy) {
    this.initializeCurrentLanguage();
    this.languageService.currentLanguageEvent.subscribe((lang: LanguageEnum) => {
      this.currentLang = lang;
    });
     this.storedRoles = localStorage.getItem('roles');
      this.roleName =this.TokenService.getroles();
     this.getUserNotificationUnreaded();
  }
  private initializeCurrentLanguage(): void {
    try {
      const storedLang = localStorage.getItem('lang');
      if (storedLang && storedLang !== '{}') {
        this.currentLang = JSON.parse(storedLang) as LanguageEnum;
      } else {
        this.currentLang = LanguageEnum.ar; // Default to Arabic
      }
    } catch (e) {
      console.error('Error parsing stored language:', e);
      this.currentLang = LanguageEnum.ar; // Default to Arabic
    }
  }
  changeLanguage(): void {
    const newLang = this.currentLang === LanguageEnum.en ? LanguageEnum.ar : LanguageEnum.en;
    this.currentLang = newLang;
    this.languageService.switchLang(this.currentLang,true);
  }
  getUserNotificationUnreaded()
  {
    this.notificationServiceProxy.notitficationList().subscribe((res) => {
      this.notificationCount = res.data;
    });
  }
  toggleSidenav() {
    this.menuToggle.emit();
  }
  isDashboard(): boolean {
    return this.router.url.includes('/admin/dashboard');
  }
  onSearch(): void {
    if (this.searchQuery.trim()) {
      this.searchEvent.emit(this.searchQuery);
    }
  }
}
