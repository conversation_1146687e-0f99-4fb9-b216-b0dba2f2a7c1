import { TokenService } from 'src/app/features/auth/services/token.service';
import { Component, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { LanguageService } from '@core/gl-services/language-services/language.service';
import { LanguageEnum } from '@core/enums/language-enum/language-enum';
import { NotificationServiceProxy } from '@core/api/api.generated';

@Component({
  selector: 'app-admin-layout-header',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule
  ],
  templateUrl: './admin-layout-header.component.html',
  styleUrls: ['./admin-layout-header.component.scss']
})
export class AdminLayoutHeaderComponent {
  @Output() menuToggle = new EventEmitter<void>();
  storedRoles: string | null;
  notificationCount = 0;
  roleName : any;


  currentLang =
    (localStorage.getItem('lang') as LanguageEnum) || LanguageEnum.ar;
  constructor(private router: Router ,private languageService:LanguageService ,
     private TokenService:TokenService,
     private notificationServiceProxy : NotificationServiceProxy) {

     this.storedRoles = localStorage.getItem('roles');
      // this.roleName = JSON.parse(this.storedRoles || '');
      this.roleName =this.TokenService.getroles();
     this.getUserNotificationUnreaded();

  }
  getUserNotificationUnreaded()
  {
    this.notificationServiceProxy.unReadedNotificationList(1,5,undefined,'CreatedAt Desc').subscribe((res) => {

      this.notificationCount = res.totalCount;
    });
  }
  toggleSidenav() {
    this.menuToggle.emit();
  }

  isDashboard(): boolean {
    return this.router.url.includes('/admin/dashboard');
  }

  changeLanguage(): void {
debugger
    this.currentLang =
      this.currentLang === LanguageEnum.en ? LanguageEnum.ar : LanguageEnum.en;
    this.languageService.switchLang(this.currentLang);

  }

}
