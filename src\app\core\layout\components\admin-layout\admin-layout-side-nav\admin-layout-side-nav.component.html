<!-- Side Navigation -->
<nav class="sidenav">
    <!-- Logo -->
    <div class="logo-container">
        <img src="assets/images/WhiteLogo.png" alt="Jadwa Investment" class="logo" />
    </div>
    <hr class="wight-hr">
    <!-- Navigation Links -->
    <ul class="nav-links">
        <!-- <li class="nav-item" routerLinkActive="active">
            <a [routerLink]="['/admin/dashboard']" class="nav-link">
                <svg width="30" height="28" viewBox="0 0 30 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M15.5078 1.2207L29.3203 13.4082C29.6758 13.7129 29.6758 14.2207 29.4219 14.5762C29.1172 14.9316 28.6094 14.9316 28.2539 14.627L26.375 12.9512V22.9551C26.375 25.2402 24.5469 27.0176 22.3125 27.0176H7.6875C5.40234 27.0176 3.625 25.2402 3.625 22.9551V12.9512L1.69531 14.627C1.39062 14.9316 0.832031 14.9316 0.578125 14.5762C0.273438 14.2207 0.273438 13.7129 0.628906 13.4082L14.4414 1.2207C14.7461 0.966797 15.2031 0.966797 15.5078 1.2207ZM5.25 22.9551C5.25 24.3262 6.31641 25.3926 7.6875 25.3926H10.9375V17.2676C10.9375 16.4043 11.6484 15.6426 12.5625 15.6426H17.4375C18.3008 15.6426 19.0625 16.4043 19.0625 17.2676V25.3926H22.3125C23.6328 25.3926 24.75 24.3262 24.75 22.9551V11.5293L15 2.94727L5.25 11.5293V22.9551ZM12.5625 25.3926H17.4375V17.2676H12.5625V25.3926Z"
                        fill="white" />
                </svg>

                <span>{{ 'sidebar.dashboard' | translate }}</span>
            </a>
        </li> -->
        <li class="nav-item" routerLinkActive="active">
            <a [routerLink]="['/admin/investment-funds']" class="nav-link">
                <svg width="26" height="24" viewBox="0 0 26 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M1.625 15.6738V20.1426C1.625 21.0566 2.33594 21.7676 3.25 21.7676H22.75C23.6133 21.7676 24.375 21.0566 24.375 20.1426V15.6738C24.375 15.5723 24.3242 15.4199 24.3242 15.3184V15.2676H19.1445L17.9766 17.6543C17.7227 18.2129 17.1641 18.5176 16.5547 18.5176H9.39453C8.78516 18.5176 8.22656 18.2129 7.97266 17.6543L6.80469 15.2676H1.67578V15.3184C1.625 15.4199 1.625 15.5723 1.625 15.6738ZM21.3789 3.53711C21.1758 2.77539 20.5156 2.26758 19.8047 2.26758H6.19531C5.43359 2.26758 4.77344 2.77539 4.62109 3.53711L2.08203 13.6426H6.80469C7.41406 13.6426 7.97266 13.998 8.22656 14.5566L9.39453 16.8926H16.5547L17.7227 14.5566C17.9766 13.998 18.5352 13.6426 19.1445 13.6426H23.918L21.3789 3.53711ZM0 20.1426V15.6738C0 15.4199 0 15.166 0.0507812 14.9121L2.99609 3.13086C3.40234 1.6582 4.67188 0.642578 6.19531 0.642578H19.8047C21.2773 0.642578 22.5977 1.6582 22.9531 3.13086L25.8984 14.9121C25.9492 15.166 26 15.4199 26 15.6738V20.1426C26 21.9707 24.5273 23.3926 22.75 23.3926H3.25C1.42188 23.3926 0 21.9707 0 20.1426ZM8.9375 6.33008H17.0625C17.4688 6.33008 17.875 6.73633 17.875 7.14258C17.875 7.59961 17.4688 7.95508 17.0625 7.95508H8.9375C8.48047 7.95508 8.125 7.59961 8.125 7.14258C8.125 6.73633 8.48047 6.33008 8.9375 6.33008ZM7.3125 10.3926H18.6875C19.0938 10.3926 19.5 10.7988 19.5 11.2051C19.5 11.6621 19.0938 12.0176 18.6875 12.0176H7.3125C6.85547 12.0176 6.5 11.6621 6.5 11.2051C6.5 10.7988 6.85547 10.3926 7.3125 10.3926Z"
                        fill="white" />
                </svg>


                <span>{{ 'sidebar.funds' | translate }}</span>
            </a>
        </li>
        <li class="nav-item">
            <a href="#" class="nav-link">
                <svg width="28" height="29" viewBox="0 0 28 29" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M12.8333 8.75684H24.4999C24.9829 8.75684 25.3749 8.36484 25.3749 7.88184C25.3749 7.39884 24.9829 7.00684 24.4999 7.00684H12.8333C12.3503 7.00684 11.9583 7.39884 11.9583 7.88184C11.9583 8.36484 12.3503 8.75684 12.8333 8.75684Z"
                        fill="white" />
                    <path
                        d="M12.8333 22.7568H24.4999C24.9829 22.7568 25.3749 22.3648 25.3749 21.8818C25.3749 21.3988 24.9829 21.0068 24.4999 21.0068H12.8333C12.3503 21.0068 11.9583 21.3988 11.9583 21.8818C11.9583 22.3648 12.3503 22.7568 12.8333 22.7568Z"
                        fill="white" />
                    <path
                        d="M5.24856 12.2571C4.92423 12.2571 4.62556 12.0774 4.47389 11.7881C4.00867 10.8984 3.03073 10.2466 3.01908 10.2388C2.61658 9.97279 2.50339 9.4314 2.76823 9.02773C3.03306 8.62407 3.57323 8.51094 3.97689 8.77344C4.01073 8.7956 4.51823 9.13045 5.06307 9.68695C5.81207 8.18778 7.17823 5.8626 8.86873 4.80677C9.27823 4.55127 9.81839 4.67492 10.0751 5.08442C10.3317 5.49392 10.2069 6.0341 9.79623 6.2896C8.08589 7.35827 6.52022 10.5619 6.06172 11.7064C5.93456 12.0237 5.63473 12.2373 5.29406 12.2548H5.24973L5.24856 12.2571Z"
                        fill="white" />
                    <path
                        d="M24.5 15.7572H12.8333C12.3503 15.7572 11.9583 15.3652 11.9583 14.8822C11.9583 14.3992 12.3503 14.0072 12.8333 14.0072H24.5C24.983 14.0072 25.375 14.3992 25.375 14.8822C25.375 15.3652 24.983 15.7572 24.5 15.7572Z"
                        fill="white" />
                    <path
                        d="M4.47389 24.6214C4.62556 24.9107 4.92423 25.0904 5.24856 25.0904L5.24973 25.0881H5.29406C5.63473 25.0706 5.93456 24.8571 6.06172 24.5397C6.52022 23.3952 8.08589 20.1916 9.79623 19.1229C10.2069 18.8674 10.3317 18.3273 10.0751 17.9178C9.81839 17.5083 9.27823 17.3846 8.86873 17.6401C7.17823 18.6959 5.81207 21.0211 5.06307 22.5203C4.51823 21.9638 4.01073 21.6289 3.97689 21.6068C3.57323 21.3443 3.03306 21.4574 2.76823 21.8611C2.50339 22.2647 2.61656 22.8061 3.01906 23.0721C3.03101 23.0801 4.00874 23.7319 4.47389 24.6214Z"
                        fill="white" />
                </svg>

                <span>{{ 'sidebar.requests' | translate }}</span>
                <ng-container *ngIf="!isLoading && !hasError">
                    <!-- <span class="badge" *ngIf="requestsCount > 0">{{ requestsCount }}</span> -->
                </ng-container>
                <span class="loading-indicator" *ngIf="isLoading">
                    <i class="fas fa-spinner fa-spin"></i>
                </span>
            </a>
        </li>
        <li class="nav-item" routerLinkActive="active">
            <a [routerLink]="['/admin/user-management']" class="nav-link">
                <svg width="29" height="30" viewBox="0 0 29 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M6.6458 7.11133C4.47694 7.11133 2.71872 8.86954 2.71872 11.0384C2.71872 13.2073 4.47694 14.9655 6.6458 14.9655C7.14631 14.9655 7.55205 14.5598 7.55205 14.0592C7.55205 13.5587 7.14631 13.153 6.6458 13.153C5.47795 13.153 4.53122 12.2063 4.53122 11.0384C4.53122 9.87056 5.47795 8.92383 6.6458 8.92383C7.14631 8.92383 7.55205 8.51809 7.55205 8.01758C7.55205 7.51707 7.14631 7.11133 6.6458 7.11133Z"
                        fill="white" />
                    <path
                        d="M6.9442 16.3943C6.89911 15.8958 6.45846 15.5283 5.95998 15.5733C4.82593 15.6759 3.72327 16.1122 2.75141 16.8606C2.6954 16.9037 2.62162 16.9574 2.53493 17.0205C2.12326 17.32 1.42043 17.8313 0.94675 18.4309C0.644083 18.814 0.361044 19.3154 0.310085 19.9183C0.256717 20.5497 0.468388 21.1631 0.922186 21.7222C1.6168 22.5781 2.56993 23.4238 3.898 23.4238C4.39851 23.4238 4.80425 23.0181 4.80425 22.5176C4.80425 22.0171 4.39851 21.6113 3.898 21.6113C3.41526 21.6113 2.92809 21.3176 2.32949 20.58C2.12145 20.3237 2.1084 20.1626 2.11615 20.071C2.1263 19.9509 2.19019 19.7808 2.369 19.5544C2.67017 19.1732 3.09235 18.8629 3.5027 18.5613C3.62302 18.4729 3.74232 18.3852 3.85726 18.2967C4.57062 17.7473 5.34959 17.4485 6.12329 17.3785C6.62176 17.3334 6.9893 16.8927 6.9442 16.3943Z"
                        fill="white" />
                    <path
                        d="M21.7499 7.11133C21.2494 7.11133 20.8437 7.51707 20.8437 8.01758C20.8437 8.51809 21.2494 8.92383 21.7499 8.92383C22.9178 8.92383 23.8645 9.87056 23.8645 11.0384C23.8645 12.2063 22.9178 13.153 21.7499 13.153C21.2494 13.153 20.8437 13.5587 20.8437 14.0592C20.8437 14.5598 21.2494 14.9655 21.7499 14.9655C23.9188 14.9655 25.677 13.2073 25.677 11.0384C25.677 8.86954 23.9188 7.11133 21.7499 7.11133Z"
                        fill="white" />
                    <path
                        d="M23.0399 15.5733C22.5414 15.5283 22.1008 15.8958 22.0557 16.3943C22.0106 16.8927 22.3781 17.3334 22.8766 17.3785C23.6503 17.4485 24.4293 17.7473 25.1426 18.2967C25.2575 18.3852 25.3768 18.4728 25.4971 18.5612C25.9075 18.8628 26.3297 19.1732 26.6309 19.5544C26.8097 19.7808 26.8736 19.9509 26.8837 20.071C26.8915 20.1626 26.8784 20.3237 26.6704 20.58C26.0718 21.3176 25.5846 21.6113 25.1019 21.6113C24.6014 21.6113 24.1956 22.0171 24.1956 22.5176C24.1956 23.0181 24.6014 23.4238 25.1019 23.4238C26.43 23.4238 27.3831 22.5781 28.0777 21.7222C28.5315 21.1631 28.7432 20.5497 28.6898 19.9183C28.6388 19.3154 28.3558 18.814 28.0531 18.4309C27.5795 17.8313 26.8767 17.32 26.465 17.0205C26.3784 16.9575 26.3045 16.9037 26.2485 16.8606C25.2766 16.1122 24.174 15.6759 23.0399 15.5733Z"
                        fill="white" />
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M14.4999 4.69434C11.6637 4.69434 9.3645 6.99354 9.3645 9.82975C9.3645 12.666 11.6637 14.9652 14.4999 14.9652C17.3361 14.9652 19.6353 12.666 19.6353 9.82975C19.6353 6.99354 17.3361 4.69434 14.4999 4.69434ZM11.177 9.82975C11.177 7.99456 12.6647 6.50684 14.4999 6.50684C16.3351 6.50684 17.8228 7.99456 17.8228 9.82975C17.8228 11.6649 16.3351 13.1527 14.4999 13.1527C12.6647 13.1527 11.177 11.6649 11.177 9.82975Z"
                        fill="white" />
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M19.7088 18.2558C16.5215 16.285 12.4787 16.285 9.29141 18.2558C9.19743 18.3139 9.07842 18.3836 8.94166 18.4636C8.32266 18.8258 7.34006 19.4008 6.66955 20.0823C6.24842 20.5104 5.8284 21.0939 5.75182 21.8215C5.6699 22.5998 6.00026 23.3203 6.63048 23.9438C7.67161 24.9738 8.9613 25.8402 10.6523 25.8402H18.3479C20.0389 25.8402 21.3286 24.9738 22.3697 23.9438C22.9999 23.3203 23.3303 22.5998 23.2484 21.8215C23.1718 21.0939 22.7518 20.5104 22.3306 20.0823C21.6601 19.4008 20.6775 18.8258 20.0585 18.4636C19.9218 18.3836 19.8027 18.3139 19.7088 18.2558ZM10.2446 19.7974C12.8478 18.1878 16.1524 18.1878 18.7555 19.7974C18.9118 19.894 19.0794 19.9928 19.2523 20.0948C19.8708 20.4595 20.5574 20.8643 21.0386 21.3534C21.3346 21.6543 21.4314 21.874 21.4458 22.0112C21.4549 22.0976 21.4506 22.3035 21.095 22.6553C20.2098 23.531 19.3541 24.0277 18.3479 24.0277H10.6523C9.64607 24.0277 8.79038 23.531 7.90523 22.6553C7.54963 22.3035 7.54527 22.0976 7.55437 22.0112C7.56881 21.874 7.66557 21.6543 7.96161 21.3534C8.44278 20.8643 9.1294 20.4595 9.74791 20.0948C9.92079 19.9928 10.0884 19.894 10.2446 19.7974Z"
                        fill="white" />
                </svg>

                <span>{{ 'sidebar.users' | translate }}</span>
            </a>
        </li>
        <li class="nav-item">
            <a href="#" class="nav-link">
                <svg width="28" height="29" viewBox="0 0 28 29" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M13.9334 2.80957H14.0668C16.6226 2.80955 18.6301 2.80954 20.1974 3.02026C21.8035 3.2362 23.0778 3.68774 24.079 4.68897C25.0802 5.6902 25.5318 6.9645 25.7477 8.5706C25.9584 10.1379 25.9584 12.1454 25.9584 14.7012V14.8346C25.9584 17.3904 25.9584 19.3979 25.7477 20.9652C25.5318 22.5713 25.0802 23.8456 24.079 24.8468C23.0778 25.8481 21.8035 26.2996 20.1974 26.5155C18.6301 26.7263 16.6226 26.7263 14.0668 26.7262H13.9334C11.3776 26.7263 9.3701 26.7263 7.80278 26.5155C6.19667 26.2996 4.92238 25.8481 3.92115 24.8468C2.91992 23.8456 2.46838 22.5713 2.25244 20.9652C2.04172 19.3979 2.04173 17.3904 2.04175 14.8346V14.7012C2.04173 12.1454 2.04172 10.1379 2.25244 8.5706C2.46838 6.9645 2.91992 5.6902 3.92115 4.68897C4.92238 3.68774 6.19667 3.2362 7.80278 3.02026C9.3701 2.80954 11.3776 2.80955 13.9334 2.80957ZM8.03596 4.75466C6.62391 4.9445 5.78047 5.30452 5.15858 5.92641C4.5367 6.54829 4.17668 7.39174 3.98684 8.80378C3.79361 10.241 3.79175 12.1308 3.79175 14.7679C3.79175 17.405 3.79361 19.2948 3.98684 20.732C4.17668 22.1441 4.5367 22.9875 5.15858 23.6094C5.78047 24.2313 6.62391 24.5913 8.03596 24.7811C9.47318 24.9744 11.363 24.9762 14.0001 24.9762C16.6372 24.9762 18.527 24.9744 19.9642 24.7811C21.3762 24.5913 22.2197 24.2313 22.8416 23.6094C23.4635 22.9875 23.8235 22.1441 24.0133 20.732C24.2066 19.2948 24.2084 17.405 24.2084 14.7679C24.2084 12.1308 24.2066 10.241 24.0133 8.80378C23.8235 7.39174 23.4635 6.54829 22.8416 5.92641C22.2197 5.30452 21.3762 4.9445 19.9642 4.75466C18.527 4.56143 16.6372 4.55957 14.0001 4.55957C11.363 4.55957 9.47318 4.56143 8.03596 4.75466Z"
                        fill="white" />
                    <path
                        d="M8.16675 8.05957C7.6835 8.05957 7.29175 8.45132 7.29175 8.93457C7.29175 9.41782 7.6835 9.80957 8.16675 9.80957L9.33341 9.80957C9.81666 9.80957 10.2084 9.41782 10.2084 8.93457C10.2084 8.45132 9.81666 8.05957 9.33342 8.05957H8.16675Z"
                        fill="white" />
                    <path
                        d="M12.8334 8.05957C12.3502 8.05957 11.9584 8.45132 11.9584 8.93457C11.9584 9.41782 12.3502 9.80957 12.8334 9.80957L19.8334 9.80957C20.3167 9.80957 20.7084 9.41782 20.7084 8.93457C20.7084 8.45132 20.3167 8.05957 19.8334 8.05957H12.8334Z"
                        fill="white" />
                    <path
                        d="M7.29175 14.7679C7.29175 14.2847 7.6835 13.8929 8.16675 13.8929H9.33342C9.81666 13.8929 10.2084 14.2847 10.2084 14.7679C10.2084 15.2512 9.81666 15.6429 9.33341 15.6429H8.16675C7.6835 15.6429 7.29175 15.2512 7.29175 14.7679Z"
                        fill="white" />
                    <path
                        d="M8.16675 19.7262C7.6835 19.7262 7.29175 20.118 7.29175 20.6012C7.29175 21.0845 7.6835 21.4762 8.16675 21.4762L9.33341 21.4762C9.81666 21.4762 10.2084 21.0845 10.2084 20.6012C10.2084 20.118 9.81666 19.7262 9.33342 19.7262L8.16675 19.7262Z"
                        fill="white" />
                    <path
                        d="M11.9584 14.7679C11.9584 14.2847 12.3502 13.8929 12.8334 13.8929L19.8334 13.8929C20.3167 13.8929 20.7084 14.2847 20.7084 14.7679C20.7084 15.2512 20.3167 15.6429 19.8334 15.6429L12.8334 15.6429C12.3502 15.6429 11.9584 15.2512 11.9584 14.7679Z"
                        fill="white" />
                    <path
                        d="M12.8334 19.7262C12.3502 19.7262 11.9584 20.118 11.9584 20.6012C11.9584 21.0845 12.3502 21.4762 12.8334 21.4762L19.8334 21.4762C20.3167 21.4762 20.7084 21.0845 20.7084 20.6012C20.7084 20.118 20.3167 19.7262 19.8334 19.7262L12.8334 19.7262Z"
                        fill="white" />
                </svg>

                <span>{{ 'sidebar.reports' | translate }}</span>
            </a>
        </li>
        <li class="nav-item" *ngIf="tokenService.hasPermission('Strategy.List')">
            <button class="nav-link btn w-100 text-start d-flex justify-content-between" data-bs-toggle="collapse"
                data-bs-target="#settingsSubmenu" aria-expanded="false" aria-controls="settingsSubmenu" type="button">
                <div>
                    <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M12.3815 0.235355H13.7645C14.134 0.235309 14.4812 0.235266 14.7722 0.268555C15.0965 0.305648 15.4375 0.391432 15.7557 0.621215C16.0737 0.850895 16.2625 1.14756 16.3999 1.44355C16.5233 1.7094 16.633 2.03922 16.7499 2.39055L16.7623 2.42801L17.1452 3.57162C17.2557 3.85019 17.4491 4.08762 17.6986 4.25182L18.0514 4.45538C18.3467 4.60229 18.683 4.64415 19.005 4.57421L20.4061 4.17958L20.4465 4.16809C20.8292 4.05945 21.187 3.95785 21.4952 3.90892C21.8379 3.85451 22.2128 3.84756 22.6005 4.00525C22.9881 4.16287 23.2518 4.42946 23.4594 4.70746C23.6462 4.95758 23.8321 5.28016 24.0309 5.62539L24.6493 6.69861C24.8357 7.02184 25.0105 7.32516 25.1278 7.59599C25.2583 7.89758 25.3547 8.23806 25.3134 8.63073C25.2722 9.02344 25.1072 9.33645 24.9167 9.60429C24.7457 9.84478 24.5116 10.105 24.2622 10.3823L24.236 10.4114L24.2345 10.4131L23.0548 11.732C22.9074 11.9317 22.7533 12.3659 22.7533 12.7768C22.7533 13.1882 22.9074 13.6222 23.0548 13.822L24.236 15.1426L24.2622 15.1717C24.5116 15.4489 24.7457 15.7092 24.9167 15.9497C25.1072 16.2175 25.2722 16.5305 25.3134 16.9232C25.3547 17.3159 25.2583 17.6564 25.1278 17.958C25.0105 18.2288 24.8356 18.5322 24.6493 18.8554L24.031 19.9285C23.8321 20.2737 23.6463 20.5963 23.4594 20.8465C23.2518 21.1245 22.9881 21.3911 22.6005 21.5487C22.2128 21.7064 21.8379 21.6994 21.4952 21.645C21.187 21.5961 20.8292 21.4945 20.4465 21.3858L20.406 21.3743L19.0051 20.9797C18.683 20.9097 18.3466 20.9516 18.0513 21.0986L17.6986 21.3022C17.4491 21.4664 17.2558 21.7038 17.1453 21.9823L16.7628 23.1244L16.7499 23.1635C16.633 23.5148 16.5233 23.8447 16.3999 24.1105C16.2625 24.4065 16.0737 24.7032 15.7557 24.9328C15.4375 25.1626 15.0965 25.2484 14.7722 25.2855C14.4812 25.3188 14.134 25.3187 13.7645 25.3187H12.3816C12.0121 25.3187 11.6649 25.3188 11.3739 25.2855C11.0496 25.2484 10.7086 25.1626 10.3904 24.9328C10.0724 24.7032 9.88361 24.4065 9.74618 24.1105C9.62274 23.8446 9.51306 23.5148 9.39622 23.1635L9.38324 23.1244L9.00081 21.9823C8.89032 21.7038 8.69702 21.4664 8.44751 21.3022L8.09481 21.0986C7.79947 20.9516 7.46313 20.9097 7.14104 20.9797L5.74007 21.3743L5.69962 21.3858C5.31693 21.4945 4.95907 21.5961 4.65091 21.645C4.30819 21.6994 3.93326 21.7064 3.54557 21.5487C3.158 21.3911 2.89427 21.1245 2.68664 20.8465C2.49983 20.5963 2.31399 20.2737 2.11512 19.9285L2.09329 19.8906L1.49677 18.8554C1.31046 18.5321 1.13562 18.2288 1.01834 17.958C0.887745 17.6564 0.791404 17.3159 0.832657 16.9232C0.873914 16.5305 1.03893 16.2175 1.22941 15.9497C1.40044 15.7092 1.63452 15.4489 1.88391 15.1716L1.91006 15.1426L1.9116 15.1408L3.09127 13.822C3.23873 13.6222 3.39284 13.1881 3.39284 12.7768C3.39284 12.366 3.2387 11.9317 3.09125 11.732L1.9116 10.4131L1.91006 10.4114L1.8839 10.3823C1.63451 10.105 1.40043 9.84478 1.2294 9.60429C1.03893 9.33645 0.873911 9.02344 0.832655 8.63073C0.791405 8.23807 0.887742 7.89758 1.01833 7.59599C1.13562 7.32514 1.31045 7.02181 1.49677 6.69856C1.50365 6.68663 1.51054 6.67467 1.51745 6.66268L2.0933 5.66331C2.10059 5.65066 2.10787 5.63804 2.11512 5.62544C2.314 5.2802 2.49984 4.95759 2.68665 4.70746C2.89429 4.42946 3.158 4.16287 3.54556 4.00525C3.93325 3.84756 4.30817 3.85451 4.65088 3.90892C4.95904 3.95785 5.31689 4.05945 5.69957 4.16809C5.71326 4.17198 5.72697 4.17587 5.74072 4.17977L7.14109 4.57421C7.46312 4.64415 7.79938 4.60229 8.09467 4.45538L8.44745 4.25183C8.697 4.08763 8.89034 3.8502 9.00086 3.57163L9.38324 2.42962C9.38749 2.41683 9.39199 2.40332 9.39622 2.39059C9.51306 2.03925 9.62274 1.70941 9.74618 1.44355C9.88361 1.14756 10.0724 0.850895 10.3904 0.621215C10.7086 0.391433 11.0496 0.305648 11.3738 0.268555C11.6649 0.235266 12.0121 0.235309 12.3815 0.235355ZM11.415 2.03994L11.414 2.04077C11.414 2.04077 11.4101 2.04477 11.4042 2.05264C11.3915 2.06965 11.3679 2.10632 11.3334 2.1805C11.2571 2.34489 11.1782 2.57793 11.0438 2.98204L10.6476 4.16517L10.6409 4.1828C10.3957 4.82197 9.95266 5.36607 9.37628 5.73523L9.35921 5.74616L8.93017 5.99371L8.91121 6.00345C8.23892 6.34887 7.46627 6.44525 6.72979 6.27541L6.70937 6.2707L5.26393 5.86357C4.82441 5.73884 4.5687 5.66779 4.37647 5.63727C4.28953 5.62347 4.24301 5.62301 4.22087 5.62435C4.2107 5.62496 4.20556 5.62608 4.20556 5.62608L4.20414 5.62667C4.20414 5.62667 4.19954 5.62955 4.19174 5.63628C4.17477 5.65092 4.14159 5.68391 4.08876 5.75465C3.972 5.91097 3.83796 6.14071 3.60959 6.53703L3.03373 7.53639C2.81951 7.90816 2.69684 8.12373 2.62424 8.29138C2.59147 8.36705 2.57973 8.40952 2.57542 8.43057C2.57342 8.44032 2.57304 8.44617 2.57304 8.44617L2.57308 8.44789L2.5734 8.44955C2.5734 8.44955 2.57497 8.45516 2.57894 8.46425C2.5875 8.48388 2.60779 8.52293 2.65553 8.59007C2.76134 8.73884 2.92608 8.92405 3.21288 9.24299L3.21442 9.2447L4.43515 10.6095L4.45175 10.6305C4.88857 11.1835 5.14284 12.0333 5.14284 12.7768C5.14284 13.5207 4.88867 14.3703 4.45178 14.9234L4.43518 14.9444L3.21442 16.3092L3.21288 16.311C2.92608 16.6299 2.76134 16.8151 2.65554 16.9639C2.60779 17.031 2.58751 17.0701 2.57894 17.0897C2.57497 17.0988 2.5734 17.1044 2.5734 17.1044L2.57308 17.1061L2.57305 17.1078C2.57305 17.1078 2.57342 17.1136 2.57542 17.1234C2.57973 17.1444 2.59148 17.1869 2.62424 17.2626C2.69684 17.4302 2.81952 17.6458 3.03374 18.0176L3.60957 19.0169C3.83793 19.4132 3.97199 19.6429 4.08875 19.7993C4.14159 19.87 4.17476 19.903 4.19174 19.9177C4.19954 19.9244 4.20414 19.9273 4.20414 19.9273L4.20555 19.9279C4.20555 19.9279 4.2107 19.929 4.22087 19.9296C4.243 19.9309 4.28953 19.9305 4.37647 19.9167C4.56869 19.8861 4.82441 19.8151 5.26394 19.6903L6.7093 19.2832L6.72973 19.2785C7.46634 19.1086 8.23913 19.205 8.9115 19.5506L8.93046 19.5603L9.35944 19.8079L9.37651 19.8189C9.95277 20.188 10.3957 20.7321 10.6408 21.3711L10.6476 21.3888L11.0438 22.572C11.1782 22.9761 11.2571 23.2092 11.3334 23.3735C11.3679 23.4477 11.3915 23.4844 11.4042 23.5014C11.4101 23.5093 11.414 23.5133 11.414 23.5133L11.415 23.5141C11.415 23.5141 11.4208 23.5171 11.43 23.5201C11.4499 23.5267 11.4918 23.5376 11.5727 23.5468C11.7522 23.5674 11.9975 23.5687 12.4228 23.5687H13.7233C14.1486 23.5687 14.3939 23.5674 14.5733 23.5468C14.6543 23.5376 14.6962 23.5267 14.7161 23.5201C14.7253 23.5171 14.7301 23.5147 14.7301 23.5147L14.7321 23.5133C14.7321 23.5133 14.736 23.5093 14.7419 23.5014C14.7545 23.4844 14.7782 23.4477 14.8126 23.3735C14.889 23.2092 14.9679 22.9761 15.1023 22.572L15.1029 22.5704L15.4985 21.3888L15.5053 21.3711C15.7504 20.7321 16.1933 20.188 16.7696 19.8189L16.7866 19.8079L17.2156 19.5603L17.2346 19.5506C17.907 19.205 18.6797 19.1086 19.4164 19.2785L19.4368 19.2832L20.8822 19.6903C21.3217 19.8151 21.5774 19.8861 21.7696 19.9167C21.8566 19.9305 21.9031 19.9309 21.9252 19.9296C21.9354 19.929 21.9405 19.9279 21.9405 19.9279L21.942 19.9273C21.942 19.9273 21.9465 19.9244 21.9544 19.9177C21.9713 19.903 22.0045 19.87 22.0573 19.7993C22.1741 19.6429 22.3082 19.4132 22.5365 19.0169L23.1124 18.0176C23.3266 17.6458 23.4492 17.4302 23.5218 17.2626C23.5546 17.1869 23.5664 17.1444 23.5707 17.1234C23.5727 17.1136 23.573 17.1078 23.573 17.1078L23.573 17.1061L23.5727 17.1044C23.5727 17.1044 23.5711 17.0988 23.5672 17.0897C23.5586 17.0701 23.5383 17.031 23.4906 16.9639C23.3847 16.8151 23.22 16.6299 22.9332 16.311L21.7109 14.9444L21.6943 14.9234C21.2574 14.3703 21.0033 13.5207 21.0033 12.7768C21.0033 12.0333 21.2575 11.1835 21.6943 10.6305L21.7109 10.6095L22.9317 9.2447L22.9332 9.24299C23.22 8.92405 23.3848 8.73884 23.4906 8.59007C23.5383 8.52293 23.5586 8.48388 23.5672 8.46425C23.5711 8.45516 23.5727 8.44955 23.5727 8.44955L23.573 8.4479L23.573 8.44617C23.573 8.44617 23.5727 8.44032 23.5707 8.43057C23.5664 8.40952 23.5546 8.36705 23.5218 8.29138C23.4493 8.12373 23.3266 7.90816 23.1124 7.53639L22.5365 6.53703C22.3081 6.14071 22.1741 5.91097 22.0573 5.75465C22.0045 5.68391 21.9713 5.65092 21.9544 5.63628C21.9465 5.62955 21.942 5.62667 21.942 5.62667L21.9405 5.62608C21.9405 5.62608 21.9354 5.62496 21.9252 5.62435C21.9031 5.62301 21.8566 5.62347 21.7696 5.63727C21.5774 5.66779 21.3217 5.73884 20.8822 5.86357L19.4367 6.2707L19.4163 6.27541C18.6798 6.44525 17.9072 6.34887 17.2349 6.00345L17.2159 5.99371L16.7869 5.74616L16.7698 5.73523C16.1934 5.36607 15.7504 4.82197 15.5052 4.1828L15.4985 4.16517L15.1023 2.98204C14.9679 2.57793 14.889 2.34489 14.8126 2.1805C14.7782 2.10632 14.7545 2.06965 14.7419 2.05264C14.736 2.04477 14.7321 2.04077 14.7321 2.04077L14.7311 2.03994C14.7311 2.03994 14.7253 2.03694 14.7161 2.0339C14.6962 2.02732 14.6543 2.01647 14.5733 2.00722C14.3939 1.98669 14.1486 1.98536 13.7233 1.98536H12.4228C11.9975 1.98536 11.7522 1.98669 11.5727 2.00722C11.4918 2.01647 11.4499 2.02732 11.43 2.0339C11.4208 2.03694 11.415 2.03994 11.415 2.03994Z"
                            fill="white" />
                        <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M13.0228 9.55957C11.2509 9.55957 9.81445 10.996 9.81445 12.7679C9.81445 14.5398 11.2509 15.9762 13.0228 15.9762C14.7947 15.9762 16.2311 14.5398 16.2311 12.7679C16.2311 10.996 14.7947 9.55957 13.0228 9.55957ZM8.06445 12.7679C8.06445 10.0295 10.2844 7.80957 13.0228 7.80957C15.7612 7.80957 17.9811 10.0295 17.9811 12.7679C17.9811 15.5063 15.7612 17.7262 13.0228 17.7262C10.2844 17.7262 8.06445 15.5063 8.06445 12.7679Z"
                            fill="white" />
                    </svg>

                    <span>{{ 'sidebar.settings' | translate }}</span>
                </div>
                <img src="assets/images/down-white.png" alt="Dropdown">
            </button>

            <ul class="collapse list-unstyled ps-1 mt-1" id="settingsSubmenu">
                <li class="nav-item" routerLinkActive="active">
                    <a class="nav-link" [routerLink]="'/admin/fund-strategies'">{{ 'sidebar.fund_strategies' | translate }}</a>
                </li>
            </ul>
        </li>

        <hr class="wight-hr mt-5">
        <div class="logout-container">
            <a href="javascript:void(0)" class="nav-link" (click)="onLogout()">
                <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M20.501 22.2735C19.253 23.5175 17.378 23.5176 13.975 23.5176C10.573 23.5176 8.699 23.5175 7.45 22.2735C6.579 21.4055 6.34105 20.2626 6.25205 18.8136C6.22705 18.3996 6.54198 18.0436 6.95498 18.0186C7.36898 17.9946 7.725 18.3086 7.75 18.7216C7.832 20.0496 8.04496 20.7475 8.50996 21.2115C9.31996 22.0175 10.8761 22.0176 13.9771 22.0176C17.0781 22.0176 18.635 22.0175 19.444 21.2115C20.252 20.4065 20.2521 18.8556 20.2521 15.7676V9.76758C20.2521 6.67858 20.252 5.12854 19.445 4.32354C18.6351 3.51758 17.0782 3.51758 13.9775 3.51758C10.8769 3.51758 9.31892 3.51758 8.50996 4.32354C8.04396 4.78754 7.831 5.48557 7.75 6.81357C7.724 7.22757 7.36898 7.5396 6.95498 7.5166C6.54098 7.4906 6.22705 7.13458 6.25205 6.72158C6.34105 5.27358 6.578 4.13052 7.45 3.26152C8.69795 2.01758 10.5708 2.01758 13.9746 2.01758C17.3783 2.01758 19.2531 2.01758 20.5021 3.26152C21.75 4.50647 21.75 6.37443 21.75 9.76716V15.7676C21.75 19.1596 21.75 21.0285 20.501 22.2735Z"
                        fill="white" />
                    <path
                        d="M14.9946 19.7676C14.9946 20.3196 14.5426 20.7676 13.9906 20.7676C13.4386 20.7676 12.9896 20.3196 12.9896 19.7676C12.9896 19.2156 13.4376 18.7676 13.9896 18.7676H13.9986C14.5516 18.7676 14.9946 19.2156 14.9946 19.7676Z"
                        fill="white" />
                    <path
                        d="M5.52588 16.5179C5.71788 16.5179 5.90993 16.4449 6.05693 16.2979C6.34993 16.0049 6.34893 15.5298 6.05693 15.2368L4.39189 13.5749H13.0049C13.4189 13.5749 13.7549 13.2389 13.7549 12.8249C13.7549 12.4109 13.4189 12.0749 13.0049 12.0749H4.24395L6.05195 10.3039C6.34795 10.0139 6.35289 9.53889 6.06289 9.24289C5.77289 8.94689 5.29795 8.94185 5.00195 9.23185L2.75088 11.4379C2.74288 11.4459 2.73493 11.4538 2.72793 11.4618C2.06993 12.1668 2.13493 13.4429 2.64893 13.9539L4.99795 16.2988C5.14395 16.4448 5.33593 16.5179 5.52793 16.5179H5.52588Z"
                        fill="white" />
                </svg>

                <span>{{ 'LOGIN_PAGE.LOGOUT' | translate }}</span>
            </a>
        </div>
    </ul>
</nav>
