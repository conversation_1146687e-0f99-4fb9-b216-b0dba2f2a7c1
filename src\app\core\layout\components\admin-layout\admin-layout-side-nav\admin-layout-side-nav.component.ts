import { Component } from '@angular/core';
import { RouterModule, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AuthService } from '../../../../../features/auth/services/auth-service/auth.service';
import { ErrorModalService } from '../../../../services/error-modal.service';
import { TokenService } from 'src/app/features/auth/services/token.service';
import { CommonModule } from '@angular/common';
import { AuthenticationServiceProxy, SignOutCommand } from '@core/api/api.generated';

@Component({
  selector: 'app-admin-layout-side-nav',
  standalone: true,
  imports: [RouterModule, TranslateModule,CommonModule],
  providers: [AuthService, ErrorModalService],
  templateUrl: './admin-layout-side-nav.component.html',
  styleUrls: ['./admin-layout-side-nav.component.scss']
})
export class AdminLayoutSideNavComponent {
  constructor(
    private authService: AuthService,
    private router: Router,
    private errorModalService: ErrorModalService,
    private translateService: TranslateService,
    public tokenService:TokenService,
    private apiClient: AuthenticationServiceProxy
  ) {}

  onLogout(): void {
    this.apiClient.signOut(new SignOutCommand()).subscribe({
      next: () => {
      },
      error: () => {
        this.errorModalService.showError(this.translateService.instant('LOGIN_PAGE.LOGOUT_FAILED'));
      }
    });

    this.authService.logout();
    this.errorModalService.showSuccess(this.translateService.instant('LOGIN_PAGE.LOGOUT_SUCCESS'));
    this.router.navigate(['/auth/login']);
  }
}
