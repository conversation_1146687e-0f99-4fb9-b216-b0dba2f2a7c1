@import "../../../../../assets/scss/_variables.scss";

.admin-layout {
    display: flex;
    height: 100vh;
    direction: rtl;
    background-color: $white;
    position: relative;
    overflow-x: hidden;
}

.admin-layout-main {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.admin-layout-content {
    flex: 1;
    padding: 1.5rem;
    min-height: 0;
    overflow-y: auto;
}

// Desktop styles
@media (min-width: 992px) {
    .admin-layout-main {
        margin-right: 280px;
    }

    .sidenav-closed {
        .admin-layout-main {
            margin-right: 0;
        }
    }
}

// Mobile styles
@media (max-width: 991.98px) {
    .admin-layout-main {
        margin-right: 0;
    }
}
.custom-notification {
  position: fixed;
  top: 1rem;
  left: 1rem;
  z-index: 9999;
  background: white;
  padding: 1rem;
  border-radius: 12px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  max-width: 350px;
  min-width: 300px;
  animation: fadeIn 0.4s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.timeline-item {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  border-bottom: 1px solid #EAEEF1;


  .timeline-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    background-color: #f8f9fa;
  }

  .timeline-content {
    flex: 1;
    border-radius: 8px;
    padding:0 16px;
    .title {
      font-size: 14px;
      font-weight: 400;
      color: #00205A;

      margin: 0;
    }

    .description {
      font-size: 12px;
      color: #424242;
      font-weight: 400;

      margin: 0 0 10px 0;
    }
    .status {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 8px;
    font-size: 12px;
    border-radius: 20px;
    padding: 0px 7px;
    height: 24px;

      &.active {
        color: #28a745;

        i {
          color: #28a745;
        }
      }

      i {
        font-size: 12px;
      }
    }
    .status-new i {
      color: gray;
    }

    .status-under-construction {
      background-color: #E5EEFB;
      color: #2F80ED;

      i {
      color: #2F80ED;
    }
  }
    .status-waiting {
      background-color: #FDF1EB;
      color: #FF5F3D;

      i {
      color: #FF5F3D;
    }
    }
    .status-active {
      background-color: #F1FAF1;
      color: #27AE60;
      i {
      color: #27AE60;
    }
  }
    .status-exited{
      color: #828282;

      background-color: #E0E0E0;
       i {
      color: #828282;
    }
  }

    .timestamp {
      line-height: 16px;
      text-transform: capitalize;
      display: flex;
      align-items: center;
      font-size: 10px;
      color: #828282;
      justify-content: end;
      flex-direction: row-reverse;
      font-weight: 400;
      margin-bottom: 8px;


    }
    hr{
      color:#EAEEF1;
      border: 1px solid;
      width: 100%;
    }
  }
}

