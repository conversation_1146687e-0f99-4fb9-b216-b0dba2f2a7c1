import { Component ,inject , OnInit} from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AdminLayoutHeaderComponent } from './admin-layout-header/admin-layout-header.component';
import { BreadcrumbComponent } from "../../../../shared/components/breadcrumb/breadcrumb.component";
import { TableComponent } from "../../../../shared/components/table/table.component";
import { AdminLayoutSideNavComponent } from './admin-layout-side-nav/admin-layout-side-nav.component';
import { Messaging, getMessaging, getToken, onMessage, isSupported } from '@angular/fire/messaging';
import { environment } from '../../../../../environments/environment';
import { DateHijriConverterPipe } from "../../../../shared/pipes/dateHijriConverter/dateHijriConverter.pipe";
import { AuthenticationServiceProxy, UpdateFCMTokenCommand } from '@core/api/api.generated';
import { TokenService } from 'src/app/features/auth/services/token.service';
@Component({
  selector: 'app-admin-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    AdminLayoutHeaderComponent, BreadcrumbComponent, TableComponent,
    AdminLayoutSideNavComponent,
    DateHijriConverterPipe
],
  templateUrl: './admin-layout.component.html',
  styleUrls: ['./admin-layout.component.scss']
})
export class AdminLayoutComponent implements OnInit {
  notification: {
  title: string;
  description: string;
  type: string;
  status: string;
  statusId: number;
  time: string;
  createdAt: Date;
  date: string;
} | null = null;
  constructor(private AuthService: AuthenticationServiceProxy,public tokenService : TokenService) {
  }

 async ngOnInit()  {
    try {
      const isMessagingSupported = await isSupported();
      if (isMessagingSupported) {
        console.log('Firebase messaging is supported in this browser');
        this.registerServiceWorker();
        this.requestNotificationPermission();
        this.setupMessageListener();
      } else {
        console.warn('Firebase messaging is not supported in this browser');
      }
    } catch (error) {
      console.error('Error initializing Firebase messaging:', error);
    }

  }
  private currentToken = '';
  private messaging = inject(Messaging);
  periodicElements = [
    { id: 1, position: 1, name: 'Hydrogen', weight: 1.0079, symbol: 'H' },
  ];
  handleAction(id: number): void {
    console.log('id:', id);
  }

  isSidenavOpen = true;

  toggleSidenav() {
    this.isSidenavOpen = !this.isSidenavOpen;
  }
  private setupMessageListener() {
  onMessage(this.messaging, (payload:any) => {
    console.log('Message received in AdminLayout:', payload);
    if (payload.notification) {
      let notificationData = {
        title: payload.notification.title || 'إشعار',
        description: payload.notification.body || 'إشعار جديد',
        type: payload.data?.['type'] || 'fundNotifications',
        status: 'active',
        statusId: 1, // customize based on logic
        time: new Date().toLocaleTimeString(),
        createdAt: new Date(),
        date: new Date().toLocaleDateString('en-EG') // or convert Hijri manually
      };

      // Show browser Notification only if granted
      if (Notification.permission === 'granted') {
        try {
          new Notification(notificationData.title, {
            body: notificationData.description,
            icon: '/assets/icons/jadwa-logo.png'
          });
        } catch (err) {
          console.warn('Notification error:', err);
        }
      }

      // Show custom in-app styled notification
      this.notification = notificationData;

      // Optional: auto-hide after 5s
      setTimeout(() => {
        this.notification = null;
      }, 5000);
    }
  });
}

  getIconForType(type: string): string {
    return type === 'fundNotifications'
      ? 'assets/images/notify-green.png'
      : 'assets/images/notify-red.png';
  }


    private showAlertNotification(title: string, body?: string) {
      console.log('Alert notification:', title, body);

      // If you have SweetAlert or another notification library, you can use it here
      // Example (uncomment if you have Swal imported):
      // Swal.fire({
      //   title: title,
      //   text: body || '',
      //   icon: 'info',
      //   toast: true,
      //   position: 'top-end',
      //   showConfirmButton: false,
      //   timer: 3000
      // });
    }

      private async registerServiceWorker() {
        try {
          if ('serviceWorker' in navigator) {
            const registration = await navigator.serviceWorker.register(
              '/firebase-messaging-sw.js',
              {
                scope: '/firebase-cloud-messaging-push-scope',
              }
            );
          } else {
            console.warn('Service workers are not supported in this browser');
          }
        } catch (error) {
          console.error('Error registering service worker:', error);
        }
      }
      private async requestNotificationPermission() {
        try {
          const permission = await Notification.requestPermission();
          console.log('Notification permission status:', permission);
          if (permission === 'granted') {
            console.log('Notification permission granted');
            this.getDeviceToken();
          } else {
            console.log('Notification permission denied');
          }
        } catch (error) {
          console.error('Error requesting notification permission:', error);
        }
      }

      private async getDeviceToken() {
        try {
          this.currentToken = await getToken(this.messaging, {
            vapidKey: environment.firebaseConfig.vapidKey,
          });
          if (this.currentToken) {
            var obj :UpdateFCMTokenCommand = new UpdateFCMTokenCommand();
            obj.fcmWebToken = this.currentToken;
            obj.userId = this.tokenService.getuserId();
            this.AuthService.updateFCMToken(obj).subscribe({

            });
          } else {
            console.warn(
              'No FCM token received. The user may have denied permission.'
            );
          }
        } catch (error) {
          console.error('Error getting FCM token:', error);
        }
      }
}
