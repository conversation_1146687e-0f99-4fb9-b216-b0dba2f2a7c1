.login {
  padding: 55px 120px;
  background-image: url(../../../../../assets/icons/bg-login.png);
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  min-width: fit-content;
  min-height: 100vh;
  max-height: 100%;
  .lang-btn {
    border-radius: 50px;
    // width: 98px;
    color: #fff;
    font-size: 20px;
    font-weight: 400;
    line-height: 16px;
    height: 40px;
    padding-bottom: 20px;
    img {

      padding-top: 7px;

    }

  }
  .login-container {
    padding: 0 24px;

    .eye-icon {
      display: flex;
      justify-content: end;
      align-items: end;
      width: 100%;
      top: 64%;

      img {
        margin: -7px 10px;
        width: 24px;
        height: 24px;

      }
    }
    .login-btn{margin-bottom: 64px;}
  }

  // Mobile responsive styles
  @media (max-width: 768px) {
    padding: 20px 16px;
    background-position: center;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;

    .d-flex.justify-content-between {
      flex-direction: column;
      align-items: center !important;
      text-align: center;
      gap: 16px;
      margin-bottom: 32px !important;

      .logo-img {
        height: 80px !important; // Smaller logo on mobile
        max-width: 200px;
      }
    }

    .lang-btn {
      font-size: 16px;
      height: 36px;
      padding: 8px 16px;
      padding-bottom: 16px;
      min-width: 120px;
    }

    .login-container {
      padding: 24px;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 16px;
      max-width: 400px;
      width: 100%;
      margin: 0 auto;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

      .margin-bottom-56 {
        margin-bottom: 24px !important;
        text-align: center;
      }

      .margin-bottom-24 {
        margin-bottom: 16px !important;
      }

      // Center the login title and decoration
      .font-size-m.navy-color.mb-0.bold-400 {
        text-align: center;
        margin-bottom: 8px !important;
      }

      img[src*="title-shape"] {
        display: block;
        margin: 0 auto 16px auto;
      }

      .font-size-xxs.light-dark-color-color.bold-400.margin-bottom-24 {
        text-align: center;
      }

      // Make form elements full width on mobile
      .row {
        margin: 0;

        .col-md-12 {
          padding: 0;

          .margin-bottom-24,
          .mb-2.position-relative.w-50,
          .my-4.w-50 {
            width: 100% !important;
          }

          .form-control {
            width: 100% !important;
            padding: 12px 16px;
            font-size: 16px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;

            &:focus {
              border-color: #00205A;
              box-shadow: 0 0 0 0.2rem rgba(0, 32, 90, 0.25);
            }
          }

          .form-label {
            margin-bottom: 8px;
            font-weight: 600;
          }
        }
      }

      .eye-icon {
        top: 50%;
        transform: translateY(-50%);
        right: 16px;

        img {
          margin: 0;
          width: 20px;
          height: 20px;
        }
      }

      .login-btn {
        margin-bottom: 24px;
      }

      // Center the copyright text
      .bold-400.font-size-xxs.dark-gray {
        text-align: center;
        margin-top: 16px;

        img {
          margin-right: 8px;
        }
      }
    }

    // Typography adjustments for mobile
    .font-size-xl {
      font-size: 24px !important;
      line-height: 1.3;
    }

    .font-size-sm {
      font-size: 14px !important;
      line-height: 1.4;
    }

    .font-size-m {
      font-size: 18px !important;
      font-weight: 700;
    }

    .font-size-xxs {
      font-size: 12px !important;
    }
  }

  // Extra small mobile devices
  @media (max-width: 480px) {
    padding: 24px;

    .d-flex.justify-content-between img {
      height: 60px !important;
    }

    .login-container {
      padding: 20px;
      margin-top: 10px;

      .font-size-xl {
        font-size: 20px !important;
      }

      .margin-bottom-56 {
        margin-bottom: 16px !important;
      }

      // Adjust form elements for very small screens
      .form-control {
        padding: 10px 14px !important;
      }

      .eye-icon img {
        width: 18px !important;
        height: 18px !important;
      }

      // Footer styling to match the screenshot
      .bold-400.font-size-xxs.dark-gray {
        font-size: 10px !important;
        margin-top: 8px;

        img {
          width: 14px;
          height: 14px;
        }
      }
    }
  }
}
