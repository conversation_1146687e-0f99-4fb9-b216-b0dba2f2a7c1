<div class="">
  <app-breadcrumb  [breadcrumbs]="breadcrumbItems"   (onClickEvent)="handleBreadcrumbClick($event)"
    [size]="breadcrumbSizeEnum.Medium" divider=">"></app-breadcrumb>
  <div class="d-flex">
  <h2 class=" header" *ngIf="fundDetails">
   {{ fundDetails.name}}  </h2>
   <div class="w-50 mx-3" *ngIf="fundDetails.membersCount == 0 && fundDetails.statusId == fundStatus.WaitingForAddingMembers">
    <app-alert [hasClose]="false" [alertType]="AlertType.Warning" [msg]="'يجب إضافة أعضاء الصندوق حتى تتمكن من التعامل مع أنشطة الصندوق، على الأقل 2 عضو مستقل '"></app-alert>
  </div>

  </div>
  <div class="row">
    <div class=" col-8">
    <div class=" fund-details-container">
      <div class="fund-details-header">
        <p class="section-title navy-color">
          {{ 'FUND_DETAILS.BASIC_INFO' | translate }}
        </p>
        <div class="header-actions">
          <button class="edit-button" (click)="goToDetails(fundDetails.id)" *ngIf="tokenService.hasPermission('Fund.Edit') && fundDetails.statusId != fundStatus.UnderConstruction ">
            <img src="assets/images/edit-table-icon.png" alt="edit" />
          </button>
          <button class="expand-button" (click)="toggleExpand()">
            <img [src]="isExpanded ?'assets/images/accrdion_up.png' :'assets/images/accrdion_down.png'" alt="edit" style="width: 14px;height: 8px;"/>


          </button>

        </div>
      </div>
      <hr *ngIf="isExpanded">
      <div class="fund-details-content" [class.expanded]="isExpanded">
        <div class="details-grid">
          <div class="detail-item">
            <label>
              {{ 'FUND_DETAILS.FUND_CODE' | translate }}
            </label>
            <span class="value">{{fundDetails.id}}</span>
          </div>
          <div class="detail-item">
            <label> {{ 'FUND_DETAILS.STRATEGY' | translate }}
            </label>
            <span class="value">{{fundDetails.strategyName}}</span>
          </div>
          <div class="detail-item">
            <label> {{ 'FUND_DETAILS.BUILDING_COUNT' | translate }}
            </label>
            <span class="value">{{fundDetails.propertiesNumber}}</span>
          </div>
          <div class="detail-item">
            <label> {{ 'FUND_DETAILS.CREATION_DATE' | translate }}
            </label>
            <div class="date-value" *ngIf="fundDetails.initiationDate">
              <span class="gregorian">{{fundDetails.initiationDate | date:'d/M/y' }}</span>-
              <span class="hijri">{{fundDetails.initiationDate| dateHijriConverter}}</span>
            </div>
          </div>
          <div class="detail-item">
            <label> {{ 'FUND_DETAILS.END_DATE' | translate }}
              <button class="edit-button p-0" (click)="editFundExitDate(fundDetails.id)" *ngIf="tokenService.hasPermission('Fund.EditFundExitdate') && fundDetails.statusId != fundStatus.UnderConstruction ">
                <img src="assets/images/edit-table-icon.png" class=" mx-1" style="width: 12px; height: 12px;" alt="edit" />

              </button>
            </label>
            <div class="date-value"  *ngIf="fundDetails.exitDate">
              <span class="gregorian">{{fundDetails.exitDate | date: 'd/M/y'}}</span>-
              <span class="hijri">{{fundDetails.exitDate | dateHijriConverter}}</span>
            </div>
          </div>
          <div class="detail-item">
            <label> {{ 'FUND_DETAILS.STATUS' | translate }}
            </label>
            <div class="d-flex justify-content-between">
              <span class="status" [ngClass]="getStatusClass(fundDetails.statusId)">
                <!-- <i class="fas fa-circle"></i> -->
                <span class="circle"></span>
                {{ fundDetails.status }}
              </span>

              <button *ngIf="fundDetails.statusId == fundStatus.Active"
                class="custom-btn">
                {{ 'FUND_DETAILS.EXIT' | translate }}
              </button>
              <button *ngIf="fundDetails.statusId == fundStatus.Exited"
              class="custom-btn">
              {{ 'FUND_DETAILS.ACTIVATED' | translate }}
            </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="my-5">
      <div class="row">
        <div class="col-md-4 mb-3" *ngFor="let card of fundCards">
          <app-fund-card-info
            [notificationCount]="card.notificationCount"
            [fundCount]="fundDetails[card.fundCountKey]"
            [title]="card.title"   [queryParams]="card.queryParams"
            [icon]="card.icon" [disabled]="card.disabled"
            [routerLink]="card.routerLink">
          </app-fund-card-info>
        </div>
      </div>
    </div>

  </div>
    <div class="fund-details-page col-4">
      <app-fund-history [apiResponse]="fundDetails"></app-fund-history>
    </div>
  </div>

</div>
