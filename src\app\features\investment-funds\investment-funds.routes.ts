import { Routes } from '@angular/router';

export const INVESTMENT_FUNDS_ROUTES: Routes = [
  {
    path: '',
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./components/funds-list/investment-funds.component').then(
            (m) => m.InvestmentFundsComponent
          ),
      },
      {
        path: 'fund-details',
        loadComponent: () =>
          import('./components/fund-details/fund-details.component').then(
            (m) => m.FundDetailsComponent
          ),

      },
      
      {
        path: 'create',
        loadComponent: () =>
          import('./components/create-fund/create-fund.component').then(
            (m) => m.CreateFundComponent
          ),
      },
      {
        path: 'update/:id',
        loadComponent: () =>
          import('./components/update-fund/update-fund.component').then(
            (m) => m.UpdateFundComponent
          ),
      },
      {
        path: 'complete-fund-info/:id',
        loadComponent: () =>
          import('./components/update-fund/update-fund.component').then(
            (m) => m.UpdateFundComponent
          ),
      },
      {
        path: 'resolutions',
        loadChildren: () =>
          import('../resolutions/resolutions.routes').then(
            (m) => m.RESOLUTION_ROUTES
          ),
          
      },
      {
        path: 'members',
        loadChildren: () =>
          import('../members/members.routes').then(
            (m) => m.MEMBERS_ROUTES
          ),
          
      },
      // Add other investment funds routes here
    ],
  },
];
