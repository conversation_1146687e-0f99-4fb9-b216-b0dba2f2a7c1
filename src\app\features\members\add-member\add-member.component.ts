import { Component, OnInit, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { InputType } from '@shared/enum/input-type.enum';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import {
  BoardMembersServiceProxy,
  AddBoardMemberCommand,
  BoardMemberType
} from '@core/api/api.generated';
import { ErrorModalService } from '@core/services/error-modal.service';
import { UserManagementService } from '@shared/services/users/user-management.service';
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';

export interface AddMemberDialogData {
  fundId: number;
  hasChairman?: boolean;
}

@Component({
  selector: 'app-add-member',
  standalone: true,
  imports: [CommonModule, FormsModule, FormBuilderComponent, TranslateModule],
  templateUrl: './add-member.component.html',
  styleUrl: './add-member.component.scss',
})

export class AddMemberComponent implements OnInit {
  isFormSubmitted: boolean | undefined = false;
  isValidationFire: boolean | undefined = false;
  isLoading: boolean = false;
  private isSubmitting: boolean = false; // Guard against multiple submissions

  formGroup!: FormGroup;
  fundId: number;

  formControls: IControlOption[] = [
    {
      type: InputType.Dropdown,
      formControlName: 'userId',
      id: 'userId',
      name: 'userId',
      label: 'INVESTMENT_FUNDS.MEMBERS.MEMBER_NAME',
      placeholder: 'INVESTMENT_FUNDS.MEMBERS.CHOOSE_MEMBER_HERE',
      isRequired: true,
      class: 'col-md-12',
      options: [],
    },

    {
      type: InputType.Radio,
      formControlName: 'memberType',
      id: 'memberType',
      name: 'memberType',
      label: 'INVESTMENT_FUNDS.MEMBERS.TYPE_MEMBER',
      isRequired: true,
      class: 'col-md-12',
      options: [
        { name: 'INVESTMENT_FUNDS.MEMBERS.INDEPENDENT', id: BoardMemberType._1 },
        { name: 'INVESTMENT_FUNDS.MEMBERS.DEPENDENT', id: BoardMemberType._2 },
      ],
    },

    {
      type: InputType.Checkbox,
      formControlName: 'isChairman',
      id: 'isChairman',
      name: 'isChairman',
      label: '',
      isRequired: false,
      class: 'col-md-12',
      options: [{ name: 'INVESTMENT_FUNDS.MEMBERS.IS_CHAIRMAN', id: 1 }],
    },
  ];

  constructor(
    private formBuilder: FormBuilder,
    public dialogRef: MatDialogRef<AddMemberComponent>,
    @Inject(MAT_DIALOG_DATA) public data: AddMemberDialogData,
    private boardMembersService: BoardMembersServiceProxy,
    private userManagementService: UserManagementService,
    private errorModalService: ErrorModalService
  ) {
    this.fundId = data.fundId;
  }

  ngOnInit() {
    this.initForm();
    this.loadEligibleUsers();
  }

  onClose() {
    this.dialogRef.close();
  }

  dropdownChanged(_data: any) {
    // Handle dropdown changes if needed
  }

  onSubmitClick() {
    // This method is called by the button click
    // It triggers form validation and submission
    if (this.formGroup.valid) {
      this.onSubmit();
    } else {
      this.isValidationFire = true;
    }
  }

  onSubmit(_data?: any) {
    // Guard against multiple submissions
    if (this.isSubmitting || this.isLoading) {
      return;
    }

    if (this.formGroup.valid) {
      this.isSubmitting = true;
      this.isFormSubmitted = true;
      this.isLoading = true;

      const command = new AddBoardMemberCommand({
        id: 0, // New member
        fundId: this.fundId,
        userId: this.formGroup.get('userId')?.value,
        memberType: this.formGroup.get('memberType')?.value,
        isChairman: this.formGroup.get('isChairman')?.value || false,
        isActive: true,
        memberName: undefined,
        lastUpdateDate: undefined,
        memberTypeDisplay: undefined,
        statusDisplay: undefined,
        roleDisplay: undefined
      });

      this.boardMembersService.addBoardMember(command).subscribe({
        next: (response) => {
          this.isLoading = false;
          this.isSubmitting = false;
          if (response.successed) {
            this.errorModalService.showSuccess(
              'INVESTMENT_FUNDS.MEMBERS.SUCCESS_ADDED'
            );
            this.dialogRef.close(true); // Return true to indicate success
          } else {
            this.isSubmitting = false;
            this.errorModalService.showError(
              response.message || 'INVESTMENT_FUNDS.MEMBERS.ERROR_UNKNOWN'
            );
          }
        },
        error: (error) => {
          this.isLoading = false;
          this.isSubmitting = false;
          this.isFormSubmitted = false;
          console.error('Error adding board member:', error);
          this.errorModalService.showError(
            'INVESTMENT_FUNDS.MEMBERS.ERROR_UNKNOWN'
          );
        }
      });
    } else {
      this.isSubmitting = false;
      this.isValidationFire = true;
    }
  }

  private initForm() {
    const formGroup: any = {};

    this.formControls.forEach((control) => {
      const validators = [];
      if (control.formControlName == '') return;
      if (control.isRequired) {
        validators.push(Validators.required);
      }
      if (control.minLength) {
        validators.push(Validators.minLength(control.minLength));
      }
      if (control.maxLength) {
        validators.push(Validators.maxLength(control.maxLength));
      }
      if (control.max) {
        validators.push(Validators.max(control.max));
      }
      if (control.min) {
        validators.push(Validators.min(control.min));
      }

      formGroup[control.formControlName] = [null, validators];
    });
    this.formGroup = this.formBuilder.group(formGroup);

    // Disable the chairman checkbox if there's already a chairman
    if (this.data.hasChairman) {
      this.formGroup.get('isChairman')?.disable();
    }
  }

  private loadEligibleUsers() {
    // For now, we'll use fund manager users as eligible board members
    // In a real implementation, you might need a specific endpoint for eligible board members
    // that excludes legal council/board secretary/finance controller/compliance and legal/Managing director

    this.userManagementService.userListForBoardMembers(this.fundId).subscribe({
      next: (response) => {
        if (response.successed && response.data) {
          const userOptions = response.data.map((user:any) => ({
            id: user.id,
            name: user.fullName || user.email || `User ${user.id}`
          }));

          // Update the dropdown options
          const userDropdown = this.formControls.find(control => control.formControlName === 'userId');
          if (userDropdown) {
            userDropdown.options = userOptions;
          }
        }
      },
      error: (error: any) => {
        console.error('Error loading users:', error);
        this.errorModalService.showError('Error loading users');
      }
    });
  }

}
