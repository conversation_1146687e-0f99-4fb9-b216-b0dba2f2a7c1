<div class="form-container p-4 mt-3">
    <h3 class="header font-weight-bold">
        <span>
            <img src="assets/images/filter-1.png" alt="filter">
        </span>
        {{'INVESTMENT_FUNDS.RESOLUTIONS.ADVANCED_SEARCH' | translate}}
    </h3>
    <!-- Loading indicator -->
    <div *ngIf="isLoadingData" class="text-center py-3">
        <div class="spinner-border text-primary" role="status">
            <span class="sr-only">{{'COMMON.LOADING' | translate}}</span>
        </div>
        <p class="mt-2 text-muted">{{'INVESTMENT_FUNDS.RESOLUTIONS.LOADING_FILTERS' | translate}}</p>
    </div>

    <!-- Form -->
    <app-form-builder
        *ngIf="!isLoadingData"
        [formControls]="formControls"
        [formGroup]="formGroup"
        [isFormSubmitted]="isFormSubmitted"
        (dateSelected)="dateSelected($event)"
        (dropdownChanged)="dropdownChanged($event)">
    </app-form-builder>

    <div class="mt-3 row">
        <div class="col-12 mb-3">
            <app-custom-button
                class="w-100 py-1 fs-14"
                [btnName]="'INVESTMENT_FUNDS.RESOLUTIONS.APPLY_FILTERS' | translate"
                [buttonType]="buttonEnum.Primary"
                [iconName]="IconEnum.verify"
                [disabled]="isLoadingData"
                (click)="applyFilters()">
            </app-custom-button>
        </div>

        <div class="col-6">
            <app-custom-button
                class="w-100 p-1 fs-14"
                [btnName]="'INVESTMENT_FUNDS.RESOLUTIONS.RESET' | translate"
                [buttonType]="buttonEnum.OutLine"
                [iconName]="IconEnum.reset"
                [disabled]="isLoadingData"
                (click)="resetFilters()">
            </app-custom-button>
        </div>
        <div class="col-6">
            <app-custom-button
                class="w-100 py-1 fs-14"
                [btnName]="'COMMON.CANCEL' | translate"
                (click)="closeDialog()"
                [buttonType]="buttonEnum.Secondary"
                [iconName]="IconEnum.cancel">
            </app-custom-button>
        </div>
    </div>
</div>
