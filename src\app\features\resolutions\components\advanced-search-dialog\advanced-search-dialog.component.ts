import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

// Import shared components and services
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { InputType } from '@shared/enum/input-type.enum';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { DateConversionService } from '@shared/services/date.service';


// API imports
import {
  ResolutionsServiceProxy,
  TypesServiceProxy,
  ResolutionStatusDto,
  ResolutionTypeDto
} from '@core/api/api.generated';
import moment from 'moment';


export interface ResolutionSearchFilters {
  search?: string;
  status?: number | string; // Changed to use statusId (number) instead of ResolutionStatusEnum
  resolutionType?: number;
  fromDate?: string;
  toDate?: string;
  createdBy?: string;
}

@Component({
  selector: 'app-advanced-search-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    TranslateModule,
    FormBuilderComponent,
    CustomButtonComponent
  ],
  templateUrl: './advanced-search-dialog.component.html',
  styleUrls: ['./advanced-search-dialog.component.scss']
})
export class AdvancedSearchDialogComponent implements OnInit {

  formGroup!: FormGroup;
  isFormSubmitted = false;
  buttonEnum = ButtonTypeEnum;
  IconEnum = IconEnum;

  // API data
  statusOptions: ResolutionStatusDto[] = [];
  resolutionTypeOptions: ResolutionTypeDto[] = [];
  isLoadingData = false;

  formControls: IControlOption[] = [
    {
      type: InputType.Text,
      formControlName: 'search',
      id: 'search',
      name: 'search',
      label: 'INVESTMENT_FUNDS.RESOLUTIONS.SEARCH',
      placeholder: 'INVESTMENT_FUNDS.RESOLUTIONS.SEARCHWITHCODE_PLACEHOLDER',
      isRequired: false,
      class: 'col-md-12',
      actAsNumber: true
    },
    {
      type: InputType.Dropdown,
      formControlName: 'resolutionType',
      id: 'resolutionType',
      name: 'resolutionType',
      label: 'INVESTMENT_FUNDS.RESOLUTIONS.FILTER_BY_TYPE',
      placeholder: 'INVESTMENT_FUNDS.RESOLUTIONS.ALL_TYPES',
      isRequired: false,
      class: 'col-md-12',
      options: [] // Will be populated dynamically from API
    },
    {
      type: InputType.Dropdown,
      formControlName: 'status',
      id: 'status',
      name: 'status',
      label: 'INVESTMENT_FUNDS.RESOLUTIONS.FILTER_BY_STATUS',
      placeholder: 'INVESTMENT_FUNDS.RESOLUTIONS.ALL_STATUSES',
      isRequired: false,
      class: 'col-md-12',
      options: [] // Will be populated dynamically from API
    },
    {
      type: InputType.Label,
      formControlName: 'label',
      id: 'label',
      name: 'label',
      label: 'INVESTMENT_FUNDS.RESOLUTIONS.INITIATIONDATELABEL',
      class: 'col-md-12'  
    },
    {
      type: InputType.Date,
      formControlName: 'fromDate',
      id: 'fromDate',
      name: 'fromDate',
      label: 'INVESTMENT_FUNDS.RESOLUTIONS.FROM_DATE',
      placeholder: 'INVESTMENT_FUNDS.RESOLUTIONS.FROM_DATE',
      isRequired: false,
      class: 'col-md-6',
    },
    {
      type: InputType.Date,
      formControlName: 'toDate',
      id: 'toDate',
      name: 'toDate',
      label: 'INVESTMENT_FUNDS.RESOLUTIONS.TO_DATE',
      placeholder: 'INVESTMENT_FUNDS.RESOLUTIONS.TO_DATE',
      isRequired: false,
      class: 'col-md-6',
    }
  ];

  constructor(
    public dialogRef: MatDialogRef<AdvancedSearchDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ResolutionSearchFilters,
    private formBuilder: FormBuilder,
    private resolutionsProxy: ResolutionsServiceProxy,
    private typesProxy: TypesServiceProxy,
    private translateService: TranslateService,
    private DateConversionService: DateConversionService
  ) {
    this.initForm();
  }

  ngOnInit(): void {
    // Load API data
    this.loadStatusOptions();
    this.loadResolutionTypeOptions();
    this.setDateRange();
    // Pre-populate form with existing filter data
    if (this.data) {
      this.formGroup.patchValue(this.data);
    }
     this.resetFilters();
  }

  private initForm(): void {
    // Get today's date using moment in the required format
    const todayFormatted = this.DateConversionService.mapStringToSelectedDate(moment().format('DD-MM-YYYY'));
    this.formGroup = this.formBuilder.group({
      search: [''],
      status: [''],
      resolutionType: [''],
      fromDate: [todayFormatted], // Set today's date as default
      toDate: [todayFormatted],   // Set today's date as default
      createdBy: ['']
    });

    this.formGroup.get('fromDate')?.setValue(todayFormatted);
    this.formGroup.get('toDate')?.setValue(todayFormatted);
  }



dateSelected(event: { event: any; control: IControlOption }) {
    this.formGroup.get(event.control.formControlName)?.setValue(event.event.formattedGregorian);
    if(event.control.name === "fromDate"){
       const minDate = this.DateConversionService.mapStringToSelectedDate(
         moment(event.event.formattedGregorian).format('DD-MM-YYYY')
      );
       const toDateField = this.formControls.find(
        (f) => f.formControlName === 'toDate'
      );
      if(toDateField){
        toDateField.minGreg = minDate;
        toDateField.minHijri = toDateField.minGreg
          ? this.DateConversionService.convertGregorianToHijri(toDateField.minGreg)
          : undefined;
      }
    }
  }

  dropdownChanged(event: any): void {
    // Handle dropdown changes if needed
    console.log('Dropdown changed:', event);
  }

  applyFilters(): void {
    this.isFormSubmitted = true;
    if (this.formGroup.valid) {
      const filters = this.formGroup.value;
      console.log('Raw form values:', filters);

      // Remove empty values
      const cleanFilters = Object.keys(filters).reduce((acc: any, key) => {
        const value = filters[key];
        console.log(`Checking filter key: ${key}, value: ${value}, type: ${typeof value}`);

        if (value !== '' && value !== null && value !== undefined) {
          acc[key] = value;
          console.log(`Added ${key} to clean filters:`, value);
        } else {
          console.log(`Filtered out ${key} because it was empty/null/undefined`);
        }
        return acc;
      }, {});

      console.log('Clean filters being sent:', cleanFilters);
      this.dialogRef.close(cleanFilters);
    }
  }

  resetFilters(): void {
    this.formGroup.reset();
    this.isFormSubmitted = false;
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  /**
   * Load status options from API
   */
  private loadStatusOptions(): void {
    this.isLoadingData = true;

    this.resolutionsProxy.statuses().subscribe({
      next: (response) => {
        if (response.successed && response.data) {
          this.statusOptions = response.data;
          this.updateStatusFormControl();
        } else {
          console.warn('Failed to load status options:', response.message);
          this.setDefaultStatusOptions();
        }
        this.isLoadingData = false;
      },
      error: (error) => {
        console.error('Error loading status options:', error);
        this.setDefaultStatusOptions();
        this.isLoadingData = false;
      }
    });
  }

  /**
   * Load resolution type options from API
   */
  private loadResolutionTypeOptions(): void {
    this.typesProxy.all().subscribe({
      next: (response) => {
        if (response.successed && response.data) {
          this.resolutionTypeOptions = response.data.filter(type => type.isActive);
          this.updateResolutionTypeFormControl();
        } else {
          console.warn('Failed to load resolution type options:', response.message);
          this.setDefaultResolutionTypeOptions();
        }
      },
      error: (error) => {
        console.error('Error loading resolution type options:', error);
        this.setDefaultResolutionTypeOptions();
      }
    });
  }

  /**
   * Update status form control with API data
   */
  private updateStatusFormControl(): void {
    const statusControl = this.formControls.find(control => control.formControlName === 'status');
    if (statusControl) {
      statusControl.options = [
        { id: '', name: this.translateService.instant('RESOLUTIONS.ALL_STATUSES') },
        ...this.statusOptions.map(status => ({
          id: status.id, // Use statusId instead of enum value
          name: status.localizedName || status.nameAr || status.nameEn || `Status ${status.id}`
        }))
      ];
    }
  }

  /**
   * Update resolution type form control with API data
   */
  private updateResolutionTypeFormControl(): void {
    const typeControl = this.formControls.find(control => control.formControlName === 'resolutionType');
    if (typeControl) {
      typeControl.options = [
        { id: '', name: this.translateService.instant('RESOLUTIONS.ALL_TYPES') },
        ...this.resolutionTypeOptions.map(type => ({
          id: type.id,
          name: type.localizedName || type.nameAr || type.nameEn || `Type ${type.id}`
        }))
      ];
    }
  }

  /**
   * Set default status options as fallback
   */
  private setDefaultStatusOptions(): void {
    const statusControl = this.formControls.find(control => control.formControlName === 'status');
    if (statusControl) {
      statusControl.options = [
        { id: '', name: 'RESOLUTIONS.ALL_STATUSES' },
        { id: 1, name: 'RESOLUTIONS.STATUSDDL.DRAFT' }, // statusId 1 = Draft
        { id: 2, name: 'RESOLUTIONS.STATUSDDL.PENDING' }, // statusId 2 = Pending
        { id: 3, name: 'RESOLUTIONS.STATUSDDL.COMPLETING_DATA' }, // statusId 3 = CompletingData
        { id: 4, name: 'RESOLUTIONS.STATUSDDL.WAITING_CONFIRMATION' }, // statusId 4 = WaitingForConfirmation
        { id: 5, name: 'RESOLUTIONS.STATUSDDL.CONFIRMED' }, // statusId 5 = Confirmed
        { id: 6, name: 'RESOLUTIONS.STATUSDDL.REJECTED' }, // statusId 6 = Rejected
        { id: 7, name: 'RESOLUTIONS.STATUSDDL.VOTING_IN_PROGRESS' }, // statusId 7 = VotingInProgress
        { id: 8, name: 'RESOLUTIONS.STATUSDDL.APPROVED' }, // statusId 8 = Approved
        { id: 9, name: 'RESOLUTIONS.STATUSDDL.NOT_APPROVED' }, // statusId 9 = NotApproved
        { id: 10, name: 'RESOLUTIONS.STATUSDDL.CANCELLED' } // statusId 10 = Cancelled
      ];
    }
  }

  /**
   * Set default resolution type options as fallback
   */
  private setDefaultResolutionTypeOptions(): void {
    const typeControl = this.formControls.find(control => control.formControlName === 'resolutionType');
    if (typeControl) {
      typeControl.options = [
        { id: '', name: 'RESOLUTIONS.TYPES.ALL_TYPES' },
        { id: 1, name: 'RESOLUTIONS.TYPES.BOARD_DECISION' },
        { id: 2, name: 'RESOLUTIONS.TYPES.INVESTMENT_DECISION' },
        { id: 3, name: 'RESOLUTIONS.TYPES.OPERATIONAL_DECISION' },
        { id: 4, name: 'RESOLUTIONS.TYPES.FINANCIAL_DECISION' },
        { id: 5, name: 'RESOLUTIONS.TYPES.ADMINISTRATIVE_DECISION' },
        { id: 10, name: 'RESOLUTIONS.TYPES.OTHER' }
      ];
    }
  }


    setDateRange() {
      moment.locale('en');
      const todayDate = this.DateConversionService.mapStringToSelectedDate(
         moment().format('DD-MM-YYYY')
      );
  
      const maxDate = this.DateConversionService.mapStringToSelectedDate(
        moment().format('DD-MM-YYYY')
      );
  
      const fromDateField = this.formControls.find(
        (f) => f.formControlName === 'fromDate'
      );

      const toDateField = this.formControls.find(
        (f) => f.formControlName === 'toDate'
      );

      if (fromDateField && toDateField) {  
        fromDateField.maxHijri = fromDateField.maxGreg
          ? this.DateConversionService.convertGregorianToHijri(fromDateField.maxGreg)
          : undefined;

        
        fromDateField.maxGreg = maxDate;
        fromDateField.maxHijri = fromDateField.maxGreg
          ? this.DateConversionService.convertGregorianToHijri(fromDateField.maxGreg)
          : undefined;
        toDateField.maxGreg = maxDate;
        toDateField.maxHijri = toDateField.maxGreg
          ? this.DateConversionService.convertGregorianToHijri(toDateField.maxGreg)
          : undefined;
      }
    }
}
