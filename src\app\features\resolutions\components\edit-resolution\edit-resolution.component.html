<div class="edit-resolution-page" dir="rtl">
  <!-- Breadcrumb -->
  <div class="breadcrumb-section">
    <app-breadcrumb
      [breadcrumbs]="breadcrumbItems"
      [size]="breadcrumbSizeEnum.Medium"
      divider=">">
    </app-breadcrumb>
  </div>

  <!-- Page Header -->
  <div class="page-header-section">
    <app-page-header
      title="INVESTMENT_FUNDS.RESOLUTIONS.EDIT_RESOLUTION"
      [showCreateButton]="false"
      [showSearch]="false"
      [showFilter]="false">
    </app-page-header>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">{{ 'COMMON.LOADING' | translate }}</span>
    </div>
    <p class="loading-text">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.LOADING_RESOLUTION' | translate }}</p>
  </div>

  <!-- Main Content -->
  <div class="main-content" *ngIf="!isLoading && currentResolution">

    <!-- Edit Form -->
    <div class="edit-form-container">
      <form [formGroup]="formGroup" (ngSubmit)="onSubmit(false)">

        <!-- Form Container using reusable form-builder -->
        <div class="form-section">
          <!-- Form Builder Component -->
          <app-form-builder
            (dropdownChanged)="dropdownChanged($event)"
            [formControls]="formControls"
            [formGroup]="formGroup"
            [isFormSubmitted]="isValidationFire"
            (dateSelected)="dateSelected($event)"
            (fileUploaded)="onFileUpload($event)">
            <p slot="top" class="header mt-2">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.BASIC_INFO' | translate }}</p>
            <hr slot="between" class="hr-first"/>
            <p slot="bottom" class="header mt-2">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.ATTACHMENTS' | translate }}</p>
            <hr slot="last" class="hr-last" />
          </app-form-builder>
        </div>

        <!-- Resolution Items Section -->
        <div class="form-section">
          <div class="section-header">
            <div class="header-content">
              <h6>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_ITEMS' | translate }}</h6>
              <button type="button"
                      class="btn btn-outline-primary add-item-btn"
                      (click)="addResolutionItem()"
                      [disabled]="isSubmitting">
                <i class="fas fa-plus"></i>
                {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ADD_ITEM' | translate }}
              </button>
            </div>
          </div>

          <!-- Items List -->
          <div class="items-container" *ngIf="resolutionItems.length > 0">
            <div class="resolution-item-card" *ngFor="let item of resolutionItems; let i = index">
              <div class="item-header">
                <div class="item-info">
                  <div class="item-number">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.ITEM' | translate }} {{ i + 1 }}</div>
                  <div class="item-actions">
                    <button type="button"
                            class="btn btn-sm btn-outline-secondary edit-btn"
                            (click)="editResolutionItem(item, i)"
                            title="{{ 'COMMON.EDIT' | translate }}">
                      <i class="fas fa-edit"></i>
                    </button>
                    <button type="button"
                            class="btn btn-sm btn-outline-danger delete-btn"
                            (click)="deleteResolutionItem(item, i)"
                            title="{{ 'COMMON.DELETE' | translate }}">
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </div>
              </div>

              <div class="item-body">
                <div class="item-title" *ngIf="item.title">{{ item.title }}</div>
                <div class="item-description" *ngIf="item.description">{{ item.description }}</div>

                <div class="item-conflict" *ngIf="item.hasConflict">
                  <div class="conflict-indicator">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.HAS_CONFLICT' | translate }}</span>
                  </div>
                  <button type="button"
                          class="btn btn-sm btn-link view-conflict-btn"
                          (click)="viewConflictMembers(item)"
                          *ngIf="item.conflictMembersCount > 0">
                    {{ 'INVESTMENT_FUNDS.RESOLUTIONS.VIEW_CONFLICT_MEMBERS' | translate }}
                    ({{ item.conflictMembersCount }})
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div class="empty-items" *ngIf="resolutionItems.length === 0">
            <div class="empty-icon">
              <i class="fas fa-list-ul"></i>
            </div>
            <p class="empty-message">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.NO_ITEMS_YET' | translate }}</p>
            <button type="button"
                    class="btn btn-primary add-first-item-btn"
                    (click)="addResolutionItem()">
              <i class="fas fa-plus"></i>
              {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ADD_ITEM' | translate }}
            </button>
          </div>
        </div>

        <!-- Attachments Section -->
        <div class="form-section">
          <div class="section-header">
            <div class="header-content">
              <h6>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.ATTACHMENTS' | translate }}</h6>
              <div class="attachments-info">
                <span class="attachments-count">
                  {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ATTACHMENTS_COUNT' | translate }}:
                  <strong>{{ getTotalAttachmentsCount() }}</strong>/{{ maxAttachments }}
                </span>
                <span class="remaining-count" *ngIf="canAddMoreAttachments">
                  ({{ attachmentsRemaining }} {{ 'INVESTMENT_FUNDS.RESOLUTIONS.REMAINING' | translate }})
                </span>
              </div>
            </div>
          </div>

          <!-- Attachments List -->
          <div class="attachments-container" *ngIf="additionalAttachments.length > 0">
            <div class="attachment-card" *ngFor="let attachment of additionalAttachments; let i = index">
              <div class="attachment-icon">
                <i class="fas fa-file-pdf"></i>
              </div>
              <div class="attachment-info">
                <div class="attachment-name">{{ attachment.fileName }}</div>
                <div class="attachment-meta">
                  <span class="file-size">{{ formatFileSize(attachment.fileSize) }}</span>
                  <span class="upload-date">{{ attachment.uploadedDate | date:'dd/MM/yyyy' }}</span>
                </div>
              </div>
              <div class="attachment-actions">
                <button type="button"
                        class="btn btn-sm btn-outline-primary download-btn"
                        (click)="downloadAttachment(attachment)"
                        title="{{ 'COMMON.DOWNLOAD' | translate }}">
                  <i class="fas fa-download"></i>
                </button>
                <button type="button"
                        class="btn btn-sm btn-outline-danger delete-btn"
                        (click)="deleteAttachment(attachment, i)"
                        title="{{ 'COMMON.DELETE' | translate }}">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div class="empty-attachments" *ngIf="additionalAttachments.length === 0">
            <div class="empty-icon">
              <i class="fas fa-paperclip"></i>
            </div>
            <p class="empty-message">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.NO_ADDITIONAL_ATTACHMENTS' | translate }}</p>
          </div>

          <!-- Upload Section -->
          <div class="upload-section" *ngIf="canAddMoreAttachments">
            <input type="file"
                   #fileInput
                   (change)="onAdditionalFileUpload($event)"
                   accept=".pdf"
                   multiple
                   style="display: none;">
            <button type="button"
                    class="btn btn-outline-primary upload-btn"
                    (click)="fileInput.click()">
              <i class="fas fa-plus"></i>
              {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ADD_ATTACHMENT' | translate }}
            </button>
            <div class="upload-info">
              <small class="text-muted">
                {{ 'INVESTMENT_FUNDS.RESOLUTIONS.UPLOAD_REQUIREMENTS' | translate }}
                (PDF, {{ 'INVESTMENT_FUNDS.RESOLUTIONS.MAX_SIZE' | translate }}: 10MB)
              </small>
            </div>
          </div>

          <!-- Max Attachments Reached -->
          <div class="max-reached-message" *ngIf="!canAddMoreAttachments">
            <i class="fas fa-info-circle"></i>
            <span>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.MAX_ATTACHMENTS_REACHED' | translate }}</span>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
          <div class="actions-container">
            <button type="button" 
                    class="btn btn-secondary cancel-btn" 
                    (click)="onCancel()">
              <i class="fas fa-times"></i>
              {{ 'COMMON.CANCEL' | translate }}
            </button>

            <button type="button"
                    class="btn btn-outline-primary save-draft-btn"
                    *ngIf="canSaveAsDraft"
                    (click)="onSubmit(true)"
                    [disabled]="isSubmitting">
              <i class="fas fa-spinner fa-spin" *ngIf="isSubmitting"></i>
              <i class="fas fa-save" *ngIf="!isSubmitting"></i>
              {{ 'INVESTMENT_FUNDS.RESOLUTIONS.SAVE_AS_DRAFT' | translate }}
            </button>

            <button type="submit"
                    class="btn btn-primary submit-btn"
                    *ngIf="canSend"
                    [disabled]="isSubmitting">
              <i class="fas fa-spinner fa-spin" *ngIf="isSubmitting"></i>
              <i class="fas fa-paper-plane" *ngIf="!isSubmitting"></i>
              {{ getSubmitButtonText() }}
            </button>
          </div>
        </div>

      </form>
    </div>
  </div>

  <!-- Error State -->
  <div class="error-container" *ngIf="!isLoading && !currentResolution">
    <div class="error-message">
      <i class="fas fa-exclamation-triangle"></i>
      <h5>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_NOT_FOUND' | translate }}</h5>
      <p>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_NOT_FOUND_DESC' | translate }}</p>
      <button class="btn btn-primary" (click)="onCancel()">
        {{ 'INVESTMENT_FUNDS.RESOLUTIONS.BACK_TO_LIST' | translate }}
      </button>
    </div>
  </div>
</div>
