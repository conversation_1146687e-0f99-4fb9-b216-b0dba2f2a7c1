<div class="edit-resolution-page" dir="rtl">
  <!-- Breadcrumb -->
  <div class="breadcrumb-section">
    <app-breadcrumb
      (onClickEvent)="onBreadcrumbClicked($event)"
      [breadcrumbs]="breadcrumbItems"
      [size]="breadcrumbSizeEnum.Medium"
      divider=">">
    </app-breadcrumb>
  </div>

  <!-- Page Header -->
  <div class="page-header-section">
    <app-page-header
      title="INVESTMENT_FUNDS.RESOLUTIONS.EDIT_RESOLUTION"
      [showCreateButton]="false"
      [showSearch]="false"
      [showFilter]="false"
      [showSubHeader]="true">
    </app-page-header>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">{{ 'COMMON.LOADING' | translate }}</span>
    </div>
    <p class="loading-text">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.LOADING_RESOLUTION' | translate }}</p>
  </div>

  <!-- Main Content -->
  <div class="main-content" *ngIf="!isLoading && currentResolution">

    <!-- Edit Form -->
    <div class="edit-form-container">
      <form [formGroup]="formGroup" (ngSubmit)="onSubmit(false)">

        <!-- Form Container using reusable form-builder -->
        <div class="form-section">
          <!-- Form Builder Component -->
          <app-form-builder
            (dropdownChanged)="dropdownChanged($event)"
            [formControls]="formControls"
            [formGroup]="formGroup"
            [isFormSubmitted]="isValidationFire"
            (dateSelected)="dateSelected($event)"
            (fileUploaded)="onFileUpload($event)">
            <p slot="top" class="header mt-2">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.BASIC_INFO' | translate }}</p>
            <!-- <div slot="between" class="hr-first-container">
              <hr class="hr-first"/>
            </div> -->
            <!-- <ng-template #customTemplate let-element="element">
              <div class="custom-section">
                <p class="header">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.ATTACHMENTS' | translate }}</p>
                <hr class="hr-first" />
              </div> 
            </ng-template> -->
          
          </app-form-builder>
        </div>

     

        <!-- Attachments Section -->
        <!-- <div class="form-section" *ngIf="canAddAdditionalAttachments">
          <div class="section-header">
            <div class="header-content">
              <h6>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.ATTACHMENTS' | translate }}</h6>
              <div class="attachments-info">
                <span class="attachments-count">
                  {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ATTACHMENTS_COUNT' | translate }}:
                  <strong>{{ getTotalAttachmentsCount() }}</strong>/{{ maxAttachments }}
                </span>
                <span class="remaining-count" *ngIf="canAddMoreAttachments">
                  ({{ attachmentsRemaining }} {{ 'INVESTMENT_FUNDS.RESOLUTIONS.REMAINING' | translate }})
                </span>
              </div>
            </div>
          </div>
        </div> -->

          <!-- Attachments List -->
          <!-- <div class="attachments-container" *ngIf="additionalAttachments.length > 0">
            <div class="attachment-card" *ngFor="let attachment of additionalAttachments; let i = index">
              <div class="attachment-icon">
                <i class="fas fa-file-pdf"></i>
              </div>
              <div class="attachment-info">
                <div class="attachment-name">{{ attachment.fileName }}</div>
                <div class="attachment-meta">
                  <span class="file-size">{{ formatFileSize(attachment.fileSize) }}</span>
                  <span class="upload-date">{{ attachment.uploadedDate | date:'dd/MM/yyyy' }}</span>
                </div>
              </div>
              <div class="attachment-actions">
                <button type="button"
                        class="btn btn-sm btn-outline-primary download-btn"
                        (click)="downloadAttachment(attachment)"
                        title="{{ 'COMMON.DOWNLOAD' | translate }}">
                  <i class="fas fa-download"></i>
                </button>
                <button type="button"
                        class="btn btn-sm btn-outline-danger delete-btn"
                        (click)="deleteAttachment(attachment, i)"
                        title="{{ 'COMMON.DELETE' | translate }}">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
          </div> -->

          <!-- Empty State -->
          <!-- <div class="empty-attachments" *ngIf="additionalAttachments.length === 0">
            <div class="empty-icon">
              <i class="fas fa-paperclip"></i>
            </div>
            <p class="empty-message">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.NO_ADDITIONAL_ATTACHMENTS' | translate }}</p>
          </div> -->

          <!-- Upload Section -->
          <!-- <div class="upload-section" *ngIf="canAddMoreAttachments">
            <input type="file"
                   #fileInput
                   (change)="onAdditionalFileUpload($event)"
                   accept=".pdf"
                   multiple
                   style="display: none;">
            <button type="button"
                    class="btn btn-outline-primary upload-btn"
                    (click)="fileInput.click()">
              <i class="fas fa-plus"></i>
              {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ADD_ATTACHMENT' | translate }}
            </button>
            <div class="upload-info">
              <small class="text-muted">
                {{ 'INVESTMENT_FUNDS.RESOLUTIONS.UPLOAD_REQUIREMENTS' | translate }}
                (PDF, {{ 'INVESTMENT_FUNDS.RESOLUTIONS.MAX_SIZE' | translate }}: 10MB)
              </small>
            </div>
          </div> -->

          <!-- Max Attachments Reached -->
          <!-- <div class="max-reached-message" *ngIf="!canAddMoreAttachments">
            <i class="fas fa-info-circle"></i>
            <span>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.MAX_ATTACHMENTS_REACHED' | translate }}</span>
          </div> -->
        <!-- </div> -->
          <!-- </div> -->
          <!-- Resolution Items Section -->
          <div class="form-section" *ngIf="CanAddItems">
            <div class="section-header">
              <div class="header-content">
                <h6>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_ITEMS' | translate }}</h6>
                <button type="button"
                        class="btn btn-outline-primary add-item-btn"
                        (click)="addResolutionItem()"
                        [disabled]="isSubmitting">
                  <i class="fas fa-plus"></i>
                  {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ADD_ITEM' | translate }}
                </button>
              </div>
            </div>
          </div>

          <!-- Items List -->
          <div class="items-container" *ngIf="resolutionItems.length > 0">
            <div class="resolution-item-card" *ngFor="let item of resolutionItems; let i = index">
              <div class="item-header">
                <div class="item-info">
                  <div class="item-number">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.ITEM' | translate }} {{ i + 1 }}</div>
                  <div class="item-actions">
                    <button type="button"
                            class="btn btn-sm btn-outline-secondary edit-btn"
                            (click)="editResolutionItem(item, i)"
                            title="{{ 'COMMON.EDIT' | translate }}">
                      <i class="fas fa-edit"></i>
                    </button>
                    <button type="button"
                            class="btn btn-sm btn-outline-danger delete-btn"
                            (click)="deleteResolutionItem(item, i)"
                            title="{{ 'COMMON.DELETE' | translate }}">
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </div>
              </div>

              <div class="item-body">
                <div class="item-title" *ngIf="item.title">{{ item.title }}</div>
                <div class="item-description" *ngIf="item.description">{{ item.description }}</div>

                <div class="item-conflict" *ngIf="item.hasConflict">
                  <div class="conflict-indicator">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.HAS_CONFLICT' | translate }}</span>
                  </div>
                  <button type="button"
                          class="btn btn-sm btn-link view-conflict-btn"
                          (click)="viewConflictMembers(item)"
                          *ngIf="item.conflictMembersCount > 0">
                    {{ 'INVESTMENT_FUNDS.RESOLUTIONS.VIEW_CONFLICT_MEMBERS' | translate }}
                    ({{ item.conflictMembersCount }})
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div class="empty-items" *ngIf="resolutionItems.length === 0">
            <div class="empty-icon">
              <i class="fas fa-list-ul"></i>
            </div>
            <p class="empty-message">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.NO_ITEMS_YET' | translate }}</p>
            <button type="button"
                    class="btn btn-primary add-first-item-btn"
                    (click)="addResolutionItem()">
              <i class="fas fa-plus"></i>
              {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ADD_ITEM' | translate }}
            </button>
          </div>
        <!-- Form Actions -->
        <div class="form-actions">
          <div class="actions-container">
            <button type="button" 
                    class="btn btn-secondary cancel-btn" 
                    (click)="onCancel()">
              <!-- <i class="fas fa-times"></i> -->
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M2.0062 2.7529C2.22033 2.53883 2.51072 2.41857 2.8135 2.41857C3.11629 2.41857 3.40667 2.53883 3.62081 2.7529L9.66474 8.79683L15.7087 2.7529C15.814 2.64384 15.94 2.55685 16.0793 2.497C16.2186 2.43716 16.3685 2.40566 16.5201 2.40434C16.6717 2.40302 16.8221 2.43191 16.9624 2.48933C17.1027 2.54674 17.2302 2.63153 17.3374 2.73874C17.4446 2.84596 17.5294 2.97345 17.5868 3.11378C17.6443 3.25411 17.6731 3.40447 17.6718 3.55609C17.6705 3.70771 17.639 3.85754 17.5792 3.99686C17.5193 4.13617 17.4323 4.26217 17.3233 4.3675L11.2793 10.4114L17.3233 16.4554C17.5313 16.6707 17.6464 16.9592 17.6438 17.2586C17.6412 17.558 17.5211 17.8443 17.3094 18.0561C17.0977 18.2678 16.8113 18.3879 16.5119 18.3905C16.2125 18.3931 15.924 18.278 15.7087 18.07L9.66474 12.026L3.62081 18.07C3.40545 18.278 3.11701 18.3931 2.81761 18.3905C2.51822 18.3879 2.23182 18.2678 2.02011 18.0561C1.8084 17.8443 1.68831 17.558 1.68571 17.2586C1.6831 16.9592 1.7982 16.6707 2.0062 16.4554L8.05013 10.4114L2.0062 4.3675C1.79213 4.15337 1.67188 3.86298 1.67188 3.5602C1.67188 3.25742 1.79213 2.96703 2.0062 2.7529Z" fill="#4F4F4F"/>
              </svg>
              {{ 'COMMON.CANCEL' | translate }}
            </button>

            <button type="button"
                    class="btn btn-outline-primary save-draft-btn"
                    *ngIf="canSaveAsDraft"
                    (click)="onSubmit(true)"
                    [disabled]="isSubmitting">
              <!-- <i class="fas fa-spinner fa-spin" *ngIf="isSubmitting"></i>
              <i class="fas fa-save" *ngIf="!isSubmitting"></i> -->
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_4189_40187)">
                <path d="M19.3284 3.9358L15.5627 0.170176C15.4529 0.0602883 15.3038 -0.00145238 15.1484 -0.00146484L1.65492 -0.00146484C0.466719 -0.00146484 -0.5 0.965254 -0.5 2.1535V17.8436C-0.5 19.0318 0.466719 19.9985 1.65492 19.9985H17.345C18.5333 19.9985 19.5 19.0318 19.5 17.8436V4.3501C19.5 4.1947 19.4382 4.04568 19.3284 3.9358ZM13.3073 1.17041V5.91908C13.3073 6.46115 12.8663 6.90217 12.3242 6.90217H5.42055C4.87848 6.90217 4.43746 6.46115 4.43746 5.91908V1.17041H13.3073ZM4.43746 18.8267V12.8228C4.43746 12.2807 4.87848 11.8397 5.42055 11.8397H13.5794C14.1214 11.8397 14.5625 12.2807 14.5625 12.8228V18.8267H4.43746ZM18.3281 17.8436C18.3281 18.3856 17.8871 18.8267 17.345 18.8267H15.7344V12.8228C15.7344 11.6346 14.7677 10.6678 13.5794 10.6678H5.42055C4.2323 10.6678 3.26559 11.6346 3.26559 12.8228V18.8267H1.65492C1.11289 18.8267 0.671875 18.3856 0.671875 17.8436V2.1535C0.671875 1.61143 1.11289 1.17041 1.65492 1.17041H3.26559V5.91908C3.26559 7.10732 4.2323 8.07404 5.42055 8.07404H12.3242C13.5125 8.07404 14.4792 7.10732 14.4792 5.91908V1.17041H14.9057L18.3281 4.59283V17.8436Z" fill="#00205A"/>
                <path d="M11.3828 5.56372C11.7064 5.56372 11.9688 5.30138 11.9688 4.97778V3.09497C11.9688 2.77138 11.7064 2.50903 11.3828 2.50903C11.0592 2.50903 10.7969 2.77138 10.7969 3.09497V4.97778C10.7969 5.30138 11.0592 5.56372 11.3828 5.56372Z" fill="#00205A"/>
                </g>
                <defs>
                <clipPath id="clip0_4189_40187">
                <rect width="20" height="20" fill="white"/>
                </clipPath>
                </defs>
              </svg>
              {{ 'INVESTMENT_FUNDS.RESOLUTIONS.SAVE_AS_DRAFT' | translate }}
            </button>

            <button type="submit"
                    class="btn btn-primary submit-btn"
                    *ngIf="canSend"
                    [disabled]="isSubmitting">
              <!-- <i class="fas fa-spinner fa-spin" *ngIf="isSubmitting"></i>
              <i class="fas fa-paper-plane" *ngIf="!isSubmitting"></i> -->
              <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3.74121 9.48291C3.41817 9.48382 3.10198 9.57617 2.82923 9.74928C2.55648 9.92239 2.33831 10.1692 2.19997 10.4611C2.06163 10.753 2.00877 11.0782 2.0475 11.3989C2.08623 11.7196 2.21497 12.0228 2.41883 12.2734L6.76462 17.597C6.91957 17.7894 7.11819 17.942 7.34398 18.0423C7.56977 18.1425 7.81622 18.1874 8.06285 18.1732C8.59033 18.1449 9.06656 17.8627 9.37019 17.3988L18.3975 2.86031C18.399 2.85788 18.4005 2.85548 18.4021 2.85311C18.4868 2.72306 18.4593 2.46532 18.2845 2.30342C18.2365 2.25895 18.1799 2.22479 18.1181 2.20304C18.0564 2.18129 17.9909 2.1724 17.9256 2.17693C17.8603 2.18146 17.7966 2.19931 17.7385 2.22937C17.6803 2.25944 17.629 2.30109 17.5876 2.35176C17.5843 2.35574 17.581 2.35966 17.5775 2.36353L8.47338 12.6499C8.43874 12.689 8.39667 12.7209 8.3496 12.7436C8.30254 12.7664 8.25143 12.7795 8.19923 12.7824C8.14704 12.7852 8.0948 12.7776 8.04556 12.7601C7.99632 12.7425 7.95105 12.7154 7.91239 12.6802L4.89089 9.93063C4.57708 9.64296 4.16692 9.48324 3.74121 9.48291Z" fill="white"/>
              </svg>

              {{ getSubmitButtonText() | translate }}
            </button>
          </div>
        </div>

      </form>
    </div>
  </div>

  <!-- Error State -->
  <div class="error-container" *ngIf="!isLoading && !currentResolution">
    <div class="error-message">
      <i class="fas fa-exclamation-triangle"></i>
      <h5>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_NOT_FOUND' | translate }}</h5>
      <p>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_NOT_FOUND_DESC' | translate }}</p>
      <button class="btn btn-primary" (click)="onCancel()">
        {{ 'INVESTMENT_FUNDS.RESOLUTIONS.BACK_TO_LIST' | translate }}
      </button>
    </div>
  </div>
</div>
