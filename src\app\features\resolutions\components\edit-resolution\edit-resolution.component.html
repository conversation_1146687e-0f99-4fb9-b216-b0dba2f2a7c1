<div class="edit-resolution-page" dir="rtl">
  <!-- Breadcrumb -->
  <div class="breadcrumb-section">
    <app-breadcrumb
      [breadcrumbs]="breadcrumbItems"
      [size]="breadcrumbSizeEnum.Medium"
      divider=">">
    </app-breadcrumb>
  </div>

  <!-- Page Header -->
  <div class="page-header-section">
    <app-page-header 
      title="INVESTMENT_FUNDS.RESOLUTIONS.EDIT_RESOLUTION"
      [showCreateButton]="false"
      [showSearch]="false"
      [showFilter]="false">
    </app-page-header>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">{{ 'COMMON.LOADING' | translate }}</span>
    </div>
    <p class="loading-text">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.LOADING_RESOLUTION' | translate }}</p>
  </div>

  <!-- Main Content -->
  <div class="main-content" *ngIf="!isLoading && currentResolution">
    <!-- Resolution Info Card -->
    <div class="resolution-info-card">
      <div class="card-header">
        <h5 class="card-title">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_INFO' | translate }}</h5>
        <div class="resolution-status">
          <span class="status-badge" [ngClass]="'status-' + currentResolution.status">
            {{ currentResolution.statusDisplay || ('INVESTMENT_FUNDS.RESOLUTIONS.STATUS_' + currentResolution.status | translate) }}
          </span>
        </div>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-4">
            <strong>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_CODE' | translate }}:</strong>
            <span class="ms-2">{{ currentResolution.code }}</span>
          </div>
          <div class="col-md-4">
            <strong>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.FUND_NAME' | translate }}:</strong>
            <span class="ms-2">{{ currentResolution.fundName }}</span>
          </div>
          <div class="col-md-4">
            <strong>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.LAST_UPDATED' | translate }}:</strong>
            <span class="ms-2">{{ currentResolution.lastUpdated?.toJSDate() | date:'dd/MM/yyyy' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Edit Form -->
    <div class="edit-form-container">
      <form [formGroup]="formGroup" (ngSubmit)="onSubmit(false)">
        
        <!-- Basic Information Section -->
        <div class="form-section">
          <app-form-builder
            (dropdownChanged)="dropdownChanged($event)"
            [formControls]="formControls"
            [formGroup]="formGroup"
            [isFormSubmitted]="isValidationFire"
            (dateSelected)="dateSelected($event)"
            (fileUploaded)="onFileUpload($event)">

            <p slot="top" class="section-header mt-2">
              {{ 'INVESTMENT_FUNDS.RESOLUTIONS.BASIC_INFO' | translate }}
            </p>
            <hr slot="between" class="section-divider"/>

            <p slot="bottom" class="section-header mt-2">
              {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ATTACHMENTS' | translate }}
            </p>
            <hr slot="last" class="section-divider" />
          </app-form-builder>

          <!-- Custom Type Field (shown when Other is selected) -->
          <div class="row" *ngIf="isCustomTypeRequired()">
            <div class="col-md-4">
              <div class="form-group">
                <label for="newType" class="form-label">
                  {{ 'INVESTMENT_FUNDS.RESOLUTIONS.CUSTOM_TYPE_REQUIRED' | translate }}
                  <span class="text-danger">*</span>
                </label>
                <input
                  type="text"
                  id="newType"
                  class="form-control"
                  formControlName="newType"
                  [placeholder]="'INVESTMENT_FUNDS.RESOLUTIONS.CUSTOM_TYPE_REQUIRED' | translate">
                <div class="invalid-feedback" *ngIf="formGroup.get('newType')?.invalid && (formGroup.get('newType')?.dirty || formGroup.get('newType')?.touched)">
                  {{ 'INVESTMENT_FUNDS.RESOLUTIONS.CUSTOM_TYPE_REQUIRED' | translate }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Resolution Items Section -->
        <div class="form-section">
          <div class="section-header">
            <h6>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_ITEMS' | translate }}</h6>
          </div>

          <!-- Items List -->
          <div class="items-list" *ngIf="currentResolution.resolutionItems && currentResolution.resolutionItems.length > 0">
            <div class="item-card" *ngFor="let item of currentResolution.resolutionItems; let i = index">
              <div class="item-header">
                <span class="item-title">{{ item.title || ('INVESTMENT_FUNDS.RESOLUTIONS.ITEM' | translate) + ' ' + (i + 1) }}</span>
                <div class="item-actions">
                  <button type="button"
                          class="btn btn-sm btn-outline-primary"
                          (click)="editResolutionItem(item, i)"
                          title="{{ 'COMMON.EDIT' | translate }}">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button type="button"
                          class="btn btn-sm btn-outline-danger"
                          (click)="deleteResolutionItem(item, i)"
                          title="{{ 'COMMON.DELETE' | translate }}">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </div>
              <div class="item-body">
                <p class="item-description" *ngIf="item.description">{{ item.description }}</p>
                <div class="item-meta" *ngIf="item.hasConflict">
                  <button type="button"
                          class="conflict-badge"
                          (click)="viewConflictMembers(item)"
                          title="{{ 'INVESTMENT_FUNDS.RESOLUTIONS.VIEW_CONFLICT_MEMBERS' | translate }}">
                    <i class="fas fa-exclamation-triangle"></i>
                    {{ 'INVESTMENT_FUNDS.RESOLUTIONS.HAS_CONFLICT' | translate }}
                    <span class="conflict-count">({{ item.conflictMembersCount }})</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div class="empty-items" *ngIf="!currentResolution.resolutionItems || currentResolution.resolutionItems.length === 0">
            <i class="fas fa-list-ul"></i>
            <p>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.NO_ITEMS_YET' | translate }}</p>
          </div>

          <!-- Add Item Button -->
          <button type="button"
                  class="btn btn-outline-primary add-item-btn"
                  (click)="addResolutionItem()">
            <i class="fas fa-plus"></i>
            {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ADD_ITEM' | translate }}
          </button>
        </div>

        <!-- Additional Attachments Section -->
        <div class="form-section">
          <div class="section-header">
            <div class="header-content">
              <h6>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.ADDITIONAL_ATTACHMENTS' | translate }}</h6>
              <div class="attachments-info">
                <span class="attachments-count">
                  {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ATTACHMENTS_COUNT' | translate }}:
                  <strong>{{ getTotalAttachmentsCount() }}</strong>/{{ maxAttachments }}
                </span>
                <span class="remaining-count" *ngIf="canAddMoreAttachments">
                  ({{ attachmentsRemaining }} {{ 'INVESTMENT_FUNDS.RESOLUTIONS.REMAINING' | translate }})
                </span>
              </div>
            </div>
          </div>

          <!-- Existing Additional Attachments -->
          <div class="attachments-list" *ngIf="currentResolution.attachments && currentResolution.attachments.length > 0">
            <div class="attachment-item existing-attachment" *ngFor="let attachment of currentResolution.attachments; let i = index">
              <div class="attachment-icon">
                <i class="fas fa-file-pdf"></i>
              </div>
              <div class="attachment-details">
                <div class="attachment-name">{{ attachment.fileName }}</div>
                <div class="attachment-meta">
                  <span class="file-size">{{ formatFileSize(attachment.fileSize) }}</span>
                  <span class="upload-date">{{ attachment.uploadedDate?.toJSDate() | date:'dd/MM/yyyy' }}</span>
                </div>
              </div>
              <div class="attachment-actions">
                <button type="button"
                        class="btn btn-sm btn-outline-primary"
                        (click)="downloadAttachment(attachment)"
                        title="{{ 'COMMON.DOWNLOAD' | translate }}">
                  <i class="fas fa-download"></i>
                </button>
                <button type="button"
                        class="btn btn-sm btn-outline-danger"
                        (click)="deleteAttachment(attachment, i)"
                        title="{{ 'COMMON.DELETE' | translate }}">
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- New Additional Attachments -->
          <div class="attachments-list" *ngIf="additionalAttachments.length > 0">
            <div class="attachment-item new-attachment" *ngFor="let attachment of additionalAttachments; let i = index">
              <div class="attachment-icon">
                <i class="fas fa-file-pdf"></i>
              </div>
              <div class="attachment-details">
                <div class="attachment-name">{{ attachment.fileName }}</div>
                <div class="attachment-meta">
                  <span class="file-size">{{ formatFileSize(attachment.fileSize) }}</span>
                  <span class="new-badge">{{ 'COMMON.NEW' | translate }}</span>
                </div>
              </div>
              <div class="attachment-actions">
                <button type="button"
                        class="btn btn-sm btn-outline-danger"
                        (click)="deleteAttachment(attachment, i)"
                        title="{{ 'COMMON.DELETE' | translate }}">
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div class="empty-attachments" *ngIf="(!currentResolution.attachments || currentResolution.attachments.length === 0) && additionalAttachments.length === 0">
            <i class="fas fa-paperclip"></i>
            <p>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.NO_ADDITIONAL_ATTACHMENTS' | translate }}</p>
          </div>

          <!-- Upload Section -->
          <div class="upload-section" *ngIf="canAddMoreAttachments">
            <input type="file"
                   #fileInput
                   (change)="onAdditionalFileUpload($event)"
                   accept=".pdf"
                   multiple
                   style="display: none;">
            <button type="button"
                    class="btn btn-outline-primary upload-btn"
                    (click)="fileInput.click()">
              <i class="fas fa-plus"></i>
              {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ADD_ATTACHMENT' | translate }}
            </button>
            <div class="upload-info">
              <small class="text-muted">
                {{ 'INVESTMENT_FUNDS.RESOLUTIONS.UPLOAD_REQUIREMENTS' | translate }}
                (PDF, {{ 'INVESTMENT_FUNDS.RESOLUTIONS.MAX_SIZE' | translate }}: 10MB)
              </small>
            </div>
          </div>

          <!-- Max Attachments Reached -->
          <div class="max-reached-message" *ngIf="!canAddMoreAttachments">
            <i class="fas fa-info-circle"></i>
            <span>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.MAX_ATTACHMENTS_REACHED' | translate }}</span>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
          <div class="actions-container">
            <button type="button" 
                    class="btn btn-secondary cancel-btn" 
                    (click)="onCancel()">
              <i class="fas fa-times"></i>
              {{ 'COMMON.CANCEL' | translate }}
            </button>

            <button type="button"
                    class="btn btn-outline-primary save-draft-btn"
                    *ngIf="canSaveAsDraft"
                    (click)="onSubmit(true)"
                    [disabled]="isSubmitting">
              <i class="fas fa-spinner fa-spin" *ngIf="isSubmitting"></i>
              <i class="fas fa-save" *ngIf="!isSubmitting"></i>
              {{ 'INVESTMENT_FUNDS.RESOLUTIONS.SAVE_AS_DRAFT' | translate }}
            </button>

            <button type="submit"
                    class="btn btn-primary submit-btn"
                    *ngIf="canSend"
                    [disabled]="isSubmitting">
              <i class="fas fa-spinner fa-spin" *ngIf="isSubmitting"></i>
              <i class="fas fa-paper-plane" *ngIf="!isSubmitting"></i>
              {{ getSubmitButtonText() }}
            </button>
          </div>
        </div>

      </form>
    </div>
  </div>

  <!-- Error State -->
  <div class="error-container" *ngIf="!isLoading && !currentResolution">
    <div class="error-message">
      <i class="fas fa-exclamation-triangle"></i>
      <h5>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_NOT_FOUND' | translate }}</h5>
      <p>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_NOT_FOUND_DESC' | translate }}</p>
      <button class="btn btn-primary" (click)="onCancel()">
        {{ 'INVESTMENT_FUNDS.RESOLUTIONS.BACK_TO_LIST' | translate }}
      </button>
    </div>
  </div>
</div>
