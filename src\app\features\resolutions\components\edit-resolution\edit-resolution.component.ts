import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { FormGroup, FormBuilder, Validators, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import Swal from 'sweetalert2';

// Shared Components
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';

// Shared Interfaces and Enums
import { IControlOption } from '@shared/interfaces/i-control-option';
import { InputType } from '@shared/enum/input-type.enum';
import { SizeEnum } from '@core/enums/size';

// API Generated Types
import {
  ResolutionsServiceProxy,
  EditResolutionCommand,
  SingleResolutionResponse,
  ResolutionStatusEnum,
  VotingType,
  MemberVotingResult,
  ResolutionItemDto,
  TypesServiceProxy,
  ResolutionTypeDto
} from '@core/api/api.generated';

// Services
import { ErrorModalService } from '@core/services/error-modal.service';

// Dialog Components
import { ResolutionItemDialogComponent, ResolutionItemDialogData } from './resolution-item-dialog/resolution-item-dialog.component';
import { ConflictMembersDialogComponent, ConflictMembersDialogData } from './conflict-members-dialog/conflict-members-dialog.component';

@Component({
  selector: 'app-edit-resolution',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    FormBuilderComponent,
    PageHeaderComponent,
    BreadcrumbComponent
  ],
  providers: [
    ResolutionsServiceProxy,
    TypesServiceProxy
  ],
  templateUrl: './edit-resolution.component.html',
  styleUrl: './edit-resolution.component.scss'
})
export class EditResolutionComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  // Form Properties
  formGroup!: FormGroup;
  isFormSubmitted = false;
  isValidationFire = false;
  isLoading = false;
  isSubmitting = false;
  showCustomTypeField: boolean = false;

  // Component Data
  resolutionId!: number;
  fundId!: number;
  currentResolution!: SingleResolutionResponse;
  resolutionTypes: ResolutionTypeDto[] = [];
  resolutionItems: ResolutionItemDto[] = [];
  additionalAttachments: any[] = [];
  maxAttachments = 10;
  maxFileSize = 10485760; // 10MB

  // UI Properties
  breadcrumbSizeEnum = SizeEnum;
  breadcrumbItems = [
    { label: 'INVESTMENT_FUNDS.TITLE', link: '/admin/investment-funds' },
    { label: 'INVESTMENT_FUNDS.RESOLUTIONS.TITLE', link: '/admin/investment-funds/resolutions' },
    { label: 'INVESTMENT_FUNDS.RESOLUTIONS.EDIT_RESOLUTION', link: '' }
  ];

  // Form Controls Configuration - Following create-resolution pattern
  formControls: IControlOption[] = [
    {
      type: InputType.Text,
      formControlName: 'resolutionCode',
      id: 'resolutionCode',
      name: 'resolutionCode',
      label: 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_CODE',
      placeholder: '',
      isRequired: true,
      isReadonly: true,
      class: 'col-md-4',
    },
     {
      type: InputType.Text,
      formControlName: 'oldResolutionCode',
      id: 'oldResolutionCode',
      name: 'oldResolutionCode',
      label: 'INVESTMENT_FUNDS.RESOLUTIONS.OLD_RESOLUTION',
      placeholder: 'INVESTMENT_FUNDS.RESOLUTIONS.OLD_RESOLUTION_PLACEHOLDER',
      isRequired: false,
      class: 'col-md-4',
      isVisible: () => this.showOldResolutionField(),
    },
    {
      type: InputType.Date,
      formControlName: 'resolutionDate',
      id: 'resolutionDate',
      name: 'resolutionDate',
      label: 'INVESTMENT_FUNDS.RESOLUTIONS.DECISION_DATE',
      placeholder: 'INVESTMENT_FUNDS.RESOLUTIONS.ENTER_DECISION_DATE',
      isRequired: true,
      class: 'col-md-4',
    },
    {
      type: InputType.Dropdown,
      formControlName: 'typeId',
      id: 'typeId',
      name: 'typeId',
      label: 'RESOLUTIONS.TYPE',
      placeholder: 'RESOLUTIONS.TYPE_PLACEHOLDER',
      isRequired: true,
      class: 'col-md-4',
      options: [],
      onChange: (value: any) => this.dropdownChanged(value)
    },
    
    {
      type: InputType.Text,
      formControlName: 'customTypeName',
      id: 'customTypeName',
      name: 'customTypeName',
      label: 'RESOLUTIONS.CUSTOM_TYPE',
      placeholder: 'RESOLUTIONS.CUSTOM_TYPE_PLACEHOLDER',
      isRequired: false,
      class: 'col-md-4',
      isVisible: () => this.showCustomTypeField,
    },
   
    {
      type: InputType.Textarea,
      formControlName: 'description',
      id: 'description',
      name: 'description',
      label: 'INVESTMENT_FUNDS.RESOLUTIONS.DESCRIPTION',
      placeholder: 'INVESTMENT_FUNDS.RESOLUTIONS.ENTER_DESCRIPTION',
      isRequired: false,
      class: 'col-md-12',
    },
    {
      type: InputType.Radio,
      formControlName: 'votingType',
      id: 'votingType',
      name: 'votingType',
      label: 'INVESTMENT_FUNDS.RESOLUTIONS.VOTING_METHODOLOGY',
      isRequired: true,
      class: 'col-md-6',
      options: [
        { name: 'INVESTMENT_FUNDS.RESOLUTIONS.ALL_MEMBERS', id: VotingType._1 },
        { name: 'INVESTMENT_FUNDS.RESOLUTIONS.MAJORITY_MEMBERS', id: VotingType._2 }
      ]
    },
    {
      type: InputType.Radio,
      formControlName: 'memberVotingResult',
      id: 'memberVotingResult',
      name: 'memberVotingResult',
      label: 'INVESTMENT_FUNDS.RESOLUTIONS.VOTING_RESULT_CALCULATION',
      isRequired: true,
      class: 'col-md-6',
      options: [
        { name: 'INVESTMENT_FUNDS.RESOLUTIONS.ALL_ITEMS', id: MemberVotingResult._1 },
        { name: 'INVESTMENT_FUNDS.RESOLUTIONS.MAJORITY_ITEMS', id: MemberVotingResult._2 }
      ]
    },
    {
      type: InputType.file,
      formControlName: 'attachmentId',
      id: 'attachmentId',
      name: 'attachmentId',
      label: 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_FILE',
      placeholder: 'INVESTMENT_FUNDS.RESOLUTIONS.UPLOAD_FILE',
      isRequired: true,
      class: 'col-md-12',
      allowedTypes: ['.pdf'],
      max: 10485760 // 10MB
    }
  ];

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private resolutionsProxy: ResolutionsServiceProxy,
    private typesProxy: TypesServiceProxy,
    private translateService: TranslateService,
    private errorModalService: ErrorModalService,
    private dialog: MatDialog
  ) {
    this.initForm();
  }

  ngOnInit(): void {
    this.getRouteParams();
    this.loadResolutionTypes();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initForm(): void {
    this.formGroup = this.formBuilder.group({
      resolutionCode: [''],
      oldResolutionCode: [''],
      typeId: ['', Validators.required],
      resolutionDate: ['', Validators.required],
      customTypeName: [''],
      description: [''],
      votingType: ['', Validators.required],
      memberVotingResult: ['', Validators.required],
      attachmentId: [0, Validators.required]
    });
  }

  private getRouteParams(): void {
    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {
      this.resolutionId = +params['id'];
      this.fundId = +params['fundId'];
      
      if (this.resolutionId && this.fundId) {
        this.loadResolutionData();
      } else {
        this.errorModalService.showError('INVESTMENT_FUNDS.RESOLUTIONS.INVALID_PARAMETERS');
        this.router.navigate(['/admin/investment-funds/resolutions']);
      }
    });
  }

  private loadResolutionData(): void {
    this.isLoading = true;
    
    this.resolutionsProxy.getResolutionById(this.resolutionId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.isLoading = false;
          if (response.successed && response.data) {
            this.currentResolution = response.data;
            this.populateForm();
          } else {
            this.errorModalService.showError(
              response.message || 'INVESTMENT_FUNDS.RESOLUTIONS.LOAD_ERROR'
            );
            this.router.navigate(['/admin/investment-funds/resolutions']);
          }
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Error loading resolution:', error);
          // For testing purposes, load mock data when API is not available
          this.loadMockResolutionData();
        }
      });
  }

  private loadMockResolutionData(): void {
    console.log('Loading mock resolution data...');
    // Create mock resolution data for testing when API is not available
    this.currentResolution = {
      id: this.resolutionId,
      fundName: 'Test Fund',
      code: '22/2025/001',
      resolutionDate: '2025-07-07' as any, // Use string format for testing
      description: 'Test resolution description',
      status: ResolutionStatusEnum._2, // Pending
      resolutionType: {
        id: 1,
        nameAr: 'قرار استثماري',
        nameEn: 'Investment Decision',
        localizedName: 'Investment Decision',
        isActive: true,
        isOther: false,
        displayOrder: 1
      } as ResolutionTypeDto,
      lastUpdated: '2025-07-07' as any,
      statusId: 2,
      resolutionStatus: {
        id: 2,
        nameAr: 'في انتظار الموافقة',
        nameEn: 'Pending Approval',
        localizedName: 'Pending Approval',
        isActive: true,
        displayOrder: 2
      } as any,
      canConfirm: false,
      canReject: false,
      canEdit: true,
      canCancel: true,
      canDelete: false
    } as SingleResolutionResponse;

    // Add mock resolution items for testing
    this.resolutionItems = [
      new ResolutionItemDto({
        id: 1,
        resolutionId: this.resolutionId,
        title: 'Item 1',
        description: 'First resolution item description',
        hasConflict: false,
        displayOrder: 1,
        conflictMembers: [],
        conflictMembersCount: 0
      }),
      new ResolutionItemDto({
        id: 2,
        resolutionId: this.resolutionId,
        title: 'Item 2',
        description: 'Second resolution item with conflict of interest',
        hasConflict: true,
        displayOrder: 2,
        conflictMembers: [],
        conflictMembersCount: 2
      })
    ];

    console.log('Mock resolution items loaded:', this.resolutionItems);
    this.populateForm();
  }

  private populateForm(): void {
    if (!this.currentResolution) return;

    // Format date for form input (YYYY-MM-DD format)
    let formattedDate = '';
    if (this.currentResolution.resolutionDate) {
      try {
        // Handle both DateTime objects and date strings
        if (typeof this.currentResolution.resolutionDate === 'string') {
          const date = new Date(this.currentResolution.resolutionDate);
          formattedDate = date.toISOString().split('T')[0];
        } else if (this.currentResolution.resolutionDate && typeof this.currentResolution.resolutionDate === 'object' && 'toFormat' in this.currentResolution.resolutionDate) {
          formattedDate = (this.currentResolution.resolutionDate as any).toFormat('yyyy-MM-dd');
        } else {
          // Fallback for other date formats
          const date = new Date(this.currentResolution.resolutionDate as any);
          formattedDate = date.toISOString().split('T')[0];
        }
      } catch (error) {
        console.warn('Error formatting date:', error);
        formattedDate = '';
      }
    }

    console.log('Populating form with data:', {
      resolutionDate: formattedDate,
      typeId: this.currentResolution.resolutionType?.id,
      description: this.currentResolution.description
    });

    // Populate form with proper API data binding
    this.formGroup.patchValue({
      resolutionCode: this.currentResolution.code || '',
      oldResolutionCode: '', // Will be populated from API when available
      typeId: this.currentResolution.resolutionType?.id || '',
      resolutionDate: formattedDate,
      customTypeName: this.getCustomTypeName(),
      description: this.currentResolution.description || '',
      votingType: this.getVotingTypeFromAPI(),
      memberVotingResult: this.getMemberVotingResultFromAPI(),
      attachmentId: this.getAttachmentIdFromAPI()
    });

    // Check if custom type field should be visible based on the loaded data
    this.checkIfOtherTypeSelected();

    // Handle custom type visibility - will be handled by checkIfOtherTypeSelected
  }

  private loadResolutionTypes(): void {
    this.typesProxy.all()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.successed && response.data) {
            this.resolutionTypes = response.data;
            this.updateResolutionTypesOptions();

            // Re-check custom type visibility after types are loaded
            if (this.currentResolution) {
              this.checkIfOtherTypeSelected();
            }
          } else {
            console.warn('Failed to load resolution types:', response.message);
            // Fallback to mock data if API fails
            this.loadMockResolutionTypes();
          }
        },
        error: (error) => {
          console.error('Error loading resolution types:', error);
          // Fallback to mock data if API fails
          this.loadMockResolutionTypes();
        }
      });
  }

  private loadMockResolutionTypes(): void {
    // Fallback mock data
    this.resolutionTypes = [
      { id: 1, nameAr: 'قرار استثماري', nameEn: 'Investment Decision', localizedName: 'Investment Decision', isActive: true, isOther: false, displayOrder: 1 } as ResolutionTypeDto,
      { id: 2, nameAr: 'قرار مجلس الإدارة', nameEn: 'Board Resolution', localizedName: 'Board Resolution', isActive: true, isOther: false, displayOrder: 2 } as ResolutionTypeDto,
      { id: 3, nameAr: 'أخرى', nameEn: 'Other', localizedName: 'Other', isActive: true, isOther: true, displayOrder: 3 } as ResolutionTypeDto
    ];
    this.updateResolutionTypesOptions();

    // Re-check custom type visibility after types are loaded
    if (this.currentResolution) {
      this.checkIfOtherTypeSelected();
    }
  }

  private updateResolutionTypesOptions(): void {
    const typeControl = this.formControls.find(control => control.formControlName === 'typeId');
    if (typeControl) {
      typeControl.options = this.resolutionTypes.map(type => ({
        id: type.id,
        name: type.localizedName || type.nameEn || type.nameAr || 'Unknown'
      }));
    }
  }

  dropdownChanged(event: any): void {
    const selectedId = event?.id || event?.event?.id;

    if (selectedId === 10) {
      // Show custom type field when "Other" is selected
      this.showCustomTypeField = true;
    } else {
      // Hide custom type field for other selections
      this.showCustomTypeField = false;
      // Clear the custom type value
      this.formGroup.get('customTypeName')?.setValue('');
    }
  }

  private checkIfOtherTypeSelected(): void {
    const currentTypeId = this.formGroup.get('typeId')?.value;

    // Find the selected type in our loaded types
    const selectedType = this.resolutionTypes.find(type => type.id === currentTypeId);

    // Check if the selected type is marked as "Other" or if current resolution type is "Other"
    if (selectedType?.isOther || this.currentResolution?.resolutionType?.isOther) {
      this.showCustomTypeField = true;
      // Add required validator for custom type when "Other" is selected
      this.formGroup.get('customTypeName')?.setValidators([Validators.required]);
    } else {
      this.showCustomTypeField = false;
      // Remove required validator when "Other" is not selected
      this.formGroup.get('customTypeName')?.clearValidators();
      this.formGroup.get('customTypeName')?.setValue('');
    }
    this.formGroup.get('customTypeName')?.updateValueAndValidity();
  }



  private showCustomTypeFieldMethod(): void {
    // Set validators for custom type field when visible
    this.formGroup.get('customTypeName')?.setValidators([Validators.required]);
    this.formGroup.get('customTypeName')?.updateValueAndValidity();
  }

  private hideCustomTypeFieldMethod(): void {
    // Clear validators and value for custom type field when hidden
    this.formGroup.get('customTypeName')?.clearValidators();
    this.formGroup.get('customTypeName')?.setValue('');
    this.formGroup.get('customTypeName')?.updateValueAndValidity();
  }

  dateSelected(event: any): void {
    // Handle date selection from the date picker component
    const formattedDate = event.event?.formattedGregorian || event.event;
    this.formGroup
      .get(event.control.formControlName)
      ?.setValue(formattedDate);
  }

  onFileUpload(event: any): void {
    if (event.file) {
      // Simulate upload - replace with actual implementation
      setTimeout(() => {
        this.formGroup.patchValue({ attachmentId: event.file.id });
      }, 1000);
    }
  }

  onSubmit(saveAsDraft: boolean = false): void {
    if (this.isSubmitting) return;
    this.isSubmitting = true;
    this.isFormSubmitted = true;
    this.isValidationFire = true;

    // Basic validation check
    if (!this.isFormValid(saveAsDraft)) {
      this.isSubmitting = false;
      this.formGroup.markAllAsTouched();
      return;
    }

    // Handle status-specific business logic
    this.handleStatusSpecificSubmission(saveAsDraft);
  }

  private handleStatusSpecificSubmission(saveAsDraft: boolean): void {
    if (!this.currentResolution) {
      this.isSubmitting = false;
      return;
    }

    switch (this.currentResolution.status) {
      case ResolutionStatusEnum._1: // Draft
        this.handleDraftSubmission(saveAsDraft);
        break;
      case ResolutionStatusEnum._2: // Pending
        this.handlePendingSubmission();
        break;
      case ResolutionStatusEnum._4: // Waiting for confirmation
      case ResolutionStatusEnum._5: // Confirmed
      case ResolutionStatusEnum._7: // Rejected
        this.handleStandardEdit();
        break;
      case ResolutionStatusEnum._8: // Voting in progress
        this.handleVotingInProgressEdit();
        break;
      case ResolutionStatusEnum._3: // Approved
      case ResolutionStatusEnum._6: // Not approved
        this.handleApprovedNotApprovedEdit();
        break;
      default:
        this.isSubmitting = false;
        this.errorModalService.showError('INVESTMENT_FUNDS.RESOLUTIONS.INVALID_STATUS');
        break;
    }
  }

  private isFormValid(saveAsDraft: boolean): boolean {
    const formValue = this.formGroup.value;

    if (saveAsDraft) {
      // For draft, only date and type are required
      if (!formValue.resolutionDate) {
        this.showValidationError('INVESTMENT_FUNDS.RESOLUTIONS.DATE_REQUIRED');
        return false;
      }
      if (!formValue.typeId) {
        this.showValidationError('INVESTMENT_FUNDS.RESOLUTIONS.TYPE_REQUIRED');
        return false;
      }
      return true;
    }

    // For sending, all required fields must be filled
    const validationErrors: string[] = [];

    if (!formValue.resolutionDate) {
      validationErrors.push('INVESTMENT_FUNDS.RESOLUTIONS.DATE_REQUIRED');
    }
    if (!formValue.typeId) {
      validationErrors.push('INVESTMENT_FUNDS.RESOLUTIONS.TYPE_REQUIRED');
    }
    if (!formValue.votingType) {
      validationErrors.push('INVESTMENT_FUNDS.RESOLUTIONS.VOTING_TYPE_REQUIRED');
    }
    if (!formValue.memberVotingResult) {
      validationErrors.push('INVESTMENT_FUNDS.RESOLUTIONS.VOTING_RESULT_REQUIRED');
    }
    if (!formValue.attachmentId || formValue.attachmentId === 0) {
      validationErrors.push('INVESTMENT_FUNDS.RESOLUTIONS.ATTACHMENT_REQUIRED');
    }

    // Check custom type if "Other" is selected
    if (this.isCustomTypeRequired() && !formValue.customTypeName?.trim()) {
      validationErrors.push('INVESTMENT_FUNDS.RESOLUTIONS.CUSTOM_TYPE_REQUIRED');
    }

    if (validationErrors.length > 0) {
      this.showValidationError(validationErrors[0]); // Show first error
      return false;
    }

    return true;
  }

  private showValidationError(messageKey: string): void {
    Swal.fire({
      title: this.translateService.instant('COMMON.VALIDATION_ERROR'),
      text: this.translateService.instant(messageKey),
      icon: 'warning',
      confirmButtonColor: '#007bff',
      confirmButtonText: this.translateService.instant('COMMON.OK')
    });
  }

  isCustomTypeRequired(): boolean {
    const typeId = this.formGroup.value.typeId;
    const selectedType = this.resolutionTypes.find(type => type.id === typeId);
    return selectedType?.isOther || false;
  }

  showOldResolutionField(): boolean {
    // Show old resolution field as per Figma design
    // This field is optional and should be visible in the edit form
    return true;
  }

  // API data binding helper methods
  private getCustomTypeName(): string {
    // Return custom type name if the selected type is "Other"
    if (this.currentResolution?.resolutionType?.isOther) {
      return this.currentResolution.resolutionType.localizedName || '';
    }
    return '';
  }

  private getVotingTypeFromAPI(): VotingType | string {
    // Map API voting type to form value
    // This will be populated from API when available
    // For now, return default or empty
    return '';
  }

  private getMemberVotingResultFromAPI(): MemberVotingResult | string {
    // Map API member voting result to form value
    // This will be populated from API when available
    // For now, return default or empty
    return '';
  }

  private getAttachmentIdFromAPI(): number {
    // Get attachment ID from API response
    // This will be populated from API when available
    // For now, return 0 as default
    return 0;
  }

  // Status-specific submission handlers
  private handleDraftSubmission(saveAsDraft: boolean): void {
    if (saveAsDraft) {
      // JDWA-509: Save as draft
      this.submitResolution(true, ResolutionStatusEnum._1);
    } else {
      // JDWA-509: Send (draft to pending)
      this.submitResolution(false, ResolutionStatusEnum._2);
    }
  }

  private handlePendingSubmission(): void {
    // JDWA-509: Send (pending remains pending)
    this.submitResolution(false, ResolutionStatusEnum._2);
  }

  private handleStandardEdit(): void {
    // JDWA-567: Standard edit for waiting/confirmed/rejected
    // Note: Items validation will be implemented when API supports it
    // For now, proceed with submission
    this.submitResolution(false, ResolutionStatusEnum._4); // Waiting for confirmation
  }

  private handleVotingInProgressEdit(): void {
    // JDWA-567: Voting in progress - show vote suspension warning
    Swal.fire({
      title: this.translateService.instant('COMMON.CONFIRM'),
      text: this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.VOTE_SUSPENSION_WARNING'),
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: this.translateService.instant('COMMON.YES'),
      cancelButtonText: this.translateService.instant('COMMON.NO')
    }).then((result) => {
      if (result.isConfirmed) {
        // Suspend voting and update resolution
        this.submitResolution(false, ResolutionStatusEnum._4); // Waiting for confirmation
      } else {
        this.isSubmitting = false;
      }
    });
  }

  private handleApprovedNotApprovedEdit(): void {
    // JDWA-567: Approved/Not approved - create referral resolution
    Swal.fire({
      title: this.translateService.instant('COMMON.CONFIRM'),
      text: this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.REFERRAL_RESOLUTION_WARNING'),
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#007bff',
      cancelButtonColor: '#6c757d',
      confirmButtonText: this.translateService.instant('COMMON.YES'),
      cancelButtonText: this.translateService.instant('COMMON.NO')
    }).then((result) => {
      if (result.isConfirmed) {
        // Create referral resolution
        this.submitReferralResolution();
      } else {
        this.isSubmitting = false;
      }
    });
  }

  private showNoItemsConfirmation(callback: () => void): void {
    // MSG010: No items confirmation for legal council
    Swal.fire({
      title: this.translateService.instant('COMMON.CONFIRM'),
      text: this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.NO_ITEMS_CONFIRMATION'),
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#007bff',
      cancelButtonColor: '#6c757d',
      confirmButtonText: this.translateService.instant('COMMON.YES'),
      cancelButtonText: this.translateService.instant('COMMON.NO')
    }).then((result) => {
      if (result.isConfirmed) {
        callback();
      } else {
        this.isSubmitting = false;
      }
    });
  }

  private submitResolution(saveAsDraft: boolean, targetStatus: ResolutionStatusEnum): void {
    const command = this.buildEditResolutionCommand(saveAsDraft, targetStatus);

    this.resolutionsProxy.editResolution(command)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.isSubmitting = false;
          if (response.successed) {
            this.handleSubmissionSuccess(saveAsDraft, targetStatus);
          } else {
            this.handleSubmissionError(response.message || 'INVESTMENT_FUNDS.RESOLUTIONS.UPDATE_ERROR');
          }
        },
        error: (error) => {
          this.isSubmitting = false;
          console.error('Error updating resolution:', error);
          this.handleSubmissionError('INVESTMENT_FUNDS.RESOLUTIONS.UPDATE_ERROR');
        }
      });
  }

  private submitReferralResolution(): void {
    const command = this.buildEditResolutionCommand(false, ResolutionStatusEnum._4, true);

    this.resolutionsProxy.editResolution(command)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.isSubmitting = false;
          if (response.successed) {
            this.handleReferralResolutionSuccess();
          } else {
            this.handleSubmissionError(response.message || 'INVESTMENT_FUNDS.RESOLUTIONS.REFERRAL_ERROR');
          }
        },
        error: (error) => {
          this.isSubmitting = false;
          console.error('Error creating referral resolution:', error);
          this.handleSubmissionError('INVESTMENT_FUNDS.RESOLUTIONS.REFERRAL_ERROR');
        }
      });
  }

  onCancel(): void {
    this.router.navigate(['/admin/investment-funds/resolutions'], {
      queryParams: { fundId: this.fundId }
    });
  }

  // Getter methods for template
  get canSaveAsDraft(): boolean {
    // Only draft resolutions can be saved as draft (JDWA-509)
    return this.currentResolution?.status === ResolutionStatusEnum._1; // Draft
  }

  get canSend(): boolean {
    // All editable statuses can be sent/submitted (JDWA-509, JDWA-567)
    return [
      ResolutionStatusEnum._1, // Draft
      ResolutionStatusEnum._2, // Pending
      ResolutionStatusEnum._4, // Waiting for confirmation
      ResolutionStatusEnum._5, // Confirmed
      ResolutionStatusEnum._7, // Rejected
      ResolutionStatusEnum._8, // Voting in progress
      ResolutionStatusEnum._3, // Approved (creates referral)
      ResolutionStatusEnum._6  // Not approved (creates referral)
    ].includes(this.currentResolution?.status);
  }

  get isReferralCreation(): boolean {
    // Check if this edit will create a referral resolution
    return [
      ResolutionStatusEnum._3, // Approved
      ResolutionStatusEnum._6  // Not approved
    ].includes(this.currentResolution?.status);
  }

  get showVotingSuspensionWarning(): boolean {
    // Show warning for voting in progress status
    return this.currentResolution?.status === ResolutionStatusEnum._8;
  }

  getSubmitButtonText(): string {
    if (!this.currentResolution) return 'COMMON.SEND';

    switch (this.currentResolution.status) {
      case ResolutionStatusEnum._1: // Draft
      case ResolutionStatusEnum._2: // Pending
        return 'INVESTMENT_FUNDS.RESOLUTIONS.SEND';
      case ResolutionStatusEnum._4: // Waiting for confirmation
      case ResolutionStatusEnum._5: // Confirmed
      case ResolutionStatusEnum._7: // Rejected
      case ResolutionStatusEnum._8: // Voting in progress
        return 'INVESTMENT_FUNDS.RESOLUTIONS.SEND_FOR_CONFIRMATION';
      default:
        return 'COMMON.SEND';
    }
  }

  // Resolution Items Management Methods
  addResolutionItem(): void {
    const dialogData: ResolutionItemDialogData = {
      fundId: this.fundId,
      resolutionId: this.resolutionId,
      existingItems: this.resolutionItems,
      isEdit: false
    };

    const dialogRef = this.dialog.open(ResolutionItemDialogComponent, {
      width: '600px',
      data: dialogData,
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.resolutionItems.push(result);
        this.reorderItems();
      }
    });
  }

  editResolutionItem(item: ResolutionItemDto, index: number): void {
    const dialogData: ResolutionItemDialogData = {
      fundId: this.fundId,
      resolutionId: this.resolutionId,
      item: item,
      existingItems: this.resolutionItems,
      isEdit: true
    };

    const dialogRef = this.dialog.open(ResolutionItemDialogComponent, {
      width: '600px',
      data: dialogData,
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.resolutionItems[index] = result;
      }
    });
  }

  deleteResolutionItem(item: ResolutionItemDto, index: number): void {
    const confirmMessage = this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.DELETE_ITEM_CONFIRM');

    if (confirm(confirmMessage)) {
      this.resolutionItems.splice(index, 1);
      this.reorderItems();
    }
  }

  viewConflictMembers(item: ResolutionItemDto): void {
    if (!item.conflictMembers || item.conflictMembers.length === 0) {
      return;
    }

    // TODO: Implement conflict members dialog when available
    console.log('Viewing conflict members for item:', item.title, item.conflictMembers);
  }

  private reorderItems(): void {
    // Update display order for all items
    this.resolutionItems.forEach((item, index) => {
      item.displayOrder = index + 1;
      // Update title to reflect new order
      item.title = this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.ITEM') + ' ' + (index + 1);
    });
  }

  // Attachments Management Methods
  onAdditionalFileUpload(event: any): void {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    for (let file of files) {
      if (this.validateAttachmentFile(file)) {
        this.uploadAttachment(file);
      }
    }

    // Clear the input
    event.target.value = '';
  }

  private validateAttachmentFile(file: File): boolean {
    // Check file type
    if (file.type !== 'application/pdf') {
      this.errorModalService.showError('INVESTMENT_FUNDS.RESOLUTIONS.INVALID_FILE_TYPE');
      return false;
    }

    // Check file size
    if (file.size > this.maxFileSize) {
      this.errorModalService.showError('INVESTMENT_FUNDS.RESOLUTIONS.FILE_TOO_LARGE');
      return false;
    }

    // Check max attachments limit
    const totalAttachments = this.getTotalAttachmentsCount();
    if (totalAttachments >= this.maxAttachments) {
      this.errorModalService.showError('INVESTMENT_FUNDS.RESOLUTIONS.MAX_ATTACHMENTS_REACHED');
      return false;
    }

    return true;
  }

  private uploadAttachment(file: File): void {
    // Mock file upload - replace with actual implementation
    const mockAttachment = {
      id: Date.now(), // Mock ID
      fileName: file.name,
      filePath: `/uploads/${file.name}`,
      fileSize: file.size,
      contentType: file.type,
      uploadedDate: new Date() as any,
      uploadedBy: 'Current User',
      description: undefined
    };

    this.additionalAttachments.push(mockAttachment);
  }

  deleteAttachment(attachment: any, index: number): void {
    Swal.fire({
      title: this.translateService.instant('COMMON.CONFIRM_DELETE'),
      text: this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.DELETE_ATTACHMENT_CONFIRM'),
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: this.translateService.instant('COMMON.DELETE'),
      cancelButtonText: this.translateService.instant('COMMON.CANCEL')
    }).then((result) => {
      if (result.isConfirmed) {
        this.additionalAttachments.splice(index, 1);
      }
    });
  }

  downloadAttachment(attachment: any): void {
    // Mock download - replace with actual implementation
    console.log('Download attachment:', attachment);
    this.errorModalService.showSuccess('INVESTMENT_FUNDS.RESOLUTIONS.DOWNLOAD_STARTED');
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  getTotalAttachmentsCount(): number {
    // TODO: Implement when API supports attachments
    const additionalCount = this.additionalAttachments.length;
    return additionalCount;
  }

  get canAddMoreAttachments(): boolean {
    return this.getTotalAttachmentsCount() < this.maxAttachments;
  }

  get attachmentsRemaining(): number {
    return this.maxAttachments - this.getTotalAttachmentsCount();
  }

  // Status label getter with proper localization
  get currentStatusLabel(): string {
    if (!this.currentResolution?.status) {
      return '';
    }

    // Use localized status labels based on status enum
    const statusKey = this.getStatusTranslationKey(this.currentResolution.status);
    return this.translateService.instant(statusKey);
  }

  private getStatusTranslationKey(status: ResolutionStatusEnum): string {
    switch (status) {
      case ResolutionStatusEnum._1:
        return 'RESOLUTIONS.STATUS_DRAFT';
      case ResolutionStatusEnum._2:
        return 'RESOLUTIONS.STATUS_PENDING';
      case ResolutionStatusEnum._3:
        return 'RESOLUTIONS.STATUS_APPROVED';
      case ResolutionStatusEnum._4:
        return 'RESOLUTIONS.STATUS_WAITING_CONFIRMATION';
      case ResolutionStatusEnum._5:
        return 'RESOLUTIONS.STATUS_CONFIRMED';
      case ResolutionStatusEnum._6:
        return 'RESOLUTIONS.STATUS_NOT_APPROVED';
      case ResolutionStatusEnum._7:
        return 'RESOLUTIONS.STATUS_REJECTED';
      case ResolutionStatusEnum._8:
        return 'RESOLUTIONS.STATUS_VOTING_IN_PROGRESS';
      case ResolutionStatusEnum._9:
        return 'RESOLUTIONS.STATUS_COMPLETING_DATA';
      default:
        return 'RESOLUTIONS.STATUS_UNKNOWN';
    }
  }

  // API Integration Methods
  private buildEditResolutionCommand(saveAsDraft: boolean, targetStatus: ResolutionStatusEnum, isReferral: boolean = false): EditResolutionCommand {
    const formValue = this.formGroup.value;

    // Prepare attachment IDs (simplified for now)
    const attachmentIds = this.additionalAttachments.map(a => a.id);

    const command = new EditResolutionCommand({
      id: this.resolutionId,
      code: this.currentResolution?.code,
      resolutionDate: this.parseFormDate(formValue.resolutionDate),
      description: formValue.description || undefined,
      resolutionTypeId: formValue.typeId,
      newType: this.isCustomTypeRequired() ? formValue.customTypeName : undefined,
      attachmentId: formValue.attachmentId || 0,
      votingType: formValue.votingType || VotingType._1,
      memberVotingResult: formValue.memberVotingResult || MemberVotingResult._1,
      status: targetStatus,
      fundId: this.fundId,
      saveAsDraft: saveAsDraft,
      resolutionItems: this.resolutionItems.length > 0 ? this.resolutionItems : undefined,
      attachmentIds: attachmentIds.length > 0 ? attachmentIds : undefined,
      parentResolutionId: isReferral ? this.currentResolution?.id : undefined,
      oldResolutionCode: isReferral ? this.currentResolution?.code : undefined
    });

    return command;
  }

  private parseFormDate(dateString: string): any {
    if (!dateString) return undefined;

    // Convert YYYY-MM-DD format to DateTime
    try {
      const date = new Date(dateString);
      return date.toISOString();
    } catch (error) {
      console.error('Error parsing date:', error);
      return undefined;
    }
  }

  private handleSubmissionSuccess(saveAsDraft: boolean, targetStatus: ResolutionStatusEnum): void {
    let messageKey = 'INVESTMENT_FUNDS.RESOLUTIONS.UPDATE_SUCCESS';

    if (saveAsDraft) {
      messageKey = 'INVESTMENT_FUNDS.RESOLUTIONS.SAVED_AS_DRAFT';
    } else {
      switch (targetStatus) {
        case ResolutionStatusEnum._2: // Pending
          messageKey = 'INVESTMENT_FUNDS.RESOLUTIONS.SENT_SUCCESS';
          break;
        case ResolutionStatusEnum._4: // Waiting for confirmation
          messageKey = 'INVESTMENT_FUNDS.RESOLUTIONS.SENT_FOR_CONFIRMATION';
          break;
        default:
          messageKey = 'INVESTMENT_FUNDS.RESOLUTIONS.UPDATE_SUCCESS';
          break;
      }
    }

    Swal.fire({
      title: this.translateService.instant('COMMON.SUCCESS'),
      text: this.translateService.instant(messageKey),
      icon: 'success',
      confirmButtonColor: '#007bff',
      confirmButtonText: this.translateService.instant('COMMON.OK')
    }).then(() => {
      this.navigateToResolutionsList();
    });
  }

  private handleReferralResolutionSuccess(): void {
    Swal.fire({
      title: this.translateService.instant('COMMON.SUCCESS'),
      text: this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.REFERRAL_CREATED'),
      icon: 'success',
      confirmButtonColor: '#007bff',
      confirmButtonText: this.translateService.instant('COMMON.OK')
    }).then(() => {
      this.navigateToResolutionsList();
    });
  }

  private handleSubmissionError(message: string): void {
    Swal.fire({
      title: this.translateService.instant('COMMON.ERROR'),
      text: this.translateService.instant(message),
      icon: 'error',
      confirmButtonColor: '#d33',
      confirmButtonText: this.translateService.instant('COMMON.OK')
    });
  }

  private navigateToResolutionsList(): void {
    this.router.navigate(['/admin/investment-funds/resolutions'], {
      queryParams: { fundId: this.fundId }
    });
  }
}
