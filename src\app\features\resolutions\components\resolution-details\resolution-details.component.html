<!-- Breadcrumb Navigation -->
<div class="breadcrumb-container">
  <app-breadcrumb [breadcrumbs]="breadcrumbItems" [size]="breadcrumbSizeEnum.Small"
    (onClickEvent)="onBreadcrumbClicked($event)">
  </app-breadcrumb>
</div>
<div class="d-flex justify-content-between">
  <div class="header-container w-100 d-flex align-items-center justify-content-between mb-3">
    <div class="d-flex align-items-center">
      <span class="rotate-icon mx-2">
        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
          <path fill-rule="evenodd" clip-rule="evenodd"
            d="M11.0572 0.75H10.9428C8.75212 0.749987 7.03144 0.749976 5.68802 0.930594C4.31137 1.11568 3.21911 1.50272 2.36091 2.36091C1.50271 3.21911 1.11568 4.31137 0.930593 5.68802C0.749975 7.03144 0.749987 8.75212 0.75 10.9428V11.0572C0.749987 13.2479 0.749975 14.9686 0.930593 16.312C1.11568 17.6886 1.50271 18.7809 2.36091 19.6391C3.21911 20.4973 4.31137 20.8843 5.68802 21.0694C7.03144 21.25 8.75212 21.25 10.9428 21.25H11.0572C13.2479 21.25 14.9686 21.25 16.312 21.0694C17.6886 20.8843 18.7809 20.4973 19.6391 19.6391C20.4973 18.7809 20.8843 17.6886 21.0694 16.312C21.25 14.9686 21.25 13.2479 21.25 11.0572V10.9428C21.25 8.75212 21.25 7.03144 21.0694 5.68802C20.8843 4.31137 20.4973 3.21911 19.6391 2.36091C18.7809 1.50272 17.6886 1.11568 16.312 0.930594C14.9686 0.749976 13.2479 0.749987 11.0572 0.75ZM16.1121 2.41722C17.3224 2.57994 18.0454 2.88853 18.5784 3.42157C19.1115 3.95462 19.4201 4.67757 19.5828 5.8879C19.7484 7.11979 19.75 8.73963 19.75 11C19.75 13.2604 19.7484 14.8802 19.5828 16.1121C19.4201 17.3224 19.1115 18.0454 18.5784 18.5784C18.0454 19.1115 17.3224 19.4201 16.1121 19.5828C14.8802 19.7484 13.2604 19.75 11 19.75C8.73963 19.75 7.11979 19.7484 5.88789 19.5828C4.67757 19.4201 3.95462 19.1115 3.42157 18.5784C2.88853 18.0454 2.57994 17.3224 2.41722 16.1121C2.25159 14.8802 2.25 13.2604 2.25 11C2.25 8.73963 2.25159 7.11979 2.41722 5.8879C2.57994 4.67757 2.88853 3.95462 3.42157 3.42157C3.95462 2.88853 4.67757 2.57994 5.88789 2.41722C7.11979 2.25159 8.73963 2.25 11 2.25C13.2604 2.25 14.8802 2.25159 16.1121 2.41722Z"
            fill="#00205A" />
          <path fill-rule="evenodd" clip-rule="evenodd"
            d="M11.9622 7.97726C11.6735 8.27428 11.6802 8.74911 11.9773 9.03781C12.1388 9.19487 12.396 9.3971 12.6407 9.58933C12.6596 9.60416 12.6786 9.61906 12.6976 9.63405C12.9434 9.82696 13.2061 10.0333 13.4548 10.2439C13.4572 10.246 13.4595 10.248 13.4619 10.25L7 10.25C6.58579 10.25 6.25 10.5858 6.25 11C6.25 11.4142 6.58579 11.75 7 11.75L13.4619 11.75C13.4595 11.752 13.4572 11.754 13.4548 11.7561C13.2061 11.9667 12.9434 12.173 12.6976 12.3659C12.6786 12.3809 12.6596 12.3958 12.6407 12.4107C12.396 12.6029 12.1388 12.8051 11.9773 12.9622C11.6802 13.2509 11.6735 13.7257 11.9622 14.0227C12.2509 14.3198 12.7257 14.3265 13.0227 14.0378C13.114 13.9491 13.2958 13.8035 13.5672 13.5903C13.5869 13.5748 13.6069 13.5592 13.6272 13.5432C13.8693 13.3532 14.1534 13.1302 14.4245 12.9005C14.715 12.6543 15.0168 12.3787 15.2515 12.1032C15.369 11.9652 15.485 11.8096 15.5746 11.6422C15.661 11.4807 15.75 11.2583 15.75 11C15.75 10.7417 15.661 10.5193 15.5746 10.3578C15.485 10.1904 15.369 10.0348 15.2515 9.89679C15.0168 9.62131 14.715 9.34574 14.4245 9.09954C14.1534 8.8698 13.8693 8.64683 13.6272 8.45676C13.6069 8.44084 13.5869 8.42515 13.5672 8.40971C13.2958 8.19651 13.114 8.05089 13.0227 7.96219C12.7257 7.67349 12.2509 7.68023 11.9622 7.97726Z"
            fill="#00205A" />
        </svg>
      </span>
      <div class="title-container">
        <p class="title">
          {{'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_DETAILS' | translate }}
        </p>
        <p class="sub-title" *ngIf="resolution!= null && resolution.parentResolutionId != null">
          {{'INVESTMENT_FUNDS.RESOLUTIONS.CODE_RELATED_DECISION' | translate }}:
          <span>
            {{resolution?.parentResolutionCode}}
          </span>

        </p>
      </div>
    </div>
    <!-- Dialog Actions -->
    <div class="dialog-actions d-flex justify-content-end mt-4">

      <app-custom-button [btnName]="'COMMON.CANCEL' | translate" (click)="cancel()" [buttonType]="buttonEnum.Secondary"
        [iconName]="IconEnum.cancel">
      </app-custom-button>



      <app-custom-button *ngIf="resolution.canEdit && tokenService.hasPermission('Resolution.Edit') &&
      (tokenService.hasRole('fundmanager') || (resolution.statusId >= 4 && resolution.statusId != 10))"
        (click)="editResolution(resolution)" [btnName]="'COMMON.EDIT' | translate" (click)="editResolution(resolution)"
        class="mx-2" [buttonType]="buttonEnum.OutLine" [iconName]="IconEnum.edit">
      </app-custom-button>

      <app-custom-button *ngIf="resolution.canEdit && tokenService.hasPermission('Resolution.Edit') && ( tokenService.hasRole('legalcouncil') || tokenService.hasRole('boardsecretary'))
      && (resolution.statusId == 2 || resolution.statusId == 3)" [btnName]="'COMMON.Complete' | translate"
        (click)="completeResolution(resolution)" class="mx-2" [buttonType]="buttonEnum.Primary"
        [iconName]="IconEnum.verify">
      </app-custom-button>

      <!-- Confirm/Reject buttons for Fund Manager -->
      <app-custom-button *ngIf="shouldShowConfirmRejectButtons()" [btnName]="'RESOLUTIONS.CONFIRM' | translate"
        (click)="onConfirmResolution()" class="mx-2" [buttonType]="buttonEnum.Primary" [iconName]="IconEnum.verify">
      </app-custom-button>

      <app-custom-button *ngIf="shouldShowConfirmRejectButtons()" [btnName]="'RESOLUTIONS.REJECT' | translate"
        (click)="onRejectResolution()" class="mx-2" [buttonType]="buttonEnum.Danger" [iconName]="IconEnum.cancel">
      </app-custom-button>

      <!-- Send to Vote button for Legal Council/Board Secretary -->
      <app-custom-button *ngIf="shouldShowSendToVoteButton()" [btnName]="'RESOLUTIONS.SEND_TO_VOTE' | translate"
        (click)="onSendToVote()" class="mx-2" [buttonType]="buttonEnum.Primary" [iconName]="IconEnum.verify">
      </app-custom-button>
    </div>
  </div>

</div>
<!-- Main Content -->

<div class="row">
  <div class=" col-8">
    <div class=" resolution-details-container">
      <div class="resolution-details-header">
        <p class="section-title navy-color">
          {{ 'FUND_DETAILS.BASIC_INFO' | translate }}
        </p>
        <div class="header-actions">
          <button class="expand-button" (click)="toggleExpand()">
            <img [src]="isExpanded ?'assets/images/accrdion_up.png'
              :'assets/images/accrdion_down.png'" alt="edit" style="width:
              14px;height: 8px;" />
          </button>

        </div>
      </div>
      <hr *ngIf="isExpanded" style="margin-bottom: 16PX;">
      <div class="resolution-details-content" [class.expanded]="isExpanded">
        <div class="row" style="margin-bottom: 28px;">
          <div class="col-md-3 info-item">
            <p class="info-label">{{
              'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_CODE' | translate }}</p>
            <p class="info-value">{{ resolution?.code }}</p>
          </div>

          <div class="col-md-3 info-item" *ngIf="resolution?.oldResolutionCode">
            <p class="info-label">{{
              'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_OLD_CODE' | translate
              }}</p>
            <p class="info-value" >{{ resolution?.oldResolutionCode }}</p>
          </div>

          <!-- Note: Parent Resolution and Old Resolution Code properties are not available in current API model -->
          <!-- Resolution Date -->
          <div class="col-md-3 info-item">
            <p class="info-label">{{
              'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_DATE' | translate }}</p>
            <span class="gregorian">{{resolution?.resolutionDate | date:'d/M/y' }}</span>-
            <span class="hijri">{{resolution?.resolutionDate| dateHijriConverter}}</span>
            <!-- 
            <p class="info-value">{{ formatDate(resolution?.resolutionDate)
              }}</p> -->
          </div>

          <!-- Status -->
          <div class="col-md-3 info-item" *ngIf="resolution && resolution.statusId">
            <p class="info-label">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.STATUS' |
              translate }}</p>
            <!-- <p class="info-value">{{ resolution?.resolutionStatus?.nameAr ||
              resolution?.resolutionStatus?.nameEn || getStatusDisplay() }}</p> -->

            <span class="status" [ngClass]="getStatusClass(resolution?.statusId)">
              {{resolution?.resolutionStatus?.localizedName | translate }}
            </span>
          </div>

          <!-- Resolution Type -->
          <div class="col-md-3 info-item">
            <p class="info-label">{{
              'INVESTMENT_FUNDS.RESOLUTIONS.TYPE_DECISION' | translate }}</p>
            <p class="info-value">{{ resolution?.resolutionType?.nameAr ||
              resolution?.resolutionType?.nameEn || 'N/A' }}</p>
          </div>

        </div>
        <div class="row" style="margin-bottom: 28px;">

          <div class="col-md-3 info-item" *ngIf="resolution?.resolutionType?.isOther">
            <p class="info-label">{{
              'INVESTMENT_FUNDS.RESOLUTIONS.TYPE_DECISION_ADDED' | translate
              }}</p>
            <p class="info-value">{{ resolution?.newType || 'N/A' }}</p>
          </div>

          <div class="col-md-3 info-item">
            <p class="info-label">{{
              'INVESTMENT_FUNDS.RESOLUTIONS.MEMBER_VOTING_DECISION' | translate
              }}</p>
            <p class="info-value">{{ resolution?.votingTypeDisplay }}</p>
          </div>

          <div class="col-md-3 info-item">
            <p class="info-label">{{
              'INVESTMENT_FUNDS.RESOLUTIONS.MEMBER_VOTING_RESULT' | translate
              }}</p>
            <p class="info-value">{{ resolution?.memberVotingResultDisplay
              }}</p>
          </div>

          <div class="col-md-3 info-item">
            <p class="info-label"> {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ATTACHMENTS'
              | translate }} </p>
            <p class="info-value">{{ resolution?.attachment?.fileName }}</p>
          </div>

        </div>

        <!-- Description -->
        <div class="col-md-3 info-item full-width" *ngIf="resolution?.description">
          <p class="info-label">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.DESCRIPTION' |
            translate }}</p>
          <!-- <p class="info-value">{{ resolution?.description }}</p> -->
          <p class="info-value description-text" [title]="resolution.description"> {{ resolution.description }}</p>

        </div>

        <!-- Fund Name -->
        <!-- <div class="col-md-3 info-item">
        <p class="info-label">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.FUND_NAME' | translate }}</p>
        <p class="info-value">{{ resolution?.fundName || 'N/A' }}</p>
      </div> -->
        <!-- Last Updated -->
        <!-- <div class="col-md-3 info-item" *ngIf="resolution?.lastUpdated">
        <p class="info-label">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.LAST_UPDATED' | translate }}</p>
        <p class="info-value">{{ formatDate(resolution?.lastUpdated) }}</p>
      </div> -->
      </div>
    </div>

    <div class=" resolution-details-container mt-3">
      <div class="resolution-details-header">
        <p class="section-title navy-color">
          {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ITEMS' | translate }}
          <span> {{resolution?.resolutionItems?.length }}{{
            'INVESTMENT_FUNDS.RESOLUTIONS.ITEMS_WITHOUT' | translate
            }}</span>

        </p>
        <div class="header-actions">
          <button class="expand-button" (click)="toggleExpandItems()">
            <img [src]="isExpandedItem ?'assets/images/accrdion_up.png'
              :'assets/images/accrdion_down.png'" alt="edit" style="width:
              14px;height: 8px;" />
          </button>

        </div>
      </div>
      <hr *ngIf="isExpandedItem" style="margin-bottom: 16PX;">
      <div class="resolution-details-content" style="max-height: 400px;
    overflow-y: scroll;" [class.expanded]="isExpandedItem">
        <div class="row" style="margin-bottom: 28px;" *ngFor="let item of resolution?.resolutionItems">
          <!-- *ngFor="let item of resolution?.resolutionItems" -->
          <div class="item-container col-12">
            <div class="top-section d-flex justify-content-between w-100
              align-items-center">
              <p class="title">
                {{item.title}} </p>
              <button class="conflict-btn" *ngIf="item.conflictMembersCount > 0 "
                (click)="onViewConflictMembers(item.conflictMembers)">
                {{'INVESTMENT_FUNDS.RESOLUTIONS.HAVE_CONFLICT' | translate }}

                ({{item?.conflictMembersCount }} {{'INVESTMENT_FUNDS.RESOLUTIONS.MEMBERS_COUNT' | translate}})
              </button>
            </div>
            <p class="sub-title">
              {{item?.description}}

            </p>
          </div>

        </div>
      </div>
    </div>

    <div class=" resolution-details-container mt-3" *ngIf="resolution.statusId !=1">
      <div class="resolution-details-header">
        <p class="section-title navy-color">
          {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ACTIONS' | translate }}

        </p>
        <div class="header-actions">
          <button class="expand-button" (click)="toggleExpandActions()">
            <img [src]="isExpandedAction ?'assets/images/accrdion_up.png'
                :'assets/images/accrdion_down.png'" alt="edit" style="width:
                14px;height: 8px;" />
          </button>

        </div>

      </div>
      <hr *ngIf="isExpandedAction" style="margin-bottom: 16PX;">
      <div class="resolution-details-content"  [class.expanded]="isExpandedAction">
        <app-timeline [steps]="resolutionStatus"></app-timeline>
      </div>
    </div>
  </div>

  <div class="attachment-section col-4">
    <p class="title">
      {{ 'INVESTMENT_FUNDS.RESOLUTIONS.FILES' | translate }}
    </p>
    <hr>
    <p class="sub-title">
      {{ 'INVESTMENT_FUNDS.RESOLUTIONS.DECISION_FILE' | translate }}
    </p>
    <div class="mb-3">
      <app-attachment-card [attachment]="resolution?.attachment"></app-attachment-card>

    </div>

    <hr>
    <div>
      <p class="sub-title">
        {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ATTACHMENTS' | translate }}
        <span class="attachment-number">{{resolution?.otherAttachments?.length}}</span>
      </p>
      <app-attachment-card [attachment]="resolution?.otherAttachments"></app-attachment-card>
    </div>
  </div>
</div>