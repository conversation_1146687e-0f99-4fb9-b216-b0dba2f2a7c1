<!-- Breadcrumb Navigation -->
<div class="breadcrumb-container">
  <app-breadcrumb 
    [breadcrumbItems]="breadcrumbItems" 
    [size]="breadcrumbSizeEnum.SMALL"
    (onClickEvent)="onBreadcrumbClicked($event)">
  </app-breadcrumb>
</div>

<!-- Loading State -->
<div class="loading-container" *ngIf="isLoading">
  <mat-spinner></mat-spinner>
  <p class="loading-text">{{ 'COMMON.LOADING' | translate }}</p>
</div>

<!-- Error State -->
<div class="error-container" *ngIf="hasError && !isLoading">
  <mat-icon class="error-icon">error</mat-icon>
  <p class="error-message">{{ errorMessage }}</p>
  <app-custom-button
    [buttonType]="buttonTypeEnum.PRIMARY"
    [icon]="iconEnum.ARROW_BACK"
    [text]="'COMMON.BACK_TO_LIST' | translate"
    (click)="onBackToList()">
  </app-custom-button>
</div>

<!-- Main Content -->
<div class="resolution-details-container" *ngIf="!isLoading && !hasError && resolution">
  
  <!-- Header Section -->
  <div class="details-header">
    <div class="header-content">
      <h2 class="page-title">{{ 'RESOLUTIONS.RESOLUTION_DETAILS' | translate }}</h2>
      <div class="header-actions">
        <app-custom-button
          [buttonType]="buttonTypeEnum.SECONDARY"
          [icon]="iconEnum.ARROW_BACK"
          [text]="'COMMON.BACK_TO_LIST' | translate"
          (click)="onBackToList()">
        </app-custom-button>
      </div>
    </div>
  </div>

  <!-- Resolution Status Badge -->
  <div class="status-section">
    <mat-chip-set>
      <mat-chip [ngClass]="getStatusClass()">
        {{ getStatusDisplay() }}
      </mat-chip>
    </mat-chip-set>
  </div>

  <!-- Basic Information Card -->
  <mat-card class="info-card">
    <mat-card-header>
      <mat-card-title>{{ 'RESOLUTIONS.BASIC_INFO' | translate }}</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="info-grid">
        <!-- Resolution Code -->
        <div class="info-item">
          <label class="info-label">{{ 'RESOLUTIONS.RESOLUTION_CODE' | translate }}:</label>
          <span class="info-value">{{ resolution.code }}</span>
        </div>

        <!-- Parent Resolution (if exists) -->
        <div class="info-item" *ngIf="resolution.parentResolutionId">
          <label class="info-label">{{ 'RESOLUTIONS.PARENT_RESOLUTION' | translate }}:</label>
          <span class="info-value">{{ resolution.parentResolutionId }}</span>
        </div>

        <!-- Old Resolution Code (if exists) -->
        <div class="info-item" *ngIf="resolution.oldResolutionCode">
          <label class="info-label">{{ 'RESOLUTIONS.OLD_RESOLUTION_CODE' | translate }}:</label>
          <span class="info-value">{{ resolution.oldResolutionCode }}</span>
        </div>

        <!-- Resolution Date -->
        <div class="info-item">
          <label class="info-label">{{ 'RESOLUTIONS.RESOLUTION_DATE' | translate }}:</label>
          <span class="info-value">{{ formatDate(resolution.resolutionDate) }}</span>
        </div>

        <!-- Description -->
        <div class="info-item full-width" *ngIf="resolution.description">
          <label class="info-label">{{ 'RESOLUTIONS.DESCRIPTION' | translate }}:</label>
          <span class="info-value">{{ resolution.description }}</span>
        </div>

        <!-- Resolution Type -->
        <div class="info-item">
          <label class="info-label">{{ 'RESOLUTIONS.TYPE' | translate }}:</label>
          <span class="info-value">{{ resolution.resolutionTypeId }}</span>
        </div>

        <!-- Custom Type (if exists) -->
        <div class="info-item" *ngIf="resolution.newType">
          <label class="info-label">{{ 'RESOLUTIONS.CUSTOM_TYPE' | translate }}:</label>
          <span class="info-value">{{ resolution.newType }}</span>
        </div>

        <!-- Voting Methodology -->
        <div class="info-item">
          <label class="info-label">{{ 'RESOLUTIONS.VOTING_METHODOLOGY' | translate }}:</label>
          <span class="info-value">
            {{ resolution.votingType === 1 ? ('RESOLUTIONS.ALL_MEMBERS' | translate) : ('RESOLUTIONS.MAJORITY_MEMBERS' | translate) }}
          </span>
        </div>

        <!-- Member Voting Result -->
        <div class="info-item">
          <label class="info-label">{{ 'RESOLUTIONS.MEMBER_VOTING_RESULT' | translate }}:</label>
          <span class="info-value">
            {{ resolution.memberVotingResult === 1 ? ('RESOLUTIONS.ALL_ITEMS' | translate) : ('RESOLUTIONS.MAJORITY_ITEMS' | translate) }}
          </span>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Resolution File Card -->
  <mat-card class="file-card" *ngIf="resolution.attachmentId">
    <mat-card-header>
      <mat-card-title>{{ 'RESOLUTIONS.RESOLUTION_FILE' | translate }}</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="file-actions">
        <app-custom-button
          [buttonType]="buttonTypeEnum.PRIMARY"
          [icon]="iconEnum.DOWNLOAD"
          [text]="'COMMON.DOWNLOAD' | translate"
          (click)="onDownloadFile()">
        </app-custom-button>
        <app-custom-button
          [buttonType]="buttonTypeEnum.SECONDARY"
          [icon]="iconEnum.VISIBILITY"
          [text]="'COMMON.OPEN' | translate"
          (click)="onOpenFile()">
        </app-custom-button>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Attachments Card -->
  <mat-card class="attachments-card" *ngIf="resolution.resolutionItems && resolution.resolutionItems.length > 0">
    <mat-card-header>
      <mat-card-title>
        {{ 'RESOLUTIONS.ATTACHMENTS' | translate }} 
        <span class="attachment-counter">({{ resolution.resolutionItems.length }})</span>
      </mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="attachments-list">
        <div class="attachment-item" *ngFor="let item of resolution.resolutionItems">
          <div class="attachment-info">
            <span class="attachment-name">{{ item.title || 'RESOLUTIONS.ATTACHMENT' | translate }}</span>
            <span class="attachment-size">{{ item.description || '' }}</span>
          </div>
          <div class="attachment-actions">
            <button mat-icon-button (click)="onDownloadAttachment(item)">
              <mat-icon>download</mat-icon>
            </button>
          </div>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Resolution Items Card - Only for specific statuses -->
  <mat-card class="items-card" *ngIf="shouldShowResolutionItems() && resolution.resolutionItems && resolution.resolutionItems.length > 0">
    <mat-card-header>
      <mat-card-title>{{ 'RESOLUTIONS.RESOLUTION_ITEMS' | translate }}</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="items-list">
        <div class="item-card" *ngFor="let item of resolution.resolutionItems; let i = index">
          <div class="item-header">
            <h4 class="item-title">{{ item.title || ('RESOLUTIONS.ITEM' | translate) + ' ' + (i + 1) }}</h4>
          </div>
          <div class="item-content">
            <p class="item-description" *ngIf="item.description">{{ item.description }}</p>
            <div class="conflict-info" *ngIf="item.hasConflict">
              <mat-icon class="conflict-icon">warning</mat-icon>
              <span class="conflict-text">{{ 'RESOLUTIONS.CONFLICT_EXISTS' | translate }}</span>
              <button mat-button class="view-members-btn" (click)="onViewConflictMembers(item)">
                {{ 'RESOLUTIONS.VIEW_MEMBERS' | translate }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Resolution History Card - For non-draft statuses -->
  <mat-card class="history-card" *ngIf="shouldShowResolutionHistory()">
    <mat-card-header>
      <mat-card-title>{{ 'RESOLUTIONS.RESOLUTION_HISTORY' | translate }}</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="history-list">
        <div class="history-item" *ngFor="let historyItem of getResolutionHistory()">
          <div class="history-info">
            <span class="action-name">{{ historyItem.actionName | translate }}</span>
            <span class="user-role">{{ historyItem.role | translate }}</span>
            <span class="user-name">{{ historyItem.userName }}</span>
            <span class="date-time">{{ historyItem.dateTime }}</span>
          </div>
        </div>

        <!-- Placeholder message when no history API is available -->
        <div class="no-history" *ngIf="getResolutionHistory().length === 0">
          <p class="no-history-text">{{ 'RESOLUTIONS.HISTORY_NOT_AVAILABLE' | translate }}</p>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Rejection Reason Card - Only for rejected status -->
  <mat-card class="rejection-card" *ngIf="shouldShowRejectionReason()">
    <mat-card-header>
      <mat-card-title>{{ 'RESOLUTIONS.REJECTION_REASON' | translate }}</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="rejection-content">
        <!-- TODO: Add rejection reason field from API -->
        <p class="rejection-text">{{ 'RESOLUTIONS.REJECTION_REASON_PLACEHOLDER' | translate }}</p>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Action Buttons for Legal Council -->
  <div class="action-buttons" *ngIf="shouldShowConfirmRejectButtons()">
    <app-custom-button
      [buttonType]="buttonTypeEnum.SUCCESS"
      [icon]="iconEnum.CHECK"
      [text]="'RESOLUTIONS.CONFIRM' | translate"
      (click)="onConfirmResolution()">
    </app-custom-button>
    <app-custom-button
      [buttonType]="buttonTypeEnum.DANGER"
      [icon]="iconEnum.CLOSE"
      [text]="'RESOLUTIONS.REJECT' | translate"
      (click)="onRejectResolution()">
    </app-custom-button>
  </div>

</div>
