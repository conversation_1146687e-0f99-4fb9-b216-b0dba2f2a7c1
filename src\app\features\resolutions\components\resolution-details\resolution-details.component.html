<!-- Breadcrumb Navigation -->
<div class="breadcrumb-container">
  <app-breadcrumb
    [breadcrumbs]="breadcrumbItems"
    [size]="breadcrumbSizeEnum.Small"
    (onClickEvent)="onBreadcrumbClicked($event)">
  </app-breadcrumb>
</div>

<!-- Loading State -->
<div class="loading-container" *ngIf="isLoading">
  <mat-spinner></mat-spinner>
  <p class="loading-text">{{ 'COMMON.LOADING' | translate }}</p>
</div>

<!-- Error State -->
<div class="error-container" *ngIf="hasError && !isLoading">
  <mat-icon class="error-icon">error</mat-icon>
  <p class="error-message">{{ errorMessage }}</p>
  <app-custom-button
    [buttonType]="buttonTypeEnum.Primary"
    [iconName]="iconEnum.arrowLeft"
    [btnName]="'COMMON.BACK_TO_LIST' | translate"
    (click)="onBackToList()">
  </app-custom-button>
</div>

<!-- Main Content -->
<div class="resolution-details-container" *ngIf="!isLoading && !hasError && resolution">
  
  <!-- Header Section -->
  <div class="details-header">
    <div class="header-content">
      <h2 class="page-title">{{ 'RESOLUTIONS.RESOLUTION_DETAILS' | translate }}</h2>
      <div class="header-actions">
        <app-custom-button
          [buttonType]="buttonTypeEnum.Secondary"
          [iconName]="iconEnum.arrowLeft"
          [btnName]="'COMMON.BACK_TO_LIST' | translate"
          (click)="onBackToList()">
        </app-custom-button>
      </div>
    </div>
  </div>

  <!-- Resolution Status Badge -->
  <div class="status-section">
    <mat-chip-set>
      <mat-chip [ngClass]="getStatusClass()">
        {{ getStatusDisplay() }}
      </mat-chip>
    </mat-chip-set>
  </div>

  <!-- Basic Information Card -->
  <mat-card class="info-card">
    <mat-card-header>
      <mat-card-title>{{ 'RESOLUTIONS.BASIC_INFO' | translate }}</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="info-grid">
        <!-- Resolution Code -->
        <div class="info-item">
          <label class="info-label">{{ 'RESOLUTIONS.RESOLUTION_CODE' | translate }}:</label>
          <span class="info-value">{{ resolution.code }}</span>
        </div>

        <!-- Note: Parent Resolution and Old Resolution Code properties are not available in current API model -->

        <!-- Resolution Date -->
        <div class="info-item">
          <label class="info-label">{{ 'RESOLUTIONS.RESOLUTION_DATE' | translate }}:</label>
          <span class="info-value">{{ formatDate(resolution.resolutionDate) }}</span>
        </div>

        <!-- Description -->
        <div class="info-item full-width" *ngIf="resolution.description">
          <label class="info-label">{{ 'RESOLUTIONS.DESCRIPTION' | translate }}:</label>
          <span class="info-value">{{ resolution.description }}</span>
        </div>

        <!-- Resolution Type -->
        <div class="info-item">
          <label class="info-label">{{ 'RESOLUTIONS.TYPE' | translate }}:</label>
          <span class="info-value">{{ resolution.resolutionType.nameAr || resolution.resolutionType.nameEn || 'N/A' }}</span>
        </div>

        <!-- Status -->
        <div class="info-item">
          <label class="info-label">{{ 'RESOLUTIONS.STATUS' | translate }}:</label>
          <span class="info-value">{{ resolution.resolutionStatus.nameAr || resolution.resolutionStatus.nameEn || getStatusDisplay() }}</span>
        </div>

        <!-- Fund Name -->
        <div class="info-item">
          <label class="info-label">{{ 'RESOLUTIONS.FUND_NAME' | translate }}:</label>
          <span class="info-value">{{ resolution.fundName || 'N/A' }}</span>
        </div>

        <!-- Last Updated -->
        <div class="info-item" *ngIf="resolution.lastUpdated">
          <label class="info-label">{{ 'RESOLUTIONS.LAST_UPDATED' | translate }}:</label>
          <span class="info-value">{{ formatDate(resolution.lastUpdated) }}</span>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Resolution File Card - Placeholder for future implementation -->
  <mat-card class="file-card" *ngIf="false">
    <mat-card-header>
      <mat-card-title>{{ 'RESOLUTIONS.RESOLUTION_FILE' | translate }}</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="file-actions">
        <button mat-raised-button color="primary" (click)="onDownloadFile()">
          <mat-icon>download</mat-icon>
          {{ 'COMMON.DOWNLOAD' | translate }}
        </button>
        <button mat-raised-button color="accent" (click)="onOpenFile()">
          <mat-icon>visibility</mat-icon>
          {{ 'COMMON.OPEN' | translate }}
        </button>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Attachments Card - Placeholder for future implementation -->
  <mat-card class="attachments-card" *ngIf="false">
    <mat-card-header>
      <mat-card-title>
        {{ 'RESOLUTIONS.ATTACHMENTS' | translate }}
        <span class="attachment-counter">(0)</span>
      </mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="attachments-list">
        <p class="no-attachments">{{ 'RESOLUTIONS.NO_ATTACHMENTS' | translate }}</p>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Resolution Items Card - Placeholder for future implementation -->
  <mat-card class="items-card" *ngIf="false">
    <mat-card-header>
      <mat-card-title>{{ 'RESOLUTIONS.RESOLUTION_ITEMS' | translate }}</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="items-list">
        <p class="no-items">{{ 'RESOLUTIONS.NO_ITEMS' | translate }}</p>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Resolution History Card - For non-draft statuses -->
  <mat-card class="history-card" *ngIf="shouldShowResolutionHistory()">
    <mat-card-header>
      <mat-card-title>{{ 'RESOLUTIONS.RESOLUTION_HISTORY' | translate }}</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="history-list">
        <div class="history-item" *ngFor="let historyItem of getResolutionHistory()">
          <div class="history-info">
            <span class="action-name">{{ historyItem.actionName | translate }}</span>
            <span class="user-role">{{ historyItem.role | translate }}</span>
            <span class="user-name">{{ historyItem.userName }}</span>
            <span class="date-time">{{ historyItem.dateTime }}</span>
          </div>
        </div>

        <!-- Placeholder message when no history API is available -->
        <div class="no-history" *ngIf="getResolutionHistory().length === 0">
          <p class="no-history-text">{{ 'RESOLUTIONS.HISTORY_NOT_AVAILABLE' | translate }}</p>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Rejection Reason Card - Only for rejected status -->
  <mat-card class="rejection-card" *ngIf="shouldShowRejectionReason()">
    <mat-card-header>
      <mat-card-title>{{ 'RESOLUTIONS.REJECTION_REASON' | translate }}</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="rejection-content">
        <!-- TODO: Add rejection reason field from API -->
        <p class="rejection-text">{{ 'RESOLUTIONS.REJECTION_REASON_PLACEHOLDER' | translate }}</p>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Action Buttons for Legal Council -->
  <div class="action-buttons" *ngIf="shouldShowConfirmRejectButtons()">
    <button mat-raised-button color="primary" (click)="onConfirmResolution()">
      <mat-icon>check</mat-icon>
      {{ 'RESOLUTIONS.CONFIRM' | translate }}
    </button>
    <button mat-raised-button color="warn" (click)="onRejectResolution()">
      <mat-icon>close</mat-icon>
      {{ 'RESOLUTIONS.REJECT' | translate }}
    </button>
  </div>

</div>
