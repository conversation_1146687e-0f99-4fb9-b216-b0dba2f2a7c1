.breadcrumb-container {
  margin-bottom: 1rem;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  
  .loading-text {
    margin-top: 1rem;
    color: var(--text-secondary);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  
  .error-icon {
    font-size: 3rem;
    color: var(--error-color);
    margin-bottom: 1rem;
  }
  
  .error-message {
    color: var(--error-color);
    margin-bottom: 2rem;
    font-size: 1.1rem;
  }
}

.resolution-details-container {
  padding: 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

.details-header {
  margin-bottom: 2rem;
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .page-title {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.75rem;
    font-weight: 600;
  }
  
  .header-actions {
    display: flex;
    gap: 0.5rem;
  }
}

.status-section {
  margin-bottom: 2rem;
  
  mat-chip {
    font-weight: 500;
    
    &.status-1 { // Draft
      background-color: #e3f2fd;
      color: #1976d2;
    }
    
    &.status-2 { // Pending
      background-color: #fff3e0;
      color: #f57c00;
    }
    
    &.status-3 { // Approved/Confirmed
      background-color: #e8f5e8;
      color: #2e7d32;
    }
    
    &.status-4 { // Rejected/Cancelled
      background-color: #ffebee;
      color: #c62828;
    }
    
    &.status-5 { // Completing data
      background-color: #f3e5f5;
      color: #7b1fa2;
    }
    
    &.status-6 { // Waiting for confirmation
      background-color: #e0f2f1;
      color: #00695c;
    }
  }
}

.info-card, .file-card, .attachments-card, .items-card, .history-card, .rejection-card {
  margin-bottom: 2rem;

  mat-card-header {
    margin-bottom: 1rem;

    mat-card-title {
      color: var(--primary-color);
      font-size: 1.25rem;
      font-weight: 600;
    }
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  
  .info-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    
    &.full-width {
      grid-column: 1 / -1;
    }
    
    .info-label {
      font-weight: 600;
      color: var(--text-primary);
      font-size: 0.9rem;
    }
    
    .info-value {
      color: var(--text-secondary);
      font-size: 1rem;
      word-break: break-word;
    }
  }
}

.file-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.attachments-list {
  .attachment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 0.5rem;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .attachment-info {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;
      
      .attachment-name {
        font-weight: 500;
        color: var(--text-primary);
      }
      
      .attachment-size {
        font-size: 0.875rem;
        color: var(--text-secondary);
      }
    }
    
    .attachment-actions {
      display: flex;
      gap: 0.5rem;
    }
  }
}

.attachment-counter {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: normal;
}

.items-list {
  .item-card {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .item-header {
      margin-bottom: 1rem;
      
      .item-title {
        margin: 0;
        color: var(--primary-color);
        font-size: 1.1rem;
        font-weight: 600;
      }
    }
    
    .item-content {
      .item-description {
        margin-bottom: 1rem;
        color: var(--text-secondary);
        line-height: 1.6;
      }
      
      .conflict-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem;
        background-color: #fff3e0;
        border-radius: 6px;
        border-left: 4px solid #ff9800;
        
        .conflict-icon {
          color: #ff9800;
          font-size: 1.25rem;
        }
        
        .conflict-text {
          color: #e65100;
          font-weight: 500;
        }
        
        .view-members-btn {
          margin-left: auto;
          color: var(--primary-color);
        }
      }
    }
  }
}

.history-list {
  .history-item {
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 0.5rem;

    &:last-child {
      margin-bottom: 0;
    }

    .history-info {
      display: grid;
      grid-template-columns: 2fr 1fr 1fr 1fr;
      gap: 1rem;
      align-items: center;

      .action-name {
        font-weight: 600;
        color: var(--text-primary);
      }

      .user-role {
        color: var(--primary-color);
        font-size: 0.9rem;
      }

      .user-name {
        color: var(--text-secondary);
        font-size: 0.9rem;
      }

      .date-time {
        color: var(--text-secondary);
        font-size: 0.875rem;
      }
    }
  }
}

.rejection-card {
  border-left: 4px solid var(--error-color);

  .rejection-content {
    .rejection-text {
      color: var(--text-secondary);
      line-height: 1.6;
      font-style: italic;
    }
  }
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  padding: 2rem 0;
  border-top: 1px solid var(--border-color);
}

// Responsive design
@media (max-width: 768px) {
  .resolution-details-container {
    padding: 0.5rem;
  }
  
  .details-header .header-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .file-actions {
    flex-direction: column;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .attachment-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}

// RTL support
[dir="rtl"] {
  .details-header .header-content {
    text-align: right;
  }
  
  .info-item {
    text-align: right;
  }
  
  .conflict-info {
    border-left: none;
    border-right: 4px solid #ff9800;
  }
}
