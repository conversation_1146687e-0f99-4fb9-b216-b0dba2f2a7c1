import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';

// Core imports
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { SizeEnum } from '@shared/enum/size-enum';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';

// API imports
import {
  ResolutionsServiceProxy,
  SingleResolutionResponse,
  ResolutionStatusEnum,
  RejectResolutionCommand
} from '@core/api/api.generated';
import { TokenService } from '../../../auth/services/token.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-resolution-details',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatChipsModule,
    BreadcrumbComponent,
    CustomButtonComponent
  ],
  templateUrl: './resolution-details.component.html',
  styleUrls: ['./resolution-details.component.scss']
})
export class ResolutionDetailsComponent implements OnInit {
  // Data properties
  resolution: SingleResolutionResponse | null = null;
  resolutionId: number = 0;
  fundId: number = 0;
  fundName: string = '';

  // Loading and error states
  isLoading = false;
  hasError = false;
  errorMessage = '';

  // Role-based access control
  userRole: string = '';
  canConfirmReject = false;
  canViewDetails = false;

  // UI state
  breadcrumbSizeEnum = SizeEnum;
  buttonTypeEnum = ButtonTypeEnum;
  iconEnum = IconEnum;

  // Breadcrumb configuration
  breadcrumbItems: IBreadcrumbItem[] = [];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private resolutionsProxy: ResolutionsServiceProxy,
    private tokenService: TokenService,
    private translateService: TranslateService
  ) {}

  ngOnInit(): void {
    this.initializeComponent();
  }

  private initializeComponent(): void {
    // Get route parameters
    this.route.params.subscribe(params => {
      this.resolutionId = +params['id'];
      
      // Get fundId from query params or route params
      this.route.queryParams.subscribe(queryParams => {
        this.fundId = +queryParams['fundId'] || +params['fundId'] || 0;
        
        if (this.resolutionId && this.fundId) {
          this.initializeRoleBasedAccess();
          this.setupBreadcrumbs();
          this.loadResolutionDetails();
        } else {
          this.handleError('RESOLUTIONS.INVALID_PARAMETERS');
        }
      });
    });
  }

  private initializeRoleBasedAccess(): void {
    // Determine user role based on permissions
    if (this.tokenService.hasRole('FundManager')) {
      this.userRole = 'FundManager';
      this.canViewDetails = true;
      this.canConfirmReject = false;
    } else if (this.tokenService.hasRole('LegalCouncil') || this.tokenService.hasRole('BoardSecretary')) {
      this.userRole = 'LegalCouncil';
      this.canViewDetails = true;
      this.canConfirmReject = true;
    } else if (this.tokenService.hasRole('BoardMember')) {
      this.userRole = 'BoardMember';
      this.canViewDetails = true;
      this.canConfirmReject = false;
    } else {
      this.userRole = 'Default';
      this.canViewDetails = false;
      this.canConfirmReject = false;
    }
  }

  private setupBreadcrumbs(): void {
    this.breadcrumbItems = [
      { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
      { label: 'BREADCRUMB.FUNDS', url: '/admin/investment-funds' },
      { 
        label: 'RESOLUTIONS.TITLE', 
        url: `/admin/investment-funds/resolutions?fundId=${this.fundId}` 
      },
      { label: 'RESOLUTIONS.DETAILS', url: '' }
    ];
  }

  private loadResolutionDetails(): void {
    this.isLoading = true;
    this.hasError = false;

    this.resolutionsProxy.getResolutionById(this.resolutionId).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.successed && response.data) {
          this.resolution = response.data;
          this.validateAccess();
        } else {
          this.handleError('RESOLUTIONS.FAILED_TO_LOAD');
        }
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Error loading resolution details:', error);
        this.handleError('RESOLUTIONS.FAILED_TO_LOAD');
      }
    });
  }

  private validateAccess(): void {
    if (!this.resolution || !this.canViewDetails) {
      this.handleError('RESOLUTIONS.ACCESS_DENIED');
      return;
    }

    // Check if user role can view this resolution status
    const allowedStatuses = this.getAllowedStatusesForRole();
    if (!allowedStatuses.includes(this.resolution.status)) {
      this.handleError('RESOLUTIONS.ACCESS_DENIED');
      return;
    }
  }

  private getAllowedStatusesForRole(): ResolutionStatusEnum[] {
    switch (this.userRole) {
      case 'FundManager':
        // JDWA-588: draft/pending/cancelled + JDWA-593: completing data/waiting for confirmation/confirmed/rejected
        // Fund manager can view all resolution statuses
        return [
          ResolutionStatusEnum._1, // Draft
          ResolutionStatusEnum._2, // Pending
          ResolutionStatusEnum._3, // Approved/Confirmed
          ResolutionStatusEnum._4, // Rejected/Cancelled
          ResolutionStatusEnum._5, // Voting in progress
          ResolutionStatusEnum._6  // Not approved
        ];
      case 'LegalCouncil':
        // JDWA-584: pending/cancelled + JDWA-589: completing data/waiting for confirmation/confirmed/rejected
        // Legal council/board secretary cannot view draft status
        return [
          ResolutionStatusEnum._2, // Pending
          ResolutionStatusEnum._3, // Approved/Confirmed
          ResolutionStatusEnum._4, // Rejected/Cancelled
          ResolutionStatusEnum._5, // Voting in progress
          ResolutionStatusEnum._6  // Not approved
        ];
      case 'BoardMember':
        // Board members have limited access - only voting and final statuses
        return [
          ResolutionStatusEnum._3, // Approved/Confirmed
          ResolutionStatusEnum._4, // Rejected/Cancelled
          ResolutionStatusEnum._5, // Voting in progress
          ResolutionStatusEnum._6  // Not approved
        ];
      default:
        return [];
    }
  }

  private handleError(messageKey: string): void {
    this.hasError = true;
    this.errorMessage = this.translateService.instant(messageKey);

    Swal.fire({
      title: this.translateService.instant('COMMON.ERROR'),
      text: this.errorMessage,
      icon: 'error',
      confirmButtonText: this.translateService.instant('COMMON.OK')
    });
  }

  // Action methods
  onConfirmResolution(): void {
    if (!this.resolution || !this.canConfirmReject) return;

    Swal.fire({
      title: this.translateService.instant('RESOLUTIONS.CONFIRM_RESOLUTION'),
      text: this.translateService.instant('RESOLUTIONS.CONFIRM_RESOLUTION_TEXT'),
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#28a745',
      cancelButtonColor: '#6c757d',
      confirmButtonText: this.translateService.instant('RESOLUTIONS.CONFIRM'),
      cancelButtonText: this.translateService.instant('COMMON.CANCEL')
    }).then((result) => {
      if (result.isConfirmed) {
        this.isLoading = true;

        this.resolutionsProxy.confirmResolution(this.resolution!.id).subscribe({
          next: (response) => {
            this.isLoading = false;
            if (response.successed) {
              Swal.fire({
                title: this.translateService.instant('COMMON.SUCCESS'),
                text: this.translateService.instant('RESOLUTIONS.CONFIRMED_SUCCESSFULLY'),
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
              }).then(() => {
                // Reload the resolution details to show updated status
                this.loadResolutionDetails();
              });
            } else {
              this.handleError('RESOLUTIONS.CONFIRM_FAILED');
            }
          },
          error: (error) => {
            this.isLoading = false;
            console.error('Error confirming resolution:', error);
            this.handleError('RESOLUTIONS.CONFIRM_FAILED');
          }
        });
      }
    });
  }

  onRejectResolution(): void {
    if (!this.resolution || !this.canConfirmReject) return;

    Swal.fire({
      title: this.translateService.instant('RESOLUTIONS.REJECT_RESOLUTION'),
      text: this.translateService.instant('RESOLUTIONS.REJECT_RESOLUTION_TEXT'),
      input: 'textarea',
      inputLabel: this.translateService.instant('RESOLUTIONS.REJECTION_REASON'),
      inputPlaceholder: this.translateService.instant('RESOLUTIONS.ENTER_REJECTION_REASON'),
      inputValidator: (value) => {
        if (!value || value.trim().length === 0) {
          return this.translateService.instant('RESOLUTIONS.REJECTION_REASON_REQUIRED');
        }
        return null;
      },
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#dc3545',
      cancelButtonColor: '#6c757d',
      confirmButtonText: this.translateService.instant('RESOLUTIONS.REJECT'),
      cancelButtonText: this.translateService.instant('COMMON.CANCEL')
    }).then((result) => {
      if (result.isConfirmed && result.value) {
        this.isLoading = true;

        const rejectCommand: RejectResolutionCommand = {
          reason: result.value.trim()
        };

        this.resolutionsProxy.rejectResolution(this.resolution!.id, rejectCommand).subscribe({
          next: (response) => {
            this.isLoading = false;
            if (response.successed) {
              Swal.fire({
                title: this.translateService.instant('COMMON.SUCCESS'),
                text: this.translateService.instant('RESOLUTIONS.REJECTED_SUCCESSFULLY'),
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
              }).then(() => {
                // Reload the resolution details to show updated status
                this.loadResolutionDetails();
              });
            } else {
              this.handleError('RESOLUTIONS.REJECT_FAILED');
            }
          },
          error: (error) => {
            this.isLoading = false;
            console.error('Error rejecting resolution:', error);
            this.handleError('RESOLUTIONS.REJECT_FAILED');
          }
        });
      }
    });
  }

  onBreadcrumbClicked(item: IBreadcrumbItem): void {
    if (item.url) {
      this.router.navigate([item.url]);
    }
  }

  onBackToList(): void {
    this.router.navigate(['/admin/investment-funds/resolutions'], {
      queryParams: { fundId: this.fundId }
    });
  }

  // Utility methods
  getStatusClass(): string {
    if (!this.resolution) return '';
    return `status-${this.resolution.status}`;
  }

  getStatusDisplay(): string {
    if (!this.resolution) return '';
    return this.resolution.statusDisplay || 
           this.translateService.instant(`RESOLUTIONS.STATUS_${this.resolution.status}`);
  }

  shouldShowConfirmRejectButtons(): boolean {
    // Show confirm/reject buttons for legal council/board secretary when resolution is pending
    return this.canConfirmReject &&
           this.resolution?.status === ResolutionStatusEnum._2; // Pending status
  }

  // Status-specific UI methods
  shouldShowBasicInfo(): boolean {
    // Basic info is shown for all authorized users and statuses
    return true;
  }

  shouldShowResolutionItems(): boolean {
    // Resolution items are shown for completing data, waiting for confirmation, confirmed, and rejected statuses
    if (!this.resolution) return false;

    const statusesWithItems = [
      ResolutionStatusEnum._3, // Approved/Confirmed
      ResolutionStatusEnum._4, // Rejected/Cancelled
      ResolutionStatusEnum._5, // Voting in progress (completing data equivalent)
      ResolutionStatusEnum._6  // Not approved (waiting for confirmation equivalent)
    ];

    return statusesWithItems.includes(this.resolution.status);
  }

  shouldShowResolutionHistory(): boolean {
    // History is shown for all statuses except draft
    if (!this.resolution) return false;
    return this.resolution.status !== ResolutionStatusEnum._1; // Not draft
  }

  shouldShowRejectionReason(): boolean {
    // Rejection reason is shown only for rejected status
    if (!this.resolution) return false;
    return this.resolution.status === ResolutionStatusEnum._4; // Rejected/Cancelled
  }

  // User story specific access validation
  isJDWA588Access(): boolean {
    // JDWA-588: Fund manager viewing draft/pending/cancelled
    return this.userRole === 'FundManager' &&
           this.resolution &&
           [ResolutionStatusEnum._1, ResolutionStatusEnum._2, ResolutionStatusEnum._4].includes(this.resolution.status);
  }

  isJDWA584Access(): boolean {
    // JDWA-584: Legal council/board secretary viewing pending/cancelled
    return this.userRole === 'LegalCouncil' &&
           this.resolution &&
           [ResolutionStatusEnum._2, ResolutionStatusEnum._4].includes(this.resolution.status);
  }

  isJDWA593Access(): boolean {
    // JDWA-593: Fund manager viewing completing data/waiting for confirmation/confirmed/rejected
    return this.userRole === 'FundManager' &&
           this.resolution &&
           [ResolutionStatusEnum._3, ResolutionStatusEnum._5, ResolutionStatusEnum._6].includes(this.resolution.status);
  }

  isJDWA589Access(): boolean {
    // JDWA-589: Legal council/board secretary viewing completing data/waiting for confirmation/confirmed/rejected
    return this.userRole === 'LegalCouncil' &&
           this.resolution &&
           [ResolutionStatusEnum._3, ResolutionStatusEnum._5, ResolutionStatusEnum._6].includes(this.resolution.status);
  }

  formatDate(date: any): string {
    if (!date) return '';
    try {
      return new Date(date.toString()).toLocaleDateString('ar-SA');
    } catch {
      return '';
    }
  }

  // File operation methods
  onDownloadFile(): void {
    if (!this.resolution?.attachmentId) return;

    // TODO: Implement file download using FileManagementServiceProxy
    console.log('Downloading file:', this.resolution.attachmentId);
  }

  onOpenFile(): void {
    if (!this.resolution?.attachmentId) return;

    // TODO: Implement file opening/preview
    console.log('Opening file:', this.resolution.attachmentId);
  }

  onDownloadAttachment(item: any): void {
    // TODO: Implement attachment download
    console.log('Downloading attachment:', item);
  }

  onViewConflictMembers(item: any): void {
    // TODO: Implement conflict members dialog
    console.log('Viewing conflict members for item:', item);
  }
}
