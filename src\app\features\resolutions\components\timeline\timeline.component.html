<div class="timeline-container" [attr.dir]="isEnglish() ? 'ltr' : 'rtl'">
  <div class="timeline-item" *ngFor="let step of steps; let last= last">

      <div class="timeline-icon" [ngClass]="getStatusClass(step.status)">
      <img [src]="'assets/images/loading-history.png'" >
    </div>
    <div class="w-100">
      <div class="timeline-content">
        <div class="header">
          <strong>{{ step.displayedAction }}</strong>
          <span class="user-role">
           {{ step.displayedUserRole}}:

            <span class="user-name">{{ step.fullName}}</span>
          </span>
        </div>
        <div>
          <div class="timestamp">

            <span class="time mx-2">{{ step?.createdAt?.split('T')[0] }}</span>

            <span class="date mx-2">{{ step?.createdAt?.split('T')[1].split('.')[0] }} </span>
            <span class="hijri">
              {{ step?.createdAt?.split('T')[0] | dateHijriConverter }}</span>
          </div>
          <div class="decision-state d-flex">


            {{ 'INVESTMENT_FUNDS.RESOLUTIONS.STATUS_FILE' | translate }}:
             <p class="status-badge" [ngClass]="getStatusClass(step.status)">
              {{ step.displayedStatus }}
            </p>
          </div>

        </div>

      </div>
      <div class="reason" *ngIf="step.rejectionReason">

        {{ 'INVESTMENT_FUNDS.RESOLUTIONS.REJECTION_REASON' | translate }}:

         {{ step.rejectionReason }}
      </div>
    </div>

    <div *ngIf="!last" class="vertical-line"></div>

  </div>
</div>
