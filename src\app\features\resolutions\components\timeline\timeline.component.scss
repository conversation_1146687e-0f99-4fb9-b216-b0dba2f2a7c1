/* timeline.component.css */
.timeline-container {
  position: relative;
  padding: 10px;
  max-height: 400px;
    overflow-y: scroll;
}

.timeline-item {
  position: relative;
  display: flex;
  align-items: flex-start;
  padding-right: 30px;
  margin-bottom: 30px;
}

.timeline-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  color: white;
  font-size: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 10px;
  z-index: 1;
}

.timeline-icon {
  &.draft {
    background: rgba(117, 85, 172, 0.1);
    color: #7555ac;
  }
  &.pending {
    background: #fff3cd;
    color: #856404;
  }
  &.completing-data {
    background: rgba(157, 112, 9, 0.27);
    color: #9d7009;
  }
  &.waiting-for-confirmation {
    background: rgba(226, 180, 138, 0.34);
    color: #d16440;
  }
  &.confirmed {
    background: rgba(97, 253, 97, 0.14);

    color: #27ae60;
  }

  &.rejected {
    color: #828282;
    background: #eaeef1;
  }

  &.voting-inProgress {
    background: rgba(47, 128, 237, 0.1);
    color: #2f80ed;
  }

  &.approved {
    background: #f1faf1;
    color: #0e700e;
  }

  &.not-approved {
    background: rgba(197, 15, 31, 0.1);

    color: #c50f1f;
  }

  &.cancelled {
    background: var(--Color---Grey-5, #e0e0e0);

    color: #4f4f4f;
  }
}

.vertical-line {
  position: absolute;
  right: 42px;
  top: 30px;
  bottom: -30px;
  width: 2px;
  background-color: #ddd;
  z-index: 0;
}

.timeline-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
}

.header {
  display: flex;
  flex-direction: column;
  font-weight: 600;
  margin-bottom: 4px;
  strong {
    color: #002447;
    font-size: 14px;
    font-weight: 500;
    line-height: 24px;
  }
  .user-role {
    color: #1d1d1d;
    font-size: 12px;
    font-weight: 400;
    line-height: 20px;
  }
  .user-name {
    color: #4b5662;
    font-size: 12px;
    font-weight: 400;
    line-height: 20px;
  }
}

.timestamp {
  color: gray;
  font-size: 12px;
  margin-bottom: 6px;
  .date {
    color: #00205a;
    font-size: 12px;
    font-weight: 400;
    line-height: 24px;
  }
  .hijri {
    color: #828282;
    font-size: 10px;
    font-weight: 400;
    line-height: 16px;
  }
  .time {
    color: #828282;
    font-size: 10px;
    font-weight: 400;
    line-height: 16px;
  }
}

.decision-state {
  color: #1d1d1d;
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  height: 24px;
  border-radius: 20px;
  padding: 10px;

  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  margin-right: 6px;

  &.draft {
    background: rgba(117, 85, 172, 0.1);
    color: #7555ac;
  }
  &.pending {
    background: #fff3cd;
    color: #856404;
  }
  &.completing-data {
    background: rgba(157, 112, 9, 0.27);
    color: #9d7009;
  }
  &.waiting-for-confirmation {
    background: rgba(226, 180, 138, 0.34);
    color: #d16440;
  }
  &.confirmed {
    background: rgba(97, 253, 97, 0.14);

    color: #27ae60;
  }

  &.rejected {
    color: #828282;
    background: #eaeef1;
  }

  &.voting-inProgress {
    background: rgba(47, 128, 237, 0.1);
    color: #2f80ed;
  }

  &.approved {
    background: #f1faf1;
    color: #0e700e;
  }

  &.not-approved {
    background: rgba(197, 15, 31, 0.1);

    color: #c50f1f;
  }

  &.cancelled {
    background: var(--Color---Grey-5, #e0e0e0);

    color: #4f4f4f;
  }
}

.status-rejected {
  background-color: #e74c3c;
  color: white;
}

.status-pending {
  background-color: #f39c12;
  color: white;
}

.status-awaiting {
  background-color: #f8c471;
  color: #333;
}

.status-default {
  background-color: #ccc;
  color: #333;
}

.reason {
  font-size: 14px;
  margin-top: 4px;
  color: #333;
}

[dir="ltr"] {
  .status-badge {
    margin-left: 6px;
    margin-right: 0;
  }
  .decision-state {
    text-align: left;
    flex-direction: row-reverse;
  }
  .reason {
    text-align: left;
  }
  .vertical-line {
    left: 14px;
    right: auto;
  }
  .timeline-container {
    direction: ltr;
  }
  .timeline-item {
    padding-left: 0px;
    padding-right: 0;
  }
  .timeline-icon {
    margin-right: 10px;
    margin-left: 0;
  }
  .timeline-content {
    text-align: left;
  }
}
