import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import Swal from 'sweetalert2';

// Shared components
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';

// Core interfaces and enums
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { SizeEnum } from '@shared/enum/size-enum';

// API imports
import {
  ResolutionsServiceProxy,
  SingleResolutionResponse,
  ResolutionStatusEnum
} from '@core/api/api.generated';
import { TokenService } from '../auth/services/token.service';
import { DateTime } from 'luxon';
import moment from 'moment';

// Advanced search dialog
import { AdvancedSearchDialogComponent, ResolutionSearchFilters } from './components/advanced-search-dialog/advanced-search-dialog.component';
import { DateHijriConverterPipe } from '@shared/pipes/dateHijriConverter/dateHijriConverter.pipe';
import { ResolutionStatus } from '@shared/enum/resolution-status';
import { ErrorModalService } from '@core/services/error-modal.service';
import { LanguageEnum } from '@core/enums/language-enum/language-enum';
import { MatTooltipModule } from '@angular/material/tooltip';

// Resolution display interface

@Component({
  selector: 'app-resolutions',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    PageHeaderComponent,
    BreadcrumbComponent,
    MatIconModule,
    MatButtonModule,
    TranslateModule,
    DateHijriConverterPipe,
    MatTooltipModule
  ],
  templateUrl: './resolutions.component.html',
  styleUrl: './resolutions.component.scss'
})
export class ResolutionsComponent implements OnInit {
  // Data properties
  resolutions: SingleResolutionResponse[] = [];
  filteredResolutions: SingleResolutionResponse[] = [];
  currentFundId = 0;
  currentFundName = '';
  resolutionStatus=ResolutionStatus;

  // Pagination properties
  currentPage = 1;
  pageSize = 10;
  totalCount = 0;
  totalPages = 0;
  pageSizeOptions = [10, 25, 50, 100];

  // Loading and error states
  isLoading = false;
  hasError = false;
  errorMessage = '';

  // UI state
  isHasPermissionAdd: boolean = false;
  breadcrumbSizeEnum = SizeEnum;

  // Role-based filtering properties
  userRole: string = '';
  allowedStatuses: ResolutionStatusEnum[] = [];
  allowedStatusIds: number[] = []; // Added for statusId-based filtering

  // Breadcrumb configuration
  breadcrumbItems: IBreadcrumbItem[] = [
    { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
    { label: 'BREADCRUMB.FUNDS', url: '/admin/investment-funds' },
    { label: 'RESOLUTIONS.TITLE', url: '' }
  ];

  constructor(
    private resolutionsProxy: ResolutionsServiceProxy,
    private route: ActivatedRoute,
    private router: Router,
    private dialog: MatDialog,
    public translateService: TranslateService,
    public tokenService: TokenService,
    private errorModalService: ErrorModalService

  ) {}
  resolutionStatusEnum = ResolutionStatusEnum;
  ngOnInit(): void {
    this.isHasPermissionAdd = this.tokenService.hasPermission('Fund');

    // Initialize role-based filtering
    this.initializeRoleBasedFiltering();

    // Get fund ID from route params or query params
    this.route.params.subscribe(params => {
      this.currentFundId = +params['fundId'];
      if (!this.currentFundId) {
        // Try query params if not in route params
        this.route.queryParams.subscribe(queryParams => {
          this.currentFundId = +queryParams['fundId'] || 0;
          if (this.currentFundId) {
            this.loadResolutions();
            this.updateBreadcrumb();
          } else {
            this.hasError = true;
            this.errorMessage = 'INVESTMENT_FUNDS.RESOLUTIONS.NO_FUND_ID';
          }
        });
      } else {
        this.loadResolutions();
        this.updateBreadcrumb();
      }
    });
  }

  getStatusClass(statusId: number): string {
    switch (statusId) {
      case this.resolutionStatus.Draft:
        return 'draft';
      case this.resolutionStatus.Pending:
        return 'pending';
      case this.resolutionStatus.CompletingData:
        return 'completing-data';
      case this.resolutionStatus.WaitingForConfirmation:
        return 'waiting-for-confirmation';
      case this.resolutionStatus.Confirmed:
        return 'confirmed';
        case this.resolutionStatus.Rejected:
        return 'rejected';
      case this.resolutionStatus.VotingInProgress:
        return 'voting-inProgress';
      case this.resolutionStatus.Approved:
        return 'approved';
      case this.resolutionStatus.NotApproved:
        return 'not-approved';
      case this.resolutionStatus.Cancelled:
        return 'cancelled';
      default:
        return '';
    }
  }



  private updateBreadcrumb(): void {
    // Update breadcrumb with fund name if available
    this.breadcrumbItems = [
      { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
      { label: 'BREADCRUMB.FUNDS', url: '/admin/investment-funds' },
      { label: this.currentFundName || 'BREADCRUMB.FUND_DETAILS', url: `/admin/investment-funds/fund-details?id=${this.currentFundId}` },
      { label: 'RESOLUTIONS.TITLE', url: '', disabled: true }
    ];
  }

  onBreadcrumbClicked(item: IBreadcrumbItem): void {
    if (item.url && !item.disabled) {
      this.router.navigateByUrl(item.url);
    }
  }

  /**
   * Initialize role-based filtering according to JDWA-582 requirements
   * - Fund manager: all resolutions with all status
   * - Legal council/board secretary: all resolutions except draft
   * - Board member: only voting in progress, approved/not approved
   */
  private initializeRoleBasedFiltering(): void {
    // Determine user role based on permissions and roles
    if (this.tokenService.hasRole('FundManager')) {
      this.userRole = 'FundManager';
      // Fund manager sees all statuses
      this.allowedStatuses = [
        ResolutionStatusEnum._1, // Draft
        ResolutionStatusEnum._2, // Pending
        ResolutionStatusEnum._3, // Approved
        ResolutionStatusEnum._4, // Rejected
        ResolutionStatusEnum._5, // Voting in progress
        ResolutionStatusEnum._6  // Not approved
      ];
      this.allowedStatusIds = [1, 2, 3, 4, 5, 6]; // Corresponding statusIds
    } else if (this.tokenService.hasRole('LegalCouncil') || this.tokenService.hasRole('BoardSecretary')) {
      this.userRole = 'LegalCouncil_BoardSecretary';
      // Legal council/board secretary see all except draft
      this.allowedStatuses = [
        ResolutionStatusEnum._2, // Pending
        ResolutionStatusEnum._3, // Approved
        ResolutionStatusEnum._4, // Rejected
        ResolutionStatusEnum._5, // Voting in progress
        ResolutionStatusEnum._6  // Not approved
      ];
      this.allowedStatusIds = [2, 3, 4, 5, 6]; // Corresponding statusIds
    } else if (this.tokenService.hasRole('BoardMember')) {
      this.userRole = 'BoardMember';
      // Board member sees only voting in progress, approved/not approved
      this.allowedStatuses = [
        ResolutionStatusEnum._3, // Approved
        ResolutionStatusEnum._5, // Voting in progress
        ResolutionStatusEnum._6  // Not approved
      ];
      this.allowedStatusIds = [3, 5, 6]; // Corresponding statusIds
    } else {
      // Default fallback - show minimal access
      this.userRole = 'Default';
      this.allowedStatuses = [
        ResolutionStatusEnum._3, // Approved
        ResolutionStatusEnum._6  // Not approved
      ];
      this.allowedStatusIds = [3, 6]; // Corresponding statusIds
    }
  }

  loadResolutions(search: string = '', status?: ResolutionStatusEnum): void {
    if (this.currentFundId <= 0) {
      this.hasError = true;
      this.errorMessage = 'INVESTMENT_FUNDS.RESOLUTIONS.INVALID_FUND_ID';
      return;
    }

    this.isLoading = true;
    this.hasError = false;

    const apiPageNumber = this.currentPage; // Convert to 0-based indexing for API
    console.log(`API Call: loadResolutions - UI page: ${this.currentPage}, API pageNo: ${apiPageNumber}, pageSize: ${this.pageSize}`);

    this.resolutionsProxy.resolutionsList(
      this.currentFundId,
      status, // status filter
      undefined, // resolutionTypeId
      undefined, // fromDate
      undefined, // toDate
      undefined, // createdBy
      apiPageNumber, // pageNo (API uses 0-based indexing)
      this.pageSize, // pageSize
      search, // search
      'resolutionDate desc' // orderBy
    ).subscribe({
      next: (response) => {
        this.isLoading = false;

        if (response.successed && response.data) {
          // Convert API response to display format
          let resolutions = response.data;


          this.resolutions = resolutions;
          this.filteredResolutions = [...this.resolutions];

          // Update pagination info (don't overwrite currentPage as it's managed by UI)
          this.totalCount = response.totalCount || 0;
          this.totalPages = response.totalPages || 0;

          // Update fund name from first resolution if available
          if (this.resolutions.length > 0 && this.resolutions[0].fundName) {
            // this.currentFundName = this.resolutions[0].fundName;
            this.updateBreadcrumb();
          }

          // Show empty state message if no resolutions (MSG001 from user story)
          if (this.resolutions.length === 0) {
            this.hasError = false; // Not an error, just empty state
            this.errorMessage =  response.message ||'INVESTMENT_FUNDS.RESOLUTIONS.NO_RESOLUTIONS'; // MSG001
          }
        } else {
          this.errorMessage = response.message || 'INVESTMENT_FUNDS.RESOLUTIONS.LOAD_ERROR'; // MSG002
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.hasError = true;
        this.errorMessage = 'INVESTMENT_FUNDS.RESOLUTIONS.LOAD_ERROR';
        console.error('Error loading resolutions:', error);

        // Show user-friendly error message
        this.translateService.get('INVESTMENT_FUNDS.RESOLUTIONS.LOAD_ERROR').subscribe(message => {
          Swal.fire({
            title: this.translateService.instant('COMMON.ERROR'),
            text: message,
            icon: 'error',
            confirmButtonText: this.translateService.instant('COMMON.OK')
          });
        });
      }
    });
  }


  openFilter(): void {
    const dialogRef = this.dialog.open(AdvancedSearchDialogComponent, {
      width: '600px',
      data: {
        search: '',
        status: '',
        resolutionType: null,
        fromDate: '',
        toDate: '',
        createdBy: ''
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.applyFilters(result);
      }
    });
  }

  onSearch(event: any): void {
    debugger
      const searchTerm = event?.trim() || '';
      this.currentPage = 1; // Reset to first page when searching
      this.currentSearchFilters = null; // Clear advanced filters
      this.loadResolutions(searchTerm); // Trigger data load
  }

  private applyFilters(filters: ResolutionSearchFilters): void {
    this.currentPage = 1; // Reset to first page when applying filters
    this.currentSearchFilters = filters; // Store current search filters
    // Enhanced filtering with AND logic for JDWA-583
    this.loadResolutionsWithAdvancedFilters(filters);
  }

  /**
   * Load resolutions with advanced filters using AND logic (JDWA-583)
   */
  private loadResolutionsWithAdvancedFilters(filters: ResolutionSearchFilters): void {
    console.log('Advanced filters received:', filters);

    if (this.currentFundId <= 0) {
      this.hasError = true;
      this.errorMessage = 'INVESTMENT_FUNDS.RESOLUTIONS.INVALID_FUND_ID';
      return;
    }

    this.isLoading = true;
    this.hasError = false;

    // Convert status to enum
    let statusEnum: ResolutionStatusEnum | undefined;
    if (filters.status) {
      if (typeof filters.status === 'string') {
        statusEnum = this.getStatusEnum(filters.status);
      } else if (typeof filters.status === 'number') {
        // Convert statusId to ResolutionStatusEnum
        statusEnum = this.getStatusEnumFromId(filters.status);
      } else {
        statusEnum = filters.status;
      }
    }

    // Convert dates to proper DateTime format if provided
    let fromDate: DateTime | undefined;
    let toDate: DateTime | undefined;
    if (filters.fromDate) {
      fromDate = DateTime.fromISO(filters.fromDate);
    }
    if (filters.toDate) {
      toDate = DateTime.fromISO(filters.toDate);
    }

    console.log('=== END DATE CONVERSION DEBUG ===');

    // Convert createdBy string to number if provided
    let createdByNumber: number | undefined;
    if (filters.createdBy && filters.createdBy !== '') {
      createdByNumber = parseInt(filters.createdBy, 10);
    }

    const apiPageNumber = this.currentPage; // Convert to 0-based indexing for API
    console.log(`API Call: loadResolutionsWithAdvancedFilters - UI page: ${this.currentPage}, API pageNo: ${apiPageNumber}, pageSize: ${this.pageSize}`);

    this.resolutionsProxy.resolutionsList(
      this.currentFundId,
      statusEnum, // status filter
      filters.resolutionType, // resolutionTypeId
      fromDate, // fromDate
      toDate, // toDate
      createdByNumber, // createdBy (converted to number)
      apiPageNumber, // pageNo (API uses 0-based indexing)
      this.pageSize, // pageSize
      filters.search, // search
      'resolutionDate desc' // orderBy
    ).subscribe({
      next: (response) => {
        this.isLoading = false;
        console.log(`API Response: loadResolutionsWithAdvancedFilters - currentPage from API: ${response.currentPage}, totalPages: ${response.totalPages}, totalCount: ${response.totalCount}`);

        if (response.successed && response.data) {
          // Convert API response to display format
          let resolutions = response.data;

          this.resolutions = resolutions;
          this.filteredResolutions = [...this.resolutions];

          // Update pagination info (don't overwrite currentPage as it's managed by UI)
          this.totalCount = response.totalCount || 0;
          this.totalPages = response.totalPages || 0;
          console.log(`Pagination State: UI currentPage: ${this.currentPage}, totalPages: ${this.totalPages}, totalCount: ${this.totalCount}`);

          // Update fund name from first resolution if available
          if (this.resolutions.length > 0 && this.resolutions[0].fundName) {
            this.currentFundName = this.resolutions[0].fundName;
            this.updateBreadcrumb();
          }

          // Show empty state message if no resolutions match search criteria (MSG001)
          if (this.resolutions.length === 0) {
            this.hasError = false; // Not an error, just empty state
            this.errorMessage = 'INVESTMENT_FUNDS.RESOLUTIONS.NO_SEARCH_RESULTS'; // MSG001 for search
          }
        } else {
          this.hasError = true;
          this.errorMessage = response.message || 'INVESTMENT_FUNDS.RESOLUTIONS.LOAD_ERROR'; // MSG002
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.hasError = true;
        this.errorMessage = 'INVESTMENT_FUNDS.RESOLUTIONS.LOAD_ERROR'; // MSG002
        console.error('Error loading resolutions with filters:', error);
      }
    });
  }

  private getStatusEnum(statusKey: string): ResolutionStatusEnum | undefined {
    switch (statusKey) {
      case 'draft': return ResolutionStatusEnum._1;
      case 'pending': return ResolutionStatusEnum._2;
      case 'approved': return ResolutionStatusEnum._3;
      case 'rejected': return ResolutionStatusEnum._4;
      default: return undefined;
    }
  }

  /**
   * Convert statusId to ResolutionStatusEnum
   */
  private getStatusEnumFromId(statusId: number): ResolutionStatusEnum | undefined {
    switch (statusId) {
      case 1: return ResolutionStatusEnum._1; // Draft
      case 2: return ResolutionStatusEnum._2; // Pending
      case 3: return ResolutionStatusEnum._3; // CompletingData
      case 4: return ResolutionStatusEnum._4; // WaitingForConfirmation
      case 5: return ResolutionStatusEnum._5; // Confirmed
      case 6: return ResolutionStatusEnum._6; // Rejected
      case 7: return ResolutionStatusEnum._7; // VotingInProgress
      case 8: return ResolutionStatusEnum._8; // Approved
      case 9: return ResolutionStatusEnum._9; // NotApproved
      case 10: return ResolutionStatusEnum._10; // Cancelled
      default: return undefined;
    }
  }

  getStatusKey(status?: ResolutionStatusEnum): string {
    switch (status) {
      case ResolutionStatusEnum._1: return 'draft';
      case ResolutionStatusEnum._2: return 'pending';
      case ResolutionStatusEnum._3: return 'approved';
      case ResolutionStatusEnum._4: return 'rejected';
      case ResolutionStatusEnum._5: return 'voting-in-progress';
      case ResolutionStatusEnum._6: return 'not-approved';
      case ResolutionStatusEnum._7: return 'under-review';
      case ResolutionStatusEnum._8: return 'cancelled';
      case ResolutionStatusEnum._9: return 'expired';
      case ResolutionStatusEnum._10: return 'archived';
      default: return 'draft';
    }
  }

  /**
   * Convert ResolutionStatusEnum to statusId
   */
  getStatusIdFromEnum(status?: ResolutionStatusEnum): number {
    switch (status) {
      case ResolutionStatusEnum._1: return 1; // Draft
      case ResolutionStatusEnum._2: return 2; // Pending
      case ResolutionStatusEnum._3: return 3; // CompletingData
      case ResolutionStatusEnum._4: return 4; // WaitingForConfirmation
      case ResolutionStatusEnum._5: return 5; // Confirmed
      case ResolutionStatusEnum._6: return 6; // Rejected
      case ResolutionStatusEnum._7: return 7; // VotingInProgress
      case ResolutionStatusEnum._8: return 8; // Approved
      case ResolutionStatusEnum._9: return 9; // NotApproved
      case ResolutionStatusEnum._10: return 10; // Cancelled
      default: return 1; // Default to Draft
    }
  }

  // Pagination methods
  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      console.log(`Pagination: Changing from page ${this.currentPage} to page ${page}`);
      this.currentPage = page;
      console.log(`Pagination: Will send pageNo=${this.currentPage - 1} to API (0-based indexing)`);
      this.reloadCurrentData();
    }
  }

  onPreviousPage(): void {
    if (this.canGoPrevious()) {
      this.onPageChange(this.currentPage - 1);
    }
  }

  onNextPage(): void {
    if (this.canGoNext()) {
      this.onPageChange(this.currentPage + 1);
    }
  }

  onFirstPage(): void {
    this.onPageChange(1);
  }

  onLastPage(): void {
    this.onPageChange(this.totalPages);
  }

  onPageSizeChange(newSize: number): void {
    this.pageSize = newSize;
    this.currentPage = 1; // Reset to first page when changing page size
    this.reloadCurrentData();
  }

  canGoPrevious(): boolean {
    return this.currentPage > 1;
  }

  canGoNext(): boolean {
    return this.currentPage < this.totalPages;
  }
  
  isEnglish(): boolean {
    return this.translateService.currentLang === LanguageEnum.en;
  }

  getPageNumbers(): number[] {
    const pages: number[] = [];
    const maxPagesToShow = 5;
    const halfRange = Math.floor(maxPagesToShow / 2);

    let startPage = Math.max(1, this.currentPage - halfRange);
    let endPage = Math.min(this.totalPages, this.currentPage + halfRange);

    // Adjust if we're near the beginning or end
    if (endPage - startPage + 1 < maxPagesToShow) {
      if (startPage === 1) {
        endPage = Math.min(this.totalPages, startPage + maxPagesToShow - 1);
      } else {
        startPage = Math.max(1, endPage - maxPagesToShow + 1);
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  getRecordsInfo(): { start: number; end: number; total: number } {
    const start = (this.currentPage - 1) * this.pageSize + 1;
    const end = Math.min(this.currentPage * this.pageSize, this.totalCount);
    return { start, end, total: this.totalCount };
  }

  private reloadCurrentData(): void {
    // Check if we have active search filters
    if (this.hasActiveSearchFilters()) {
      // Reload with current search filters
      this.loadResolutionsWithAdvancedFilters(this.currentSearchFilters);
    } else {
      // Reload with basic parameters
      this.loadResolutions();
    }
  }

  private currentSearchFilters: any = null;

  private hasActiveSearchFilters(): boolean {
    return this.currentSearchFilters !== null;
  }

  viewResolutionDetails(resolution: SingleResolutionResponse): void {
    // Navigate to details page
    console.log('resolution.id', resolution.id);

    this.router.navigate(
      ['/admin/investment-funds/resolutions/details', resolution.id],
      {
        queryParams: { fundId: this.currentFundId }
      }
    );
  }


  editResolution(resolution: any): void {
    // Navigate to edit page
    this.router.navigate(['/admin/investment-funds/resolutions/edit'], {
      queryParams: { id: resolution.id, fundId: this.currentFundId }
    });
  }

  deleteResolution(resolution: SingleResolutionResponse): void {
    if (resolution.status !== ResolutionStatusEnum._1) {
      Swal.fire({
        title: this.translateService.instant('COMMON.ERROR'),
        text: this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.DELETE_NOT_ALLOWED'),
        icon: 'error',
        confirmButtonText: this.translateService.instant('COMMON.OK')
      });
      return;
    }

    Swal.fire({
      title: this.translateService.instant('COMMON.CONFIRM_DELETE'),
      text: this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.DELETE_CONFIRM'),
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: this.translateService.instant('COMMON.DELETE_RESOLUTION'),
      cancelButtonText: this.translateService.instant('COMMON.BACK')
    }).then((result) => {
      if (result.isConfirmed) {
        // Call API to cancel resolution (equivalent to delete)
        this.resolutionsProxy.deleteResolution(resolution.id).subscribe({
          next: (response) => {
            if (response.successed) {
              this.errorModalService.showSuccess(this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.DELETED_SUCCESS'));
              // Reload the data
              this.loadResolutions();
            } else {
                this.errorModalService.showError(response.message || this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.DELETE_ERROR'));
            }
          },
          error: (error: any) => {
            this.errorModalService.showError(error.message || this.translateService.instant('COMMON.ERROR'));
          }
        });
      }
    });
  }

  cancelResolution(resolution: SingleResolutionResponse): void {
    if (resolution.statusId === 1) { // Draft status
      Swal.fire({
        title: this.translateService.instant('COMMON.ERROR'),
        text: this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.DELETE_NOT_ALLOWED'),
        icon: 'error',
        confirmButtonText: this.translateService.instant('COMMON.OK')
      });
      return;
    }

       Swal.fire({

      title: this.translateService.instant('RESOLUTIONS.CONFIRM_CANCEL'),
      text: this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.CANCEL_CONFIRM'),
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: this.translateService.instant('COMMON.CANCEL'),
      cancelButtonText: this.translateService.instant('COMMON.BACK')
    }).then((result) => {
      if (result.isConfirmed) {
        // Call API to cancel resolution (equivalent to delete)
        this.resolutionsProxy.cancelResolution(resolution.id).subscribe({
          next: (response) => {
            if (response.successed) {
              Swal.fire({
                title: this.translateService.instant('COMMON.CANCELED'),
                text: this.translateService.instant('RESOLUTIONS.CANCEL_SUCCESS'),
                icon: 'success',
                timer: 1500,
                showConfirmButton: false
              });

              // Reload the data
              this.loadResolutions();
            } else {
              this.errorModalService.showError(response.message || this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.CANCEL_ERROR'));
            }
          },
          error: (error: any) => {
            this.errorModalService.showError(error.message || this.translateService.instant('COMMON.ERROR'));
          }
        });
      }
    });
  }


  /**
   * Navigate to create new resolution page
   * Validates user permissions and fund ID before navigation
   */
  addNewResolution(): void {
    console.log('addNewResolution called with fundId:', this.currentFundId);
    debugger
    // Check if user has permission to create resolutions
    if (!this.tokenService.hasPermission('Resolution.Create')) {
      console.warn('User does not have permission to create resolutions');
      this.translateService.instant('COMMON.NO_PERMISSION')
      return;
    }

    // Check if we have a valid fund ID
    if (!this.currentFundId || this.currentFundId <= 0) {
      console.error('Invalid fund ID for creating resolution:', this.currentFundId);
      this.errorModalService.showError(this.translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.NO_FUND_SELECTED'));
      return;
    }

    // Navigate to create resolution page with fund ID
    const queryParams = { fundId: this.currentFundId };
    console.log('Navigating to create resolution with params:', queryParams);

    this.router.navigate(['/admin/investment-funds/resolutions/create'], { queryParams })
      .then(success => {
        if (success) {
          console.log('Navigation to create resolution successful');
        } else {
          console.error('Navigation to create resolution failed');
        }
      })
      .catch(error => {
        console.error('Error navigating to create resolution:', error);
        this.errorModalService.showError(this.translateService.instant('COMMON.NAVIGATION_ERROR'));
        });
  }

  /**
   * Format DateTime object to string format expected by dateHijriConverter pipe
   */
  formatDateToString(dateTime: DateTime): string {
    return moment(dateTime.toJSDate()).format('YYYY-MM-DD');
  }

}
