import { Routes } from '@angular/router';
import { AuthGuard } from '@core/guards/auth.guard';

export const RESOLUTION_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('./resolutions.component').then(m => m.ResolutionsComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'create',
    loadComponent: () =>
      import('./components/create-resolution/create-resolution.component').then(m => m.CreateResolutionComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'details/:id',
    loadComponent: () =>
      import('./components/resolution-details/resolution-details.component').then(m => m.ResolutionDetailsComponent),
    canActivate: [AuthGuard]
  },
];
