<div class="dialog-container">
  <h2 class=" header mb-5">
    {{ data.isEdit ? ('FUND_STRATEGIES.EDIT_FUND_STRATEGIES' | translate) :
    ('FUND_STRATEGIES.ADD_FUND_STRATEGIES' | translate) }}
  </h2>

  <div class="form-fields">
    <div class="form-container" style="direction: rtl;">
      <label class="required mb-3 ">{{'FUND_STRATEGIES.NAME_FUND_STRATEGIES' | translate}} </label>
      <input
        class="form-control form-control-solid"
        type="text"
        [(ngModel)]="arabicName"
        name="arabicName"
        #arabicNameRef="ngModel"
        placeholder="{{'FUND_STRATEGIES.NAME_FUND_STRATEGIES' | translate}}"
        maxlength="50"
        pattern="^[a-zA-Z\u0600-\u06FF\s]{1,50}$"
        required
        />
      <div *ngIf="arabicNameRef.invalid && arabicNameRef.touched"
        class="text-danger">
        <div *ngIf="arabicNameRef.errors?.['required']">{{'FORM.ERROR_REQUIRED' | translate}}</div>
        <div *ngIf="arabicNameRef.errors?.['pattern']">{{'FORM.ERROR_PATTERN' | translate}}</div>
        <div class="text-danger" *ngIf="arabicNameRef.errors?.['duplicate']">
          {{'FORM.ERROR_DUPLICATION' | translate}}
        </div>
      </div>
    </div>

    <div class="form-container" style="direction: ltr;">
      <label class="required mb-3 " style=" text-align: left;
        ">{{'FUND_STRATEGIES.STRATEGIES_Name_En' | translate}}</label>
      <input class="form-control form-control-solid" type="text"
        [(ngModel)]="strategyName" placeholder="{{'FUND_STRATEGIES.STRATEGIES_Name_En' | translate}}" maxlength="50"
        pattern="^[a-zA-Z\u0600-\u06FF\s]{1,50}$"  #strategyNameRef="ngModel"
        required>
        <div *ngIf="strategyNameRef.invalid && strategyNameRef.touched"
        class="error-message">
        <div *ngIf="strategyNameRef.errors?.['required']">{{'FORM.ERROR_REQUIRED' | translate}}</div>
        <div *ngIf="strategyNameRef.errors?.['pattern']">{{'FORM.ERROR_PATTERN' | translate}}</div>
        <div class="text-danger" *ngIf="strategyNameRef.errors?.['duplicate']">

          {{'FORM.ERROR_DUPLICATION' | translate}}
        </div>
      </div>
    </div>
  </div>

  <div class="dialog-actions">
    <button class="btn primary-btn w-50" (click)="onSubmit()"   [disabled]="!isChanged() || !arabicNameRef?.valid || !strategyNameRef?.valid"
    >
      {{ data.isEdit ? 'حفظ' : 'إضافة' }}
      <img src="assets/icons/verify-icon.png" class="mx-2" alt="verify">
    </button>
    <button class="btn cancel-btn w-50" (click)="onCancel()">إلغاء
      <img src="assets/icons/cancel-icon.png" class="mx-2" alt="verify">
    </button>
  </div>
</div>


