import { Component, <PERSON><PERSON>ni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { Subject, takeUntil, catchError, of, finalize } from 'rxjs';
import { TranslateModule } from '@ngx-translate/core';

// Shared imports
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { ErrorModalService } from '@core/services/error-modal.service';

// Core imports
import { AuthorzationServiceProxy } from '@core/api/api.generated';
import { UserManagementService } from '@shared/services/users/user-management.service';

// Enums and interfaces
import { InputType } from '@shared/enum/input-type.enum';
import { ButtonTypeEnum } from '@core/enums/icon-enum';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';

// Validators
import {
  saudiMobileValidator,
  saudiIbanValidator,
  saudiPassportValidator,
} from '@shared/validators/saudi-validators';

@Component({
  selector: 'app-edit-user',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    FormBuilderComponent,
    BreadcrumbComponent,
    PageHeaderComponent,
    CustomButtonComponent,
  ],
  templateUrl: './edit-user.component.html',
  styleUrls: ['./edit-user.component.scss'],
})
export class EditUserComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  editUserForm!: FormGroup;
  formControls: IControlOption[] = [];
  breadcrumbItems: IBreadcrumbItem[] = [];

  isLoading = false;
  isFormSubmitted = false;
  userId!: number;
  currentUserData: any = null;

  // Enums for template
  ButtonTypeEnum = ButtonTypeEnum;

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private userManagementService: UserManagementService,
    private authorizationService: AuthorzationServiceProxy,
    private errorModalService: ErrorModalService
  ) {
    this.initializeBreadcrumbs();
  }

  ngOnInit(): void {
    this.getUserIdFromRoute();
    this.initializeForm();
    this.setupFormControls();
    this.loadRoles();
    this.loadUserData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private getUserIdFromRoute(): void {
    this.userId = Number(this.route.snapshot.paramMap.get('id'));
    if (!this.userId) {
      this.errorModalService.showError('USER_MANAGEMENT.EDIT.INVALID_USER_ID');
      this.router.navigate(['/admin/user-management']);
    }
  }

  private initializeBreadcrumbs(): void {
    this.breadcrumbItems = [
      {
        label: 'COMMON.HOME',
        url: '/admin/dashboard',
      },
      {
        label: 'USER_MANAGEMENT.TITLE',
        url: '/admin/user-management',
      },
      {
        label: 'USER_MANAGEMENT.EDIT.PAGE_TITLE',
        disabled: true,
      },
    ];
  }

  private initializeForm(): void {
    this.editUserForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      countryCode: ['+966', [Validators.required]], // Fixed to +966 for Saudi numbers
      mobile: ['', [Validators.required, saudiMobileValidator()]], // 11 digits starting with 05
      iban: ['', [saudiIbanValidator()]], // Optional field with Saudi IBAN validation
      nationality: [''], // Optional field, no default value
      cv: [''],
      personalPhoto: [''],
      passportNo: ['', [Validators.required, saudiPassportValidator()]],
      role: [[], [Validators.required]], // Changed to array to support multiple roles
      password: ['', [Validators.required, Validators.minLength(8)]],
      registrationMessageSent: [true],
      registrationCompleted: [false],
    });
    this.editUserForm.get('countryCode')?.disable();
  }

  private setupFormControls(): void {
    this.formControls = [
      // Basic Information Section
      {
        formControlName: 'name',
        type: InputType.Text,
        id: 'name',
        name: 'name',
        label: 'USER_MANAGEMENT.EDIT.NAME',
        placeholder: 'USER_MANAGEMENT.EDIT.NAME_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-6',
        maxLength: 100,
      },
      {
        formControlName: 'email',
        type: InputType.Email,
        id: 'email',
        name: 'email',
        label: 'USER_MANAGEMENT.EDIT.EMAIL',
        placeholder: 'USER_MANAGEMENT.EDIT.EMAIL_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-6',
        maxLength: 100,
      },
      {
        formControlName: 'countryCode',
        type: InputType.Text,
        id: 'countryCode',
        name: 'countryCode',
        label: 'USER_MANAGEMENT.CREATE.COUNTRY_CODE',
        placeholder: 'USER_MANAGEMENT.CREATE.COUNTRY_CODE_PLACEHOLDER',
        isRequired: true,
        class: 'col-1',
        maxLength: 5,
        isReadonly: true, // Fixed value, read-only
      },
      {
        formControlName: 'mobile',
        type: InputType.Text,
        id: 'mobile',
        name: 'mobile',
        label: 'USER_MANAGEMENT.EDIT.MOBILE',
        placeholder: 'USER_MANAGEMENT.EDIT.MOBILE_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-5', // Adjusted to accommodate country code
        maxLength: 10, // 10 digits for Saudi mobile format
        pattern: '^(05|5)(5|0|3|6|4|9|1|8|7)([0-9]{7})$', // Saudi mobile pattern
      },

      // Personal Details
      {
        formControlName: 'nationality',
        type: InputType.Text,
        id: 'nationality',
        name: 'nationality',
        label: 'USER_MANAGEMENT.EDIT.NATIONALITY',
        placeholder: 'USER_MANAGEMENT.EDIT.NATIONALITY_PLACEHOLDER',
        isRequired: false, // Optional field according to User Story 1223
        class: 'col-md-6',
        maxLength: 100, // Max 100 characters as per User Story 1223
      },
      {
        formControlName: 'passportNo',
        type: InputType.Text,
        id: 'passportNo',
        name: 'passportNo',
        label: 'USER_MANAGEMENT.EDIT.PASSPORT_NO',
        placeholder: 'USER_MANAGEMENT.EDIT.PASSPORT_NO_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-6',
        maxLength: 9, // 1 letter + 8 digits
        pattern: '^[A-Z][0-9]{8}$', // Pattern hint for UI
      },

      // System Access & Role Information
      {
        formControlName: 'role',
        type: InputType.Dropdown,
        id: 'role',
        name: 'role',
        label: 'USER_MANAGEMENT.EDIT.ROLE',
        placeholder: 'USER_MANAGEMENT.EDIT.ROLE_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-6',
        options: [],
      },
      {
        formControlName: 'password',
        type: InputType.Password,
        id: 'password',
        name: 'password',
        label: 'USER_MANAGEMENT.CREATE.PASSWORD',
        placeholder: 'USER_MANAGEMENT.CREATE.PASSWORD_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-6',
        minLength: 8,
        maxLength: 50,
      },
      {
        formControlName: 'iban',
        type: InputType.Text,
        id: 'iban',
        name: 'iban',
        label: 'USER_MANAGEMENT.EDIT.IBAN',
        placeholder: 'USER_MANAGEMENT.EDIT.IBAN_PLACEHOLDER',
        isRequired: false, // Optional field according to User Story 1223
        class: 'col-md-6',
        maxLength: 24, // SA + 22 digits
        pattern: '^SA[0-9]{22}$', // Pattern hint for UI
      },
      {
        formControlName: 'cv',
        type: InputType.file,
        id: 'cv',
        name: 'cv',
        label: 'USER_MANAGEMENT.EDIT.CV',
        placeholder: 'USER_MANAGEMENT.EDIT.CV_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-6',
        allowedTypes: ['pdf', 'doc', 'docx'],
        max: 10, // 10MB max for CV
      },
      {
        formControlName: 'personalPhoto',
        type: InputType.file,
        id: 'personalPhoto',
        name: 'personalPhoto',
        label: 'USER_MANAGEMENT.EDIT.PERSONAL_PHOTO',
        placeholder: 'USER_MANAGEMENT.EDIT.PERSONAL_PHOTO_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-6',
        allowedTypes: ['jpg', 'jpeg', 'png'],
        max: 2, // 2MB max for photo
      },
    ];
  }

  private loadRoles(): void {
    this.userManagementService.getRolesList().subscribe((response: any) => {
      console.log('Roles API called successfully', response);
      if (response)
        this.formControls.find(
          (control) => control.formControlName === 'role'
        )!.options = response.data.map((role: any) => ({
          id: role.roleName,
          name: role.roleName,
        }));
    });
  }

  private loadUserData(): void {
    this.isLoading = true;

    this.userManagementService
      .getUserById(this.userId)
     .subscribe((response) => {
        if (response) {
          // TODO: Handle API response when proper return type is implemented
          // For now, we'll need to handle the response structure
          this.populateForm(response);
        }
      });
  }

  private populateForm(userData: any): void {
    this.currentUserData = userData;

    try {
      // Handle the API response structure
      const user = userData.data || userData;
      const userRoles = user.userRoles || [];

      // Populate form with user data
      this.editUserForm.patchValue({
        name: user.fullName || '',
        email: user.email || '',
        countryCode: '+966', // Fixed value for Saudi Arabia
        mobile: '', // Mobile number not provided in API response
        nationality: user.nationality || '',
        passportNo: user.passportNo || '',
        role: this.getSelectedRoles(userRoles),
        password: '', // Password field for edit (optional or required based on business logic)
        iban: user.iban || '',
        cv: user.cv || '',
        personalPhoto: user.personalPhoto || '',
        registrationMessageSent: user.registrationMessageIsSent || false,
        registrationCompleted: user.registrationIsCompleted || false,
      });

      this.updateRoleOptions(userRoles);
    } catch (error) {
      console.error('Error populating form:', error);
      this.errorModalService.showError('USER_MANAGEMENT.EDIT.POPULATE_ERROR');
    }
  }

  private getSelectedRoles(userRoles: any[]): string[] {
    return userRoles
      .filter(role => role.hasRole === true)
      .map(role => role.id.toString());
  }

  private updateRoleOptions(userRoles: any[]): void {
    const roleControl = this.formControls.find(control => control.formControlName === 'role');
    if (roleControl) {
      // Update role options with user role data
      roleControl.options = userRoles.map(role => ({
        id: role.id,
        name: role.name,
        selected: role.hasRole === true
      }));
    }
  }

  onValueChange(event: any, control: IControlOption): void {
    console.log('Value changed:', control.formControlName, event);
  }

  onKeyPressed(event: any, control: IControlOption): void {
    // Handle specific key press events if needed
  }

  onDropdownChange(event: any, control: IControlOption): void {
    if (control.formControlName === 'role') {
      // Handle role changes and validation
      this.handleRoleChange(event);
    }
  }

  onFileUploaded(data: any) {
    this.editUserForm
      .get(data.control.formControlName)
      ?.setValue(data.file.url);
  }

  private handleRoleChange(selectedRoles: string[]): void {
    // TODO: Implement role validation logic for single-holder roles
    // This should check if the selected roles conflict with existing assignments

    // For now, we'll just validate that at least one role is selected
    if (!selectedRoles || selectedRoles.length === 0) {
      this.editUserForm.get('role')?.setErrors({ required: true });
    } else {
      // Clear any existing errors if roles are selected
      const roleControl = this.editUserForm.get('role');
      if (roleControl?.hasError('required')) {
        roleControl.setErrors(null);
      }
    }
  }

  onSubmit(): void {
    if (this.editUserForm.valid) {
      this.isFormSubmitted = true;
      this.isLoading = true;

      const editUserCommand = this.buildEditUserCommand();

      this.userManagementService
        .updateUser(editUserCommand)
        .pipe(
          takeUntil(this.destroy$),
          catchError((error) => {
            console.error('Error updating user:', error);
            this.errorModalService.showError(
              'USER_MANAGEMENT.EDIT.UPDATE_ERROR'
            );
            return of(null);
          }),
          finalize(() => {
            this.isLoading = false;
            this.isFormSubmitted = false;
          })
        )
        .subscribe((response) => {
          if (response) {
            this.errorModalService.showSuccess(
              response.data ?? 'USER_MANAGEMENT.EDIT.SUCCESS'
            );
            this.router.navigate(['/admin/user-management']);
          }
        });
    } else {
      this.isFormSubmitted = true;
      this.errorModalService.showError('USER_MANAGEMENT.EDIT.VALIDATION_ERROR');
    }
  }

  private buildEditUserCommand() {
    const formValue = this.editUserForm.value;

    const userName =
      this.currentUserData?.data?.userName ||
      this.currentUserData?.userName ||
      formValue.mobile ||
      this.editUserForm.get('mobile')?.value;

    const command = {
      id: this.userId,
      fullName: formValue.name,
      email: formValue.email,
      userName: userName,
      password: formValue.password,
      iban: formValue.iban,
      nationality: formValue.nationality,
      passportNo: formValue.passportNo,
      roles: formValue.role,
      cvFile: formValue.cv,
      personalPhoto: formValue.personalPhoto,
      registrationMessageSent: formValue.registrationMessageSent,
      registrationCompleted: formValue.registrationCompleted,
    };

    return command;
  }

  onCancel(): void {
    this.router.navigate(['/admin/user-management']);
  }
}
