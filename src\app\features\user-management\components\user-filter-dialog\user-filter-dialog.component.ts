import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

// Import shared components and services
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { InputType } from '@shared/enum/input-type.enum';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';

// User interfaces
import { IUserFilters, IUserStatus } from '../../interfaces/user.interface';

@Component({
  selector: 'app-user-filter-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    TranslateModule,
    FormBuilderComponent,
    CustomButtonComponent
  ],
  templateUrl: './user-filter-dialog.component.html',
  styleUrls: ['./user-filter-dialog.component.scss']
})
export class UserFilterDialogComponent implements OnInit {

  formGroup!: FormGroup;
  isFormSubmitted = false;
  buttonEnum = ButtonTypeEnum;
  IconEnum = IconEnum;

  formControls: IControlOption[] = [
    {
      type: InputType.Text,
      formControlName: 'name',
      id: 'name',
      name: 'name',
      label: 'USER_MANAGEMENT.FILTERS.NAME',
      placeholder: 'USER_MANAGEMENT.FILTERS.NAME_PLACEHOLDER',
      isRequired: false,
      class: 'col-md-12'
    },
    {
      type: InputType.Dropdown,
      formControlName: 'status',
      id: 'status',
      name: 'status',
      label: 'USER_MANAGEMENT.FILTERS.STATUS',
      placeholder: 'USER_MANAGEMENT.FILTERS.ALL_STATUSES',
      isRequired: false,
      class: 'col-md-12',
      options: [
        { id: IUserStatus.Active, name: 'USER_MANAGEMENT.STATUS.ACTIVE' },
        { id: IUserStatus.Inactive, name: 'USER_MANAGEMENT.STATUS.INACTIVE' },
      ]
    },
    {
      type: InputType.Dropdown,
      formControlName: 'role',
      id: 'role',
      name: 'role',
      label: 'USER_MANAGEMENT.FILTERS.ROLE',
      placeholder: 'USER_MANAGEMENT.FILTERS.ALL_ROLES',
      isRequired: false,
      class: 'col-md-12',
      options: [
        { id: '', name: 'USER_MANAGEMENT.FILTERS.ALL_ROLES' },
        { id: 'System Admin', name: 'USER_MANAGEMENT.ROLES.SYSTEM_ADMIN' },
        { id: 'Fund Manager', name: 'USER_MANAGEMENT.ROLES.FUND_MANAGER' },
        { id: 'Board Member', name: 'USER_MANAGEMENT.ROLES.BOARD_MEMBER' },
        { id: 'Legal Counsel', name: 'USER_MANAGEMENT.ROLES.LEGAL_COUNSEL' },
        { id: 'Board Secretary', name: 'USER_MANAGEMENT.ROLES.BOARD_SECRETARY' },
        { id: 'Investment Committee Member', name: 'USER_MANAGEMENT.ROLES.INVESTMENT_COMMITTEE_MEMBER' }
      ]
    }
  ];

  constructor(
    public dialogRef: MatDialogRef<UserFilterDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: IUserFilters,
    private formBuilder: FormBuilder,
    private translateService: TranslateService
  ) {
    this.initForm();
  }

  ngOnInit(): void {
    // Pre-populate form with existing filter data
    if (this.data) {
      this.formGroup.patchValue(this.data);
    }
  }

  private initForm(): void {
    this.formGroup = this.formBuilder.group({
      name: [''],
      status: [''],
      role: ['']
    });
  }

  dropdownChanged(event: any): void {
    // Handle dropdown changes if needed
    console.log('Dropdown changed:', event);
  }

  applyFilters(): void {
    this.isFormSubmitted = true;
    if (this.formGroup.valid) {
      const filters = this.formGroup.value;
      console.log('Raw form values:', filters);

      // Remove empty values
      const cleanFilters = Object.keys(filters).reduce((acc: any, key) => {
        const value = filters[key];
        console.log(`Checking filter key: ${key}, value: ${value}, type: ${typeof value}`);

        if (value !== '' && value !== null && value !== undefined) {
          acc[key] = value;
          console.log(`Added ${key} to clean filters:`, value);
        } else {
          console.log(`Filtered out ${key} because it was empty/null/undefined`);
        }
        return acc;
      }, {});

      console.log('Clean filters being sent:', cleanFilters);
      this.dialogRef.close(cleanFilters);
    }
  }

  resetFilters(): void {
    this.formGroup.reset();
    this.isFormSubmitted = false;
  }

  closeDialog(): void {
    this.dialogRef.close();
  }
}
