# User Profile Component

## Overview
The User Profile component allows logged-in users to view and update their personal profile information. This component implements the exact structure and layout shown in the provided prototype image.

## Features
- **Profile Photo Management**: Display and upload personal photo (JPG/PNG, max 2MB)
- **Personal Information**: Edit name, email, nationality, passport number
- **Contact Information**: View mobile number (read-only), edit IBAN
- **Document Management**: Upload/replace CV (PDF/DOCX, max 10MB)
- **Account Information**: View status and role (read-only)
- **Password Management**: Link to change password functionality
- **Internationalization**: Full support for Arabic and English languages
- **RTL/LTR Support**: Proper layout direction handling

## Component Structure

### Files
- `user-profile.component.ts` - Main component logic
- `user-profile.component.html` - Template matching prototype structure
- `user-profile.component.scss` - Styles with RTL support
- `README.md` - This documentation file

### Route
- **Path**: `/admin/profile`
- **Guard**: `AuthGuard` (requires authentication)

## Form Fields

### Editable Fields
- **Name**: Full name (required, max 255 characters)
- **Email**: Email address (required, unique, valid email format)
- **Country Code**: Fixed to +966 for Saudi numbers
- **IBAN**: International Bank Account Number (optional, Saudi format)
- **Nationality**: User nationality (optional, max 100 characters)
- **Passport Number**: Passport number (optional, alphanumeric, max 20 characters)
- **CV**: Resume file upload (optional, PDF/DOCX, max 10MB)
- **Personal Photo**: Profile photo upload (optional, JPG/PNG, max 2MB)

### Read-Only Fields
- **Mobile Number**: User's mobile number (used as username)
- **Status**: Account status (Active/Inactive)
- **Role**: User's assigned role(s)

## Validation Rules
- **Name**: Required, minimum 2 characters
- **Email**: Required, valid email format, must be unique
- **Country Code**: Required, must be '+966'
- **Mobile**: Read-only, Saudi mobile format validation
- **IBAN**: Optional, Saudi IBAN format validation
- **Passport Number**: Optional, Saudi passport format validation
- **CV**: Optional, PDF/DOCX only, max 10MB
- **Personal Photo**: Optional, JPG/PNG only, max 2MB

## API Integration (Planned)
- **Get Profile**: `GET /api/Users/<USER>/GetUserProfile`
- **Update Profile**: `PUT /api/Users/<USER>/UpdateUserProfile`

## Translation Keys
All text elements use translation keys from the `USER_PROFILE` namespace:
- `USER_PROFILE.PAGE_TITLE`
- `USER_PROFILE.PERSONAL_PHOTO`
- `USER_PROFILE.NAME`
- `USER_PROFILE.EMAIL`
- And more...

## Usage
The component is automatically loaded when navigating to `/admin/profile`. Users must be authenticated to access this route.

## Styling
- Follows existing design system patterns
- Uses shared form container styles
- Responsive design for mobile devices
- RTL support for Arabic language
- Dark theme support (future-ready)

## Dependencies
- Angular Reactive Forms
- Shared form builder component
- Translation service
- File upload component
- Custom button component
- Breadcrumb component
- Page header component

## Future Enhancements
- Real-time photo preview
- Drag and drop file upload
- Profile completion percentage
- Activity log integration
- Social media links
