<!-- Main Content -->
<div class="user-profile-page">
  <!-- Breadcrumb -->
  <app-breadcrumb [breadcrumbs]="breadcrumbItems"></app-breadcrumb>

  <div class="mt-3">
    <!-- Page Header -->
    <app-page-header
      [title]="'USER_PROFILE.PAGE_TITLE' | translate">
    </app-page-header>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading && !currentUserData" class="loading-container text-center">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">{{ 'COMMON.LOADING' | translate }}</span>
    </div>
    <p class="mt-2">{{ 'USER_PROFILE.LOADING_PROFILE' | translate }}</p>
  </div>

  <!-- Form Container -->
  <div *ngIf="!isLoading || currentUserData" class="form-container mt-3">
    <form [formGroup]="userProfileForm" (ngSubmit)="onSubmit()" novalidate>
      
      <!-- Profile Photo Section -->
      <div class="profile-photo-section text-center mb-4">
        <div class="photo-container">
          <div class="profile-photo-circle">
            <img
              src="assets/images/avatar-member.png"
              alt="{{ 'USER_PROFILE.PERSONAL_PHOTO' | translate }}"
              class="profile-photo">
          </div>
          <div class="photo-upload-overlay">
            <i class="fas fa-camera"></i>
          </div>
        </div>
        <p class="photo-label mt-2">{{ 'USER_PROFILE.PERSONAL_PHOTO' | translate }}</p>
      </div>

      <!-- Form Fields -->
      <div class="form-fields-section">
        <app-form-builder
          [formGroup]="userProfileForm"
          [formControls]="formControls"
          [isFormSubmitted]="isFormSubmitted"
          (valueChanged)="onValueChange($event.event, $event.control)"
          (keyPressed)="onKeyPressed($event.event, $event.control)"
          (dropdownChanged)="onDropdownChange($event.event, $event.control)"
          (fileUploaded)="onFileUploaded($event)">
        </app-form-builder>
      </div>

      <!-- Change Password Section -->
      <div class="change-password-section mt-4 mb-4">
        <button 
          type="button" 
          class="btn btn-link change-password-link"
          (click)="onChangePassword()">
          <i class="fas fa-key me-2"></i>
          {{ 'USER_PROFILE.CHANGE_PASSWORD' | translate }}
        </button>
      </div>

      <!-- Action Buttons -->
      <div class="actions justify-content-end">
        <app-custom-button
          [buttonType]="ButtonTypeEnum.Secondary"
          [btnName]="'COMMON.CANCEL' | translate"
          (click)="onCancel()">
        </app-custom-button>

        <app-custom-button
          [buttonType]="ButtonTypeEnum.Primary"
          [btnName]="'COMMON.SAVE_CHANGES' | translate"
        
          type="submit">
        </app-custom-button>
      </div>
    </form>
  </div>

  <!-- Error State -->
  <div *ngIf="!isLoading && !currentUserData" class="error-container text-center">
    <div class="alert alert-danger" role="alert">
      <i class="fas fa-exclamation-triangle me-2"></i>
      {{ 'USER_PROFILE.LOAD_ERROR' | translate }}
    </div>
    <app-custom-button
      [buttonType]="ButtonTypeEnum.Primary"
      [btnName]="'COMMON.RETRY' | translate"
      (click)="loadCurrentUserData()">
    </app-custom-button>
  </div>
</div>
