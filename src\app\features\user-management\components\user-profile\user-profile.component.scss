@import '../../../../shared/styles/form-container';
@import '../../../../../assets/scss/variables';

.user-profile-page {
  padding: 0;
  
  .form-container {
    background: $white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    form {
      .profile-photo-section {
        border-bottom: 1px solid $border-color;
        padding-bottom: 24px;
        margin-bottom: 24px;
        
        .photo-container {
          position: relative;
          display: inline-block;
          
          .profile-photo-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 3px solid $navy-blue;
            overflow: hidden;
            position: relative;
            background: $border-color;
            display: flex;
            align-items: center;
            justify-content: center;
            
            .profile-photo {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
          
          .photo-upload-overlay {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 36px;
            height: 36px;
            background: $navy-blue;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border: 3px solid $white;
            
            i {
              color: $white;
              font-size: 14px;
            }
            
            &:hover {
              background: darken($navy-blue, 10%);
            }
          }
        }
        
        .photo-label {
          color: $dark-gray;
          font-size: 14px;
          margin: 0;
        }
      }
      
      .form-fields-section {
        .photo-upload-section {
          display: none; // Hide the form-builder file upload for photo since we have custom UI
        }
      }
      
      .change-password-section {
        border-top: 1px solid $border-color;
        padding-top: 24px;
        
        .change-password-link {
          color: $navy-blue;
          text-decoration: none;
          font-weight: 500;
          padding: 0;
          border: none;
          background: none;
          
          &:hover {
            color: darken($navy-blue, 10%);
            text-decoration: underline;
          }
          
          i {
            color: $navy-blue;
          }
        }
      }
      
      .actions {
        display: flex;
        gap: 12px;
        margin-top: 32px;
        padding-top: 24px;
        border-top: 1px solid $border-color;
        
        app-custom-button {
          min-width: 120px;
        }
      }
    }
  }

  .loading-container {
    padding: 40px 20px;
    
    .spinner-border {
      width: 3rem;
      height: 3rem;
    }
    
    p {
      color: $dark-gray;
      margin-top: 16px;
      font-size: 14px;
    }
  }

  .error-container {
    padding: 40px 20px;
    
    .alert {
      max-width: 500px;
      margin: 0 auto 24px;
    }
  }
}

// RTL Support
[dir="rtl"] {
  .user-profile-page {
    .form-container {
      form {
        .profile-photo-section {
          .photo-container {
            .photo-upload-overlay {
              left: 0;
              right: auto;
            }
          }
        }
        
        .change-password-section {
          .change-password-link {
            i {
              margin-left: 8px;
              margin-right: 0;
            }
          }
        }
        
        .actions {
          &.justify-content-end {
            justify-content: flex-start;
          }
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .user-profile-page {
    .form-container {
      padding: 16px;
      
      form {
        .profile-photo-section {
          .photo-container {
            .profile-photo-circle {
              width: 100px;
              height: 100px;
            }
            
            .photo-upload-overlay {
              width: 32px;
              height: 32px;
              
              i {
                font-size: 12px;
              }
            }
          }
        }
        
        .actions {
          flex-direction: column;
          
          app-custom-button {
            width: 100%;
          }
        }
      }
    }
  }
}

// Dark theme support (if needed in the future)
@media (prefers-color-scheme: dark) {
  .user-profile-page {
    .form-container {
      background: #1a202c;
      border-color: #2d3748;
      color: #e2e8f0;
      
      form {
        .profile-photo-section {
          border-color: #2d3748;
          
          .photo-container {
            .profile-photo-circle {
              background: #2d3748;
              border-color: $navy-blue;
            }
          }
        }
        
        .change-password-section {
          border-color: #2d3748;
        }
        
        .actions {
          border-color: #2d3748;
        }
      }
    }
  }
}
