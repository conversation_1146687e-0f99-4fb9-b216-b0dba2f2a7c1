import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { catchError, finalize, of, Subject, takeUntil } from 'rxjs';

// Shared imports
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { ErrorModalService } from '@core/services/error-modal.service';

// Core imports
import {
  UserManagementServiceProxy,
  Body2,
  UserProfileResponseDtoBaseResponse
} from '@core/api/api.generated';

// Enums and interfaces
import { InputType } from '@shared/enum/input-type.enum';
import { ButtonTypeEnum } from '@core/enums/icon-enum';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';

// Validators
import { saudiIbanValidator, saudiPassportValidator } from '@shared/validators/saudi-validators';
import { FileUploadService } from '@shared/services/file.service';

@Component({
  selector: 'app-user-profile',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    FormBuilderComponent,
    BreadcrumbComponent,
    PageHeaderComponent,
    CustomButtonComponent
  ],
  templateUrl: './user-profile.component.html',
  styleUrls: ['./user-profile.component.scss']
})
export class UserProfileComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  userProfileForm!: FormGroup;
  formControls: IControlOption[] = [];
  breadcrumbItems: IBreadcrumbItem[] = [];
  
  isLoading = false;
  isFormSubmitted = false;
  currentUserData: any = null;
  
  // Enums for template
  ButtonTypeEnum = ButtonTypeEnum;

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private userManagementService: UserManagementServiceProxy,
    private errorModalService: ErrorModalService,
    private fileUploadService: FileUploadService
  ) {
    this.initializeBreadcrumbs();
  }

  ngOnInit(): void {
    this.initializeForm();
    this.setupFormControls();
    this.loadCurrentUserData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeBreadcrumbs(): void {
    this.breadcrumbItems = [
      {
        label: 'COMMON.HOME',
        url: '/admin/dashboard'
      },
      {
        label: 'USER_PROFILE.PAGE_TITLE',
        disabled: true
      }
    ];
  }

  private initializeForm(): void {
    this.userProfileForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      countryCode: ['+966', [Validators.required]], // Fixed to +966 for Saudi numbers
      mobile: [''], // Read-only field
      iban: ['', [saudiIbanValidator()]], // Optional field with Saudi IBAN validation
      nationality: [''], // Optional field
      cv: [''],
      personalPhoto: [''],
      passportNo: ['', [saudiPassportValidator()]],
      status: [''], // Read-only field
      role: [''] // Read-only field
    });
  }

  private setupFormControls(): void {
    this.formControls = [
      // Personal Photo Section
      {
        formControlName: 'personalPhoto',
        type: InputType.file,
        id: 'personalPhoto',
        name: 'personalPhoto',
        label: 'USER_PROFILE.PERSONAL_PHOTO',
        placeholder: '',
        isRequired: false,
        class: 'col-12 photo-upload-section',
        allowedTypes: ['jpg', 'jpeg', 'png'],
        max: 2
      },

      // Basic Information Section
      {
        formControlName: 'name',
        type: InputType.Text,
        id: 'name',
        name: 'name',
        label: 'USER_PROFILE.NAME',
        placeholder: 'USER_PROFILE.NAME_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-6',
        maxLength: 255
      },
      {
        formControlName: 'email',
        type: InputType.Email,
        id: 'email',
        name: 'email',
        label: 'USER_PROFILE.EMAIL',
        placeholder: 'USER_PROFILE.EMAIL_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-6',
        maxLength: 255
      },

      // Contact Information
      {
        formControlName: 'countryCode',
        type: InputType.Text,
        id: 'countryCode',
        name: 'countryCode',
        label: 'USER_PROFILE.COUNTRY_CODE',
        placeholder: '',
        isRequired: true,
        class: 'col-md-6',
        maxLength: 5
      },
      {
        formControlName: 'mobile',
        type: InputType.Text,
        id: 'mobile',
        name: 'mobile',
        label: 'USER_PROFILE.MOBILE',
        placeholder: '',
        isRequired: false,
        class: 'col-md-6',
        isReadonly: true
      },

      // Additional Information
      {
        formControlName: 'iban',
        type: InputType.Text,
        id: 'iban',
        name: 'iban',
        label: 'USER_PROFILE.IBAN',
        placeholder: 'USER_PROFILE.IBAN_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-6',
        maxLength: 34
      },
      {
        formControlName: 'nationality',
        type: InputType.Text,
        id: 'nationality',
        name: 'nationality',
        label: 'USER_PROFILE.NATIONALITY',
        placeholder: 'USER_PROFILE.NATIONALITY_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-6',
        maxLength: 100
      },

      // Document Upload
      {
        formControlName: 'cv',
        type: InputType.file,
        id: 'cv',
        name: 'cv',
        label: 'USER_PROFILE.CV',
        placeholder: '',
        isRequired: false,
        class: 'col-md-6',
        allowedTypes: ['pdf', 'docx'],
        max: 10
      },
      {
        formControlName: 'passportNo',
        type: InputType.Text,
        id: 'passportNo',
        name: 'passportNo',
        label: 'USER_PROFILE.PASSPORT_NO',
        placeholder: 'USER_PROFILE.PASSPORT_NO_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-6',
        maxLength: 20
      },

      // Read-only Status Information
      {
        formControlName: 'status',
        type: InputType.Text,
        id: 'status',
        name: 'status',
        label: 'USER_PROFILE.STATUS',
        placeholder: '',
        isRequired: false,
        class: 'col-md-6',
        isReadonly: true
      },
      {
        formControlName: 'role',
        type: InputType.Text,
        id: 'role',
        name: 'role',
        label: 'USER_PROFILE.ROLE',
        placeholder: '',
        isRequired: false,
        class: 'col-md-6',
        isReadonly: true
      }
    ];
  }

  loadCurrentUserData(): void {
    this.isLoading = true;

    // Call the actual API to get current user profile
    // Note: getUserProfile expects userId parameter, but for current user profile
    // we might need to pass undefined or get current user ID from auth service
    this.userManagementService.getUserProfile(undefined)
      .pipe(
        takeUntil(this.destroy$),
        catchError(error => {
          console.error('Error loading user profile:', error);
          this.errorModalService.showError('USER_PROFILE.LOAD_ERROR');
          return of(null);
        }),
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe(response => {
        if (response && response.successed && response.data) {
          this.populateForm(response);
        } else if (response && !response.successed) {
          console.error('API returned error:', response.message);
          this.errorModalService.showError(response.message || 'USER_PROFILE.LOAD_ERROR');
        }
      });
  }

  private populateForm(response: UserProfileResponseDtoBaseResponse): void {
    this.currentUserData = response;

    try {
      const user = response.data;

      this.userProfileForm.patchValue({
        name: user.fullName || '',
        email: user.email || '',
        mobile: user.userName || '',
        iban: user.iban || '',
        nationality: user.nationality || '',
        passportNo: user.passportNo || '',
        status: user.isActive ? 'Active' : 'Inactive',
        role: Array.isArray(user.roles) ? user.roles.join(', ') : ''
      });

      // Disable read-only fields
      this.userProfileForm.get('mobile')?.disable();
      this.userProfileForm.get('status')?.disable();
      this.userProfileForm.get('role')?.disable();

      console.log('Form populated successfully with user data:', user);
    } catch (error) {
      console.error('Error populating form:', error);
      this.errorModalService.showError('USER_PROFILE.POPULATE_ERROR');
    }
  }

  onValueChange(event: any, control: IControlOption): void {
    // Handle value changes if needed
  }

  onKeyPressed(event: any, control: IControlOption): void {
    // Handle key press events if needed
  }

  onDropdownChange(event: any, control: IControlOption): void {
    // Handle dropdown changes if needed
  }

  onFileUploaded(event: any): void {
    const { file, control } = event;
    if (file && control) {
      const controlName = control.formControlName;

      // Validate file size and type
      if (control.allowedTypes && !control.allowedTypes.some((type: string) =>
        file.name.toLowerCase().endsWith(`.${type.toLowerCase()}`))) {
        this.errorModalService.showError(`Invalid file type for ${controlName}. Allowed types: ${control.allowedTypes.join(', ')}`);
        return;
      }

      if (control.max && file.size > control.max * 1024 * 1024) {
        this.errorModalService.showError(`File size exceeds maximum allowed size (${control.max}MB) for ${controlName}`);
        return;
      }

      // Set the file in the form control
      this.userProfileForm.get(controlName)?.setValue(file);

      console.log(`File uploaded for ${controlName}:`, file);
    }
  }

  onSubmit(): void {
    if (this.userProfileForm.valid) {
      this.isFormSubmitted = true;
      this.isLoading = true;
    } else {
      this.isFormSubmitted = true;
      this.errorModalService.showError('USER_PROFILE.VALIDATION_ERROR');
    }
  }

  onCancel(): void {
    this.router.navigate(['/admin/dashboard']);
  }

  onChangePassword(): void {
    this.router.navigate(['/admin/change-password']);
  }
}
