<!-- Main Content -->
<div class="create-user-page">
  <!-- Breadcrumb -->
  <app-breadcrumb [breadcrumbs]="breadcrumbItems"></app-breadcrumb>

  <div class="mt-3">
    <!-- Page Header -->
    <app-page-header
      [title]="'USER_MANAGEMENT.CREATE.PAGE_TITLE' | translate">
    </app-page-header>
  </div>

  <div class="form-container mt-3">
    <form [formGroup]="createUserForm" (ngSubmit)="onSubmit()" novalidate>
      <app-form-builder
        [formGroup]="createUserForm"
        [formControls]="formControls"
        [isFormSubmitted]="isFormSubmitted"
        (valueChanged)="onValueChange($event.event, $event.control)"
        (keyPressed)="onKeyPressed($event.event, $event.control)"
        (dropdownChanged)="onDropdownChange($event.event, $event.control)"
        (fileUploaded)="onFileUploaded($event)">
      </app-form-builder>

      <div class="actions justify-content-end">
        <app-custom-button
          [btnName]="'COMMON.CANCEL' | translate"
          [buttonType]="ButtonTypeEnum.Secondary"
          [disabled]="isLoading"
          (click)="onCancel()">
        </app-custom-button>

        <app-custom-button
          [btnName]="'USER_MANAGEMENT.CREATE.SUBMIT' | translate"
          [buttonType]="ButtonTypeEnum.Primary"
          [disabled]="isLoading"
          (click)="onSubmit()">
        </app-custom-button>
      </div>
    </form>
  </div>
</div>
