import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
// import { catchError, finalize } from 'rxjs/operators';
// import { of } from 'rxjs';

// Shared components
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';

// Core interfaces and enums
import { IControlOption } from '@shared/interfaces/i-control-option';
import { InputType } from '@shared/enum/input-type.enum';
import { AppearanceEnum } from '@shared/enum/appearance-enum';
import { SizeEnum } from '@core/enums/size';
import { ButtonTypeEnum } from '@core/enums/icon-enum';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';

// User interfaces
import { IUser } from '../interfaces/user.interface';

// API
import {
  UserManagementServiceProxy,
  AddUserCommand,
  AuthorzationServiceProxy,
} from '@core/api/api.generated';

// Validators
import {
  saudiIbanValidator,
  saudiMobileValidator,
  saudiPassportValidator,
} from '@shared/validators/saudi-validators';
import { ErrorModalService } from '@core/services/error-modal.service';
import { catchError, of } from 'rxjs';
import { UserManagementService } from '@shared/services/users/user-management.service';

// Validators - using built-in pattern validator for Saudi phone numbers

@Component({
  selector: 'app-create-user',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    FormBuilderComponent,
    PageHeaderComponent,
    BreadcrumbComponent,
    CustomButtonComponent,
  ],
  providers: [UserManagementService],
  templateUrl: './create-user.component.html',
  styleUrls: ['./create-user.component.scss'],
})
export class CreateUserComponent implements OnInit {
  createUserForm!: FormGroup;
  formControls: IControlOption[] = [];
  isFormSubmitted = false;
  isLoading = false;

  // Enums for template
  readonly ButtonTypeEnum = ButtonTypeEnum;

  breadcrumbItems: IBreadcrumbItem[] = [
    {
      label: 'BREADCRUMB.HOME',
      url: '/admin/dashboard',
    },
    {
      label: 'BREADCRUMB.USER_MANAGEMENT',
      url: '/admin/user-management',
    },
    {
      label: 'USER_MANAGEMENT.BREADCRUMB.CREATE_USER',
      url: '',
      active: true,
    },
  ];

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private userManagementService: UserManagementService,
    private errorModalService: ErrorModalService
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.setupFormControls();
    this.loadRoles();
  }

  private initializeForm(): void {
    this.createUserForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      countryCode: ['+966', [Validators.required]], // Fixed to +966 for Saudi numbers
      mobile: ['', [Validators.required, saudiMobileValidator()]], // 11 digits starting with 05
      iban: ['', [saudiIbanValidator()]], // Optional field with Saudi IBAN validation
      nationality: [''], // Optional field, no default value
      cv: [''],
      personalPhoto: [''],
      passportNo: ['', [Validators.required, saudiPassportValidator()]],
      role: [[], [Validators.required]], // Changed to array to support multiple roles
      password: ['', [Validators.required, Validators.minLength(8)]],
      registrationMessageSent: [true],
      registrationCompleted: [false],
    });
    this.createUserForm.get('countryCode')?.disable();
  }

  private loadRoles(): void {
    this.userManagementService.getRolesList().subscribe((response: any) => {
      console.log('Roles API called successfully', response);
      if (response)
        this.formControls.find(
          (control) => control.formControlName === 'role'
        )!.options = response.data.map((role: any) => ({
          id: role.roleName,
          name: role.roleName,
        }));
    });
  }

  private setupFormControls(): void {
    this.formControls = [
      // Basic Information Section
      {
        formControlName: 'name',
        type: InputType.Text,
        id: 'name',
        name: 'name',
        label: 'USER_MANAGEMENT.CREATE.NAME',
        placeholder: 'USER_MANAGEMENT.CREATE.NAME_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-6',
        maxLength: 100,
      },
      {
        formControlName: 'email',
        type: InputType.Email,
        id: 'email',
        name: 'email',
        label: 'USER_MANAGEMENT.CREATE.EMAIL',
        placeholder: 'USER_MANAGEMENT.CREATE.EMAIL_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-6',
        maxLength: 100,
      },
      {
        formControlName: 'countryCode',
        type: InputType.Text,
        id: 'countryCode',
        name: 'countryCode',
        label: 'USER_MANAGEMENT.CREATE.COUNTRY_CODE',
        placeholder: 'USER_MANAGEMENT.CREATE.COUNTRY_CODE_PLACEHOLDER',
        isRequired: true,
        class: 'col-1',
        maxLength: 5,
        isReadonly: true, // Fixed value, read-only
      },
      {
        formControlName: 'mobile',
        type: InputType.Text,
        id: 'mobile',
        name: 'mobile',
        label: 'USER_MANAGEMENT.CREATE.MOBILE',
        placeholder: 'USER_MANAGEMENT.CREATE.MOBILE_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-5', // Adjusted to accommodate country code
        maxLength: 10, // 10 digits for Saudi mobile format
        pattern: '^(05|5)(5|0|3|6|4|9|1|8|7)([0-9]{7})$', // Saudi mobile pattern
      },

      // Personal Details
      {
        formControlName: 'nationality',
        type: InputType.Text,
        id: 'nationality',
        name: 'nationality',
        label: 'USER_MANAGEMENT.CREATE.NATIONALITY',
        placeholder: 'USER_MANAGEMENT.CREATE.NATIONALITY_PLACEHOLDER',
        isRequired: false, // Optional field according to User Story 1223
        class: 'col-md-6',
        maxLength: 100, // Max 100 characters as per User Story 1223
      },
      {
        formControlName: 'passportNo',
        type: InputType.Text,
        id: 'passportNo',
        name: 'passportNo',
        label: 'USER_MANAGEMENT.CREATE.PASSPORT_NO',
        placeholder: 'USER_MANAGEMENT.CREATE.PASSPORT_NO_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-6',
        maxLength: 9, // 1 letter + 8 digits
        pattern: '^[A-Z][0-9]{8}$', // Pattern hint for UI
      },

      // System Access & Role Information
      {
        formControlName: 'role',
        type: InputType.Dropdown,
        id: 'role',
        name: 'role',
        label: 'USER_MANAGEMENT.CREATE.ROLE',
        placeholder: 'USER_MANAGEMENT.CREATE.ROLE_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-6',

        options: [],
      },
      {
        formControlName: 'password',
        type: InputType.Password,
        id: 'password',
        name: 'password',
        label: 'USER_MANAGEMENT.CREATE.PASSWORD',
        placeholder: 'USER_MANAGEMENT.CREATE.PASSWORD_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-6',
        minLength: 8,
        maxLength: 50,
      },
      {
        formControlName: 'iban',
        type: InputType.Text,
        id: 'iban',
        name: 'iban',
        label: 'USER_MANAGEMENT.CREATE.IBAN',
        placeholder: 'USER_MANAGEMENT.CREATE.IBAN_PLACEHOLDER',
        isRequired: false, // Optional field according to User Story 1223
        class: 'col-md-6',
        maxLength: 24, // SA + 22 digits
        pattern: '^SA[0-9]{22}$', // Pattern hint for UI
      },
      {
        formControlName: 'cv',
        type: InputType.file,
        id: 'cv',
        name: 'cv',
        label: 'USER_MANAGEMENT.CREATE.CV',
        placeholder: 'USER_MANAGEMENT.CREATE.CV_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-6',
        allowedTypes: ['pdf', 'doc', 'docx'],
        max: 10, // 10MB max for CV
      },
      {
        formControlName: 'personalPhoto',
        type: InputType.file,
        id: 'personalPhoto',
        name: 'personalPhoto',
        label: 'USER_MANAGEMENT.CREATE.PERSONAL_PHOTO',
        placeholder: 'USER_MANAGEMENT.CREATE.PERSONAL_PHOTO_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-6',
        allowedTypes: ['jpg', 'jpeg', 'png'],
        max: 2, // 2MB max for photo
      },
    ];
  }

  onValueChange(event: any, control: IControlOption): void {
    console.log('Value changed:', control.formControlName, event);
  }

  onKeyPressed(event: any, control: IControlOption): void {
    // Handle specific key press events if needed
  }

  onDropdownChange(event: any, control: IControlOption): void {
    console.log('Dropdown changed:', control.formControlName, event);

    // Handle dependent dropdowns if needed
    if (control.formControlName === 'role') {
      this.handleRoleChange(event);
    }
  }
  onFileUploaded(data: any) {
    this.createUserForm
      .get(data.control.formControlName)
      ?.setValue(data.file.url);
  }

  private handleRoleChange(selectedRoles: string | string[]): void {
    // Convert to array if single value
    const rolesArray = Array.isArray(selectedRoles)
      ? selectedRoles
      : [selectedRoles];

    // Sprint 3 requirement: Multi-select enabled ONLY IF the selected roles are:
    // ('Fund Manager' AND 'Board Member') OR ('Associate Fund Manager' AND 'Board Member')

    const fundManagerAndBoardMember =
      rolesArray.includes('Fund Manager') &&
      rolesArray.includes('Board Member');
    const associateFundManagerAndBoardMember =
      rolesArray.includes('Associate Fund Manager') &&
      rolesArray.includes('Board Member');

    const isValidMultiSelectCombination =
      fundManagerAndBoardMember || associateFundManagerAndBoardMember;

    // If the current selection is not a valid multi-select combination and has more than one role,
    // keep only the last selected role
    if (!isValidMultiSelectCombination && rolesArray.length > 1) {
      const lastSelectedRole = rolesArray[rolesArray.length - 1];
      this.createUserForm.patchValue({ role: [lastSelectedRole] });
      console.log(
        'Invalid multi-select combination. Keeping only:',
        lastSelectedRole
      );
    } else {
      console.log('Valid role selection:', rolesArray);
    }

    // Log the multi-select capability for debugging
    console.log(
      'Multi-select allowed for roles:',
      isValidMultiSelectCombination
    );
  }

  // Form submission
  onSubmit() {
    this.isFormSubmitted = true;
    debugger;
    if (this.createUserForm.valid && !this.isLoading) {
      this.isLoading = true;
      const formData = this.createUserForm.value;

      console.log('Creating user with data:', formData);

      // Create AddUserCommand from form data
      const addUserCommand: any = {
        email: formData.email,
        iban: formData.iban,
        nationality: formData.nationality,
        passportNo: formData.passportNo,
        fullName: formData.name,
        userName: `${formData.mobile}`, // Combine country code with mobile number
        password: formData.password,
        confirmPassword: formData.password,
        cvFile: formData.cv,
        personalPhoto: formData.personalPhoto,
        roles: Array.isArray(formData.role) ? formData.role : [formData.role], // Ensure roles is an array
      };

      this.userManagementService
        .createUser(addUserCommand)
        .subscribe((response) => {
          debugger;
          if (response) {
            console.log('User created successfully:', response);
            this.errorModalService.showSuccess(
              response.data ?? 'USER_MANAGEMENT.CREATE.SUCCESS'
            );

            this.router.navigate(['/admin/user-management']);
          }
        });
    }
  }

  onCancel(): void {
    this.router.navigate(['/admin/user-management']);
  }
}
