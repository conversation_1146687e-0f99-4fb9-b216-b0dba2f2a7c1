import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

// Shared components
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';

// Core interfaces and enums
import { IControlOption } from '@shared/interfaces/i-control-option';
import { InputType } from '@shared/enum/input-type.enum';
import { AppearanceEnum } from '@shared/enum/appearance-enum';
import { SizeEnum } from '@core/enums/size';
import { ButtonTypeEnum } from '@core/enums/icon-enum';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';

// User interfaces
import { IUser } from '../interfaces/user.interface';

// Validators - using built-in pattern validator for Saudi phone numbers

@Component({
  selector: 'app-create-user',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    FormBuilderComponent,
    PageHeaderComponent,
    BreadcrumbComponent,
    CustomButtonComponent
  ],
  templateUrl: './create-user.component.html',
  styleUrls: ['./create-user.component.scss']
})
export class CreateUserComponent implements OnInit {
  createUserForm!: FormGroup;
  formControls: IControlOption[] = [];
  isFormSubmitted = false;
  isLoading = false;

  // Enums for template
  readonly ButtonTypeEnum = ButtonTypeEnum;

  breadcrumbItems: IBreadcrumbItem[] = [
    {
      label: 'USER_MANAGEMENT.BREADCRUMB.HOME',
      url: '/dashboard'
    },
    {
      label: 'USER_MANAGEMENT.BREADCRUMB.USER_MANAGEMENT',
      url: '/user-management'
    },
    {
      label: 'USER_MANAGEMENT.BREADCRUMB.CREATE_USER',
      url: '',
      disabled: true
    }
  ];

  constructor(
    private formBuilder: FormBuilder,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.setupFormControls();
  }

  private initializeForm(): void {
    this.createUserForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      mobile: ['', [Validators.required, Validators.pattern(/^5[0-9]{8}$/)]],
      iban: ['', [Validators.required]],
      nationality: ['SA', [Validators.required]],
      cv: [''],
      passportNo: ['', [Validators.required]],
      status: ['active', [Validators.required]],
      role: ['', [Validators.required]],
      password: ['', [Validators.required, Validators.minLength(8)]],
      isActive: [true],
      registrationMessageSent: [true],
      registrationCompleted: [false]
    });
  }

  private setupFormControls(): void {
    this.formControls = [
      // Basic Information Section
      {
        formControlName: 'name',
        type: InputType.Text,
        id: 'name',
        name: 'name',
        label: 'USER_MANAGEMENT.CREATE.NAME',
        placeholder: 'USER_MANAGEMENT.CREATE.NAME_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-6',
        maxLength: 100
      },
      {
        formControlName: 'email',
        type: InputType.Email,
        id: 'email',
        name: 'email',
        label: 'USER_MANAGEMENT.CREATE.EMAIL',
        placeholder: 'USER_MANAGEMENT.CREATE.EMAIL_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-6',
        maxLength: 100
      },
      {
        formControlName: 'mobile',
        type: InputType.Tel,
        id: 'mobile',
        name: 'mobile',
        label: 'USER_MANAGEMENT.CREATE.MOBILE',
        placeholder: 'USER_MANAGEMENT.CREATE.MOBILE_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-6',
        maxLength: 9
      },

      // Personal Details
      {
        formControlName: 'nationality',
        type: InputType.Dropdown,
        id: 'nationality',
        name: 'nationality',
        label: 'USER_MANAGEMENT.CREATE.NATIONALITY',
        placeholder: 'USER_MANAGEMENT.CREATE.NATIONALITY_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-6',
        options: [
          { id: 'SA', name: 'USER_MANAGEMENT.NATIONALITIES.SAUDI' },
          { id: 'AE', name: 'USER_MANAGEMENT.NATIONALITIES.UAE' },
          { id: 'KW', name: 'USER_MANAGEMENT.NATIONALITIES.KUWAIT' },
          { id: 'QA', name: 'USER_MANAGEMENT.NATIONALITIES.QATAR' },
          { id: 'BH', name: 'USER_MANAGEMENT.NATIONALITIES.BAHRAIN' },
          { id: 'OM', name: 'USER_MANAGEMENT.NATIONALITIES.OMAN' },
          { id: 'JO', name: 'USER_MANAGEMENT.NATIONALITIES.JORDAN' },
          { id: 'LB', name: 'USER_MANAGEMENT.NATIONALITIES.LEBANON' },
          { id: 'EG', name: 'USER_MANAGEMENT.NATIONALITIES.EGYPT' },
          { id: 'OTHER', name: 'USER_MANAGEMENT.NATIONALITIES.OTHER' }
        ]
      },
      {
        formControlName: 'passportNo',
        type: InputType.Text,
        id: 'passportNo',
        name: 'passportNo',
        label: 'USER_MANAGEMENT.CREATE.PASSPORT_NO',
        placeholder: 'USER_MANAGEMENT.CREATE.PASSPORT_NO_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-6',
        maxLength: 20
      },

      // System Access & Role Information
      {
        formControlName: 'role',
        type: InputType.Dropdown,
        id: 'role',
        name: 'role',
        label: 'USER_MANAGEMENT.CREATE.ROLE',
        placeholder: 'USER_MANAGEMENT.CREATE.ROLE_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-6',
        options: [
          { id: 'admin', name: 'USER_MANAGEMENT.ROLES.ADMIN' },
          { id: 'manager', name: 'USER_MANAGEMENT.ROLES.MANAGER' },
          { id: 'employee', name: 'USER_MANAGEMENT.ROLES.EMPLOYEE' },
          { id: 'viewer', name: 'USER_MANAGEMENT.ROLES.VIEWER' }
        ]
      },
      {
        formControlName: 'status',
        type: InputType.Dropdown,
        id: 'status',
        name: 'status',
        label: 'USER_MANAGEMENT.CREATE.STATUS',
        placeholder: 'USER_MANAGEMENT.CREATE.STATUS_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-6',
        options: [
          { id: 'active', name: 'USER_MANAGEMENT.STATUS.ACTIVE' },
          { id: 'inactive', name: 'USER_MANAGEMENT.STATUS.INACTIVE' },
          { id: 'pending', name: 'USER_MANAGEMENT.STATUS.PENDING' },
          { id: 'suspended', name: 'USER_MANAGEMENT.STATUS.SUSPENDED' }
        ]
      },

      // Financial Information
      {
        formControlName: 'iban',
        type: InputType.Text,
        id: 'iban',
        name: 'iban',
        label: 'USER_MANAGEMENT.CREATE.IBAN',
        placeholder: 'USER_MANAGEMENT.CREATE.IBAN_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-6',
        maxLength: 34
      },
      {
        formControlName: 'cv',
        type: InputType.file,
        id: 'cv',
        name: 'cv',
        label: 'USER_MANAGEMENT.CREATE.CV',
        placeholder: 'USER_MANAGEMENT.CREATE.CV_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-12',
        allowedTypes: ['pdf', 'doc', 'docx'],
        max: 5
      },
      {
        formControlName: 'password',
        type: InputType.Password,
        id: 'password',
        name: 'password',
        label: 'USER_MANAGEMENT.CREATE.PASSWORD',
        placeholder: 'USER_MANAGEMENT.CREATE.PASSWORD_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-12',
        minLength: 8,
        maxLength: 50
      }
    ];

    // Add checkbox controls
    this.addCheckboxControls();
  }



  private addCheckboxControls(): void {
    const checkboxControls: IControlOption[] = [
      // Account Settings - Switch for Active/Inactive
      {
        formControlName: 'isActive',
        type: InputType.Switch,
        id: 'isActive',
        name: 'isActive',
        label: 'USER_MANAGEMENT.CREATE.ACCOUNT_SETTINGS',
        isRequired: false,
        class: 'col-md-4',
        options: [
          { id: true, name: 'USER_MANAGEMENT.CREATE.IS_ACTIVE' }
        ]
      },
      // Readonly checkboxes in same line
      {
        formControlName: 'registrationMessageSent',
        type: InputType.Checkbox,
        id: 'registrationMessageSent',
        name: 'registrationMessageSent',
        label: '',
        isRequired: false,
        isReadonly: true,
        class: 'col-md-4',
        options: [
          { id: true, name: 'USER_MANAGEMENT.CREATE.REGISTRATION_MESSAGE_SENT' }
        ]
      },
      {
        formControlName: 'registrationCompleted',
        type: InputType.Checkbox,
        id: 'registrationCompleted',
        name: 'registrationCompleted',
        label: '',
        isRequired: false,
        isReadonly: true,
        class: 'col-md-4',
        options: [
          { id: true, name: 'USER_MANAGEMENT.CREATE.REGISTRATION_COMPLETED' }
        ]
      }
    ];

    this.formControls.push(...checkboxControls);
  }

  // Form event handlers
  onControlFocus(event: any, control: IControlOption): void {
    console.log('Control focused:', control.formControlName);
  }

  onValueChange(event: any, control: IControlOption): void {
    console.log('Value changed:', control.formControlName, event);
  }

  onKeyPressed(event: any, control: IControlOption): void {
    // Handle specific key press events if needed
  }

  onCheckboxChange(event: any, control: IControlOption): void {
    console.log('Checkbox changed:', control.formControlName, event);
  }

  onSwitchChange(event: any, control: IControlOption): void {
    console.log('Switch changed:', control.formControlName, event);
  }

  onDateSelected(event: any, control: IControlOption): void {
    console.log('Date selected:', control.formControlName, event);
  }

  onDropdownChange(event: any, control: IControlOption): void {
    console.log('Dropdown changed:', control.formControlName, event);

    // Handle dependent dropdowns if needed
    if (control.formControlName === 'role') {
      this.handleRoleChange(event);
    }
  }

  onFileUploaded(event: any, control: IControlOption): void {
    console.log('File uploaded:', control.formControlName, event);
  }

  private handleRoleChange(selectedRole: string): void {
    // Update manager dropdown based on selected role
    const managerControl = this.formControls.find(c => c.formControlName === 'manager');
    if (managerControl && selectedRole !== 'admin') {
      // Show manager field for non-admin roles
      managerControl.isRequired = true;
    } else if (managerControl) {
      // Hide manager field for admin role
      managerControl.isRequired = false;
      this.createUserForm.get('manager')?.setValue('');
    }
  }

  // Form submission
  onSubmit(): void {
    this.isFormSubmitted = true;

    if (this.createUserForm.valid) {
      this.isLoading = true;
      const formData = this.createUserForm.value;

      console.log('Creating user with data:', formData);

      // Simulate API call
      setTimeout(() => {
        this.isLoading = false;
        // Navigate back to user management list
        this.router.navigate(['/user-management']);
      }, 2000);
    } else {
      console.log('Form is invalid');
      this.markFormGroupTouched();
    }
  }

  onCancel(): void {
    this.router.navigate(['/user-management']);
  }

  private markFormGroupTouched(): void {
    Object.keys(this.createUserForm.controls).forEach(key => {
      const control = this.createUserForm.get(key);
      control?.markAsTouched();
    });
  }
}
