export interface IUser {
  id: number;
  name: string;
  email: string;
  status: IUserStatus;
  role: string;
  lastUpdateDate: Date;
  createdDate?: Date;
  phoneNumber?: string;
  department?: string;
  permissions?: string[];
}

export enum IUserStatus {
  Active = 'active',
  Inactive = 'inactive',
  Pending = 'pending',
  Suspended = 'suspended'
}

export interface IUserTableConfig {
  columns: string[];
  sortable: boolean;
  filterable: boolean;
  paginated: boolean;
}

export interface IUserFilters {
  status?: IUserStatus;
  role?: string;
  department?: string;
  searchTerm?: string;
  dateFrom?: Date;
  dateTo?: Date;
}

export interface IUserCreateRequest {
  name: string;
  email: string;
  role: string;
  department?: string;
  phoneNumber?: string;
  permissions?: string[];
}

export interface IUserUpdateRequest extends Partial<IUserCreateRequest> {
  id: number;
  status?: IUserStatus;
}

export interface IUserResponse {
  users: IUser[];
  totalCount: number;
  pageSize: number;
  currentPage: number;
  totalPages: number;
}

export interface IUserActionEvent {
  action: 'edit' | 'delete' | 'view' | 'activate' | 'deactivate';
  user: IUser;
}
