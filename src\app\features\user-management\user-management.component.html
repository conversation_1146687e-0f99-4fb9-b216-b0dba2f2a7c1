<div class="user-management-container">
  <!-- Breadcrumb -->
  <app-breadcrumb [breadcrumbs]="breadcrumbItems"></app-breadcrumb>

  <!-- Page Header -->
  <app-page-header 
    [title]="'USER_MANAGEMENT.TITLE' | translate"
    [showCreateButton]="true"
    [createButtonText]="'USER_MANAGEMENT.ADD_USER' | translate"
    (create)="onAddUser()">
  </app-page-header>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">{{ 'COMMON.LOADING' | translate }}</span>
    </div>
    <p class="mt-2">{{ 'USER_MANAGEMENT.LOADING_USERS' | translate }}</p>
  </div>

  <!-- Users Table -->
  <div *ngIf="!isLoading" class="table-container">
    <app-table
      [columns]="columns"
      [displayedColumns]="displayedColumns"
      [dataSource]="dataSource"
      [selection]="selection"
      [sortingType]="sortingType"
      [paginationType]="paginationType"
      [totalItems]="totalItems"
      [pageSize]="pageSize"
      (onClickAction)="onTableAction($event)"
      (switchToggleEvent)="onSwitchToggle($event)"
      (toggleAllRows)="onToggleAllRows()"
      (toggleRow)="onToggleRow($event)"
      (sortChanged)="onSortChanged($event)"
      (pageChange)="onPageChange($event)">
    </app-table>
  </div>

  <!-- Empty State -->
  <div *ngIf="!isLoading && dataSource.data.length === 0" class="empty-state">
    <div class="empty-state-content">
      <img src="assets/images/empty-state.png" alt="No users" class="empty-state-image">
      <h3>{{ 'USER_MANAGEMENT.NO_USERS' | translate }}</h3>
      <p>{{ 'USER_MANAGEMENT.NO_USERS_MESSAGE' | translate }}</p>
      <button 
        type="button" 
        class="btn btn-primary"
        (click)="onAddUser()">
        {{ 'USER_MANAGEMENT.ADD_FIRST_USER' | translate }}
      </button>
    </div>
  </div>
</div>
