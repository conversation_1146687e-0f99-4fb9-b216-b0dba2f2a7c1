@import "../../../assets/scss/variables";

// Custom styling for switch component in table
// ::ng-deep .mat-slide-toggle.mat-checked .mat-slide-toggle-thumb {
//   background-color: $navy-blue !important;
// }

// ::ng-deep .mat-slide-toggle.mat-checked .mat-slide-toggle-bar {
//   background-color: rgba(0, 32, 90, 0.54) !important; // $navy-blue with opacity
// }

// ::ng-deep .mat-slide-toggle .mat-slide-toggle-bar {
//   background-color: rgba(0, 0, 0, 0.38) !important;
// }

// ::ng-deep .mat-slide-toggle .mat-slide-toggle-thumb {
//   background-color: #fafafa !important;
// }

::ng-deep {
  .mdc-switch--selected:enabled .mdc-switch__handle {
    &::after,
    &::before {
      background-color: $navy-blue !important;
    }
  }

  .mdc-switch--selected:enabled .mdc-switch__handle::after {
    background: #00205a;
  }

  .mdc-switch__track::after {
    background: #00205a;
  }
}

.user-management-container {
  padding: 1rem;

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;

    .spinner-border {
      width: 3rem;
      height: 3rem;
    }

    p {
      color: #6c757d;
      font-size: 16px;
      margin: 0;
    }
  }

  .table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-top: 20px;
  }

  .empty-state, .no-results-state {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-top: 20px;

    .empty-state-content {
      text-align: center;
      max-width: 400px;
      padding: 40px 20px;

      .empty-state-image {
        width: 120px;
        height: 120px;
        margin-bottom: 24px;
        opacity: 0.6;
      }

      h3 {
        color: #00205a;
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 12px;
      }

      p {
        color: #6c757d;
        font-size: 16px;
        line-height: 1.5;
        margin-bottom: 24px;
      }

      .btn {
        padding: 12px 24px;
        font-weight: 500;
        border-radius: 8px;
        transition: all 0.2s ease;

        &.btn-primary {
          background-color: #007bff;
          border-color: #007bff;

          &:hover {
            background-color: #0056b3;
            border-color: #004085;
            transform: translateY(-1px);
          }
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .user-management-container {
    padding: 16px;

    .table-container {
      margin-top: 16px;
      border-radius: 8px;
    }

    .empty-state {
      margin-top: 16px;
      border-radius: 8px;
      min-height: 300px;

      .empty-state-content {
        padding: 30px 16px;

        .empty-state-image {
          width: 80px;
          height: 80px;
          margin-bottom: 20px;
        }

        h3 {
          font-size: 20px;
          margin-bottom: 10px;
        }

        p {
          font-size: 14px;
          margin-bottom: 20px;
        }

        .btn {
          padding: 10px 20px;
          font-size: 14px;
        }
      }
    }

    .loading-container {
      padding: 40px 16px;

      .spinner-border {
        width: 2.5rem;
        height: 2.5rem;
      }

      p {
        font-size: 14px;
      }
    }
  }
}

// RTL Support
[dir="rtl"] {
  .user-management-container {
    .empty-state-content, .no-results-content {
      .btn {
        direction: rtl;
      }
    }
  }
}
