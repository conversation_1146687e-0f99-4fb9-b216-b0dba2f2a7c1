import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { MatTableDataSource } from '@angular/material/table';
import { SelectionModel } from '@angular/cdk/collections';

// Shared components
import { TableComponent } from '@shared/components/table/table.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';

// Core interfaces and enums
import { ActionDisplayMode, ITableColumn, TableActionEvent, SwitchToggleEvent } from '@core/gl-interfaces/I-table/i-table';
import { ColumnTypeEnum, DataHandlingType } from '@core/enums/column-type';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';

// User interfaces
import { IUser, IUserStatus } from './interfaces/user.interface';

@Component({
  selector: 'app-user-management',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    TableComponent,
    PageHeaderComponent,
    BreadcrumbComponent
  ],
  templateUrl: './user-management.component.html',
  styleUrl: './user-management.component.scss'
})
export class UserManagementComponent implements OnInit {
  // Table configuration
  columns: ITableColumn[] = [];
  displayedColumns: string[] = [];
  dataSource = new MatTableDataSource<IUser>([]);
  selection = new SelectionModel<IUser>(true, []);
  
  // Data handling
  sortingType = DataHandlingType.Frontend;
  paginationType = DataHandlingType.Frontend;
  totalItems = 0;
  pageSize = 10;
  
  // Loading state
  isLoading = false;
  
  // Breadcrumb
  breadcrumbItems: IBreadcrumbItem[] = [
    { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
    { label: 'BREADCRUMB.USER_MANAGEMENT', active: true }
  ];

  constructor(private router: Router) {}

  ngOnInit(): void {
    this.initializeTable();
    this.loadUsers();
  }

  private initializeTable(): void {
    this.columns = [
      {
        columnDef: 'name',
        header: 'USER_MANAGEMENT.COLUMNS.NAME',
        columnType: ColumnTypeEnum.Text,
        cell: (element: IUser) => element.name,
        isSortingBy: true
      },
      {
        columnDef: 'email',
        header: 'USER_MANAGEMENT.COLUMNS.EMAIL',
        columnType: ColumnTypeEnum.Text,
        cell: (element: IUser) => element.email,
        isSortingBy: true
      },
      {
        columnDef: 'isActive',
        header: 'USER_MANAGEMENT.COLUMNS.STATUS',
        columnType: ColumnTypeEnum.Switch,
        cell: (element: IUser) => element.status === IUserStatus.Active,
        isSortingBy: false
      },
      {
        columnDef: 'role',
        header: 'USER_MANAGEMENT.COLUMNS.ROLE',
        columnType: ColumnTypeEnum.Text,
        cell: (element: IUser) => element.role,
        isSortingBy: true
      },
      {
        columnDef: 'lastUpdateDate',
        header: 'USER_MANAGEMENT.COLUMNS.LAST_UPDATE_DATE',
        columnType: ColumnTypeEnum.Text,
        cell: (element: IUser) => this.formatDate(element.lastUpdateDate),
        isSortingBy: true
      },
      {
        columnDef: 'actions',
        header: 'USER_MANAGEMENT.COLUMNS.ACTIONS',
        columnType: ColumnTypeEnum.Actions,
        displayMode: ActionDisplayMode.Flex,
        cell: (element: IUser) => ({
          buttons: this.getActionsForUser(element)
        })
      }
    ];

    this.displayedColumns = this.columns.map(col => col.columnDef);
  }

  private getActionsForUser(user: IUser): any[] {
    const actions = [
      {
        label: 'USER_MANAGEMENT.ACTIONS.VIEW_DETAILS',
        action: 'view',
        iconSrc: 'assets/images/eye.png'
      },
      {
        label: 'USER_MANAGEMENT.ACTIONS.EDIT',
        action: 'edit',
        iconSrc: 'assets/images/edit.png'
      }
    ];

    // Removed activate/deactivate actions - now handled by switch column

    // Reset password - available for active and pending users
    if (user.status === 'active' || user.status === 'pending') {
      actions.push({
        label: 'USER_MANAGEMENT.ACTIONS.RESET_PASSWORD',
        action: 'resetPassword',
        iconSrc: 'assets/images/reset-icon.png'
      });
    }

    // Resend message - only for pending users
    if (user.status === 'pending') {
      actions.push({
        label: 'USER_MANAGEMENT.ACTIONS.RESEND_MESSAGE',
        action: 'resendMessage',
        iconSrc: 'assets/images/notification.png'
      });
    }

    return actions;
  }

  private loadUsers(): void {
    this.isLoading = true;
    
    // Mock data - replace with actual API call
    const mockUsers: IUser[] = [
      {
        id: 1,
        name: 'أحمد محمد',
        email: '<EMAIL>',
        status: IUserStatus.Active,
        role: 'مدير الصندوق',
        lastUpdateDate: new Date('2024-01-15T10:30:00')
      },
      {
        id: 2,
        name: 'فاطمة علي',
        email: '<EMAIL>',
        status: IUserStatus.Inactive,
        role: 'محاسب',
        lastUpdateDate: new Date('2024-01-10T14:20:00')
      },
      {
        id: 3,
        name: 'محمد السعيد',
        email: '<EMAIL>',
        status: IUserStatus.Active,
        role: 'مطور',
        lastUpdateDate: new Date('2024-01-12T09:15:00')
      }
    ];

    setTimeout(() => {
      this.dataSource.data = mockUsers;
      this.totalItems = mockUsers.length;
      this.isLoading = false;
    }, 1000);
  }

  // Removed getStatusObject method - no longer needed with switch component

  private formatDate(date: Date): string {
    if (!date) return '';
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  }

  onTableAction(event: TableActionEvent): void {
    const { action, row } = event;

    switch (action) {
      case 'view':
        this.viewUserDetails(row);
        break;
      case 'edit':
        this.editUser(row);
        break;

      case 'resetPassword':
        this.resetUserPassword(row);
        break;
      case 'resendMessage':
        this.resendMessage(row);
        break;

      default:
        console.log('Unknown action:', action);
    }
  }

  onSwitchToggle(event: SwitchToggleEvent): void {
    const { row, newValue } = event;
    console.log('Toggling user status:', row, 'New value:', newValue);

    // Update user status based on switch value
    const user = row as IUser;
    const newStatus = newValue ? IUserStatus.Active : IUserStatus.Inactive;

    // Here you would typically call an API to update the user status
    // For now, we'll just update the local data
    const userIndex = this.dataSource.data.findIndex(u => u.id === user.id);
    if (userIndex !== -1) {
      this.dataSource.data[userIndex].status = newStatus;
      // Trigger change detection
      this.dataSource.data = [...this.dataSource.data];
    }

    console.log(`User ${user.name} status changed to: ${newStatus}`);
    // TODO: Implement API call to update user status
    // this.userService.updateUserStatus(user.id, newStatus).subscribe(...);
  }

  // Action implementations
  private viewUserDetails(user: IUser): void {
    console.log('Viewing user details:', user);
    // Navigate to user details page
    // this.router.navigate(['/admin/user-management/details', user.id]);
  }

  private editUser(user: IUser): void {
    console.log('Editing user:', user);
    // Navigate to edit user form
    // this.router.navigate(['/admin/user-management/edit', user.id]);
  }

  // Removed deactivateUser and activateUser methods - now handled by onSwitchToggle

  private resetUserPassword(user: IUser): void {
    console.log('Resetting password for user:', user);
    // Show confirmation dialog and reset password
    // Only show if user is eligible (not inactive or registration not completed)
    if (user.status !== 'inactive') {
      // Implement password reset logic
      // this.userService.resetPassword(user.id).subscribe(...);
    }
  }

  private resendMessage(user: IUser): void {
    console.log('Resending message to user:', user);
    // Show confirmation dialog and resend registration message
    // Only show if user registration is not completed
    if (user.status === 'pending') {
      // Implement resend message logic
      // this.userService.resendRegistrationMessage(user.id).subscribe(...);
    }
  }

  onToggleAllRows(): void {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.dataSource.data.forEach(row => this.selection.select(row));
    }
  }

  onToggleRow(row: IUser): void {
    this.selection.toggle(row);
  }

  private isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  onSortChanged(event: { active: string; direction: string }): void {
    console.log('Sort changed:', event);
    // Implement sorting if using backend sorting
  }

  onPageChange(event: any): void {
    console.log('Page changed:', event);
    // Implement pagination if using backend pagination
  }

  onAddUser(): void {
    console.log('Add new user');
    this.router.navigate(['/admin/user-management/create']);
  }
}
