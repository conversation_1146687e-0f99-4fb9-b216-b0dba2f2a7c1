import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { MatTableDataSource } from '@angular/material/table';
import { SelectionModel } from '@angular/cdk/collections';
import { MatDialog } from '@angular/material/dialog';
import { catchError, finalize } from 'rxjs/operators';
import { of } from 'rxjs';

// Shared components
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { TableComponent } from '@shared/components/table/table.component';

// Core interfaces
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import {
  ActionDisplayMode,
  ITableColumn,
  TableActionEvent,
  SwitchToggleEvent,
} from '@core/gl-interfaces/I-table/i-table';
import { ColumnTypeEnum, DataHandlingType } from '@core/enums/column-type';

// User interfaces and components
import { IUser, IUserStatus, IUserFilters } from './interfaces/user.interface';
import { UserFilterDialogComponent } from './components/user-filter-dialog/user-filter-dialog.component';
import { UserManagementService } from '@shared/services/users/user-management.service';

@Component({
  selector: 'app-user-management',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    PageHeaderComponent,
    BreadcrumbComponent,
    TableComponent,
  ],
  providers: [UserManagementService],
  templateUrl: './user-management.component.html',
  styleUrl: './user-management.component.scss',
})
export class UserManagementComponent implements OnInit {
  // User data
  users: IUser[] = [];
  totalItems = 0;
  pageSize = 10;
  currentPage = 1;

  // Sorting
  currentSortField = 'lastUpdateDate';
  currentSortDirection = 'desc';

  // Filters
  currentFilters: IUserFilters = {};
  mobileSearchTerm = '';

  // Loading state
  isLoading = false;

  // Table configuration
  columns: ITableColumn[] = [];
  displayedColumns: string[] = [];
  dataSource = new MatTableDataSource<IUser>([]);
  selection = new SelectionModel<IUser>(true, []);

  // Data handling
  sortingType = DataHandlingType.Backend;
  paginationType = DataHandlingType.Backend;

  // Breadcrumb
  breadcrumbItems: IBreadcrumbItem[] = [
    { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
    { label: 'BREADCRUMB.USER_MANAGEMENT', active: true },
  ];

  constructor(
    private router: Router,
    private userManagementService: UserManagementService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.initializeTable();
    this.loadUsers();
  }

  private initializeTable(): void {
    this.columns = [
      {
        columnDef: 'fullName',
        header: 'USER_MANAGEMENT.COLUMNS.NAME',
        columnType: ColumnTypeEnum.Text,
        cell: (element: IUser) => element.fullName,
        isSortingBy: true,
      },
      {
        columnDef: 'email',
        header: 'USER_MANAGEMENT.COLUMNS.EMAIL',
        columnType: ColumnTypeEnum.Text,
        cell: (element: IUser) => element.email,
        isSortingBy: true,
      },
      {
        columnDef: 'isActive',
        header: 'USER_MANAGEMENT.COLUMNS.STATUS',
        columnType: ColumnTypeEnum.Switch,
        cell: (element: IUser) => element.isActive,
        isSortingBy: false,
      },
      {
        columnDef: 'roles',
        header: 'USER_MANAGEMENT.COLUMNS.ROLE',
        columnType: ColumnTypeEnum.Text,
        cell: (element: IUser) => element.roles.join(', '),
        isSortingBy: true,
      },
      {
        columnDef: 'lastUpdateDate',
        header: 'USER_MANAGEMENT.COLUMNS.LAST_UPDATE_DATE',
        columnType: ColumnTypeEnum.Text,
        cell: (element: IUser) => this.formatDate(element.lastUpdateDate),
        isSortingBy: true,
      },
      {
        columnDef: 'actions',
        header: 'USER_MANAGEMENT.COLUMNS.ACTIONS',
        columnType: ColumnTypeEnum.Actions,
        displayMode: ActionDisplayMode.Flex,
        cell: (element: IUser) => ({
          buttons: this.getActionsForUser(element),
        }),
      },
    ];

    this.displayedColumns = this.columns.map((col) => col.columnDef);
  }

  private formatDate(date: Date): string {
    if (!date) return '';
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(date));
  }

  private getActionsForUser(user: IUser): any[] {
    const actions = [
      {
        label: 'USER_MANAGEMENT.ACTIONS.VIEW_DETAILS',
        action: 'view',
        iconSrc: 'assets/images/eye.png',
      },
      {
        label: 'USER_MANAGEMENT.ACTIONS.EDIT',
        action: 'edit',
        iconSrc: 'assets/images/edit.png',
      },
      {
        label: 'USER_MANAGEMENT.ACTIONS.RESET_PASSWORD',
        action: 'resetPassword',
        iconSrc: 'assets/images/reset-icon.png',
      }
    ];

    return actions;
  }

  loadUsers(): void {
    this.isLoading = true;

    // Build API parameters
    const role = this.currentFilters.role || undefined;
    const isActive =
      this.currentFilters.status === IUserStatus.Active
        ? true
        : this.currentFilters.status === IUserStatus.Inactive
        ? false
        : undefined;
    const name = this.currentFilters.name || undefined;
    const search =
      this.currentFilters.searchTerm || this.mobileSearchTerm || undefined;
    const orderBy =
      this.currentSortField && this.currentSortDirection
        ? `${this.currentSortField} ${this.currentSortDirection}`
        : undefined;

    this.userManagementService
      .getUserList(
        role,
        isActive,
        name,
        this.currentPage,
        this.pageSize,
        search,
        orderBy
      )
      .subscribe((response: any) => {
        if (response && response.data) {
          this.users = response.data;
          this.dataSource.data = this.users;
          this.totalItems = response.totalCount || 0;
        }
      });
  }

  // Table event handlers
  onTableAction(event: TableActionEvent): void {
    const { action, row } = event;
    this.onUserAction({ action, user: row as IUser });
  }

  onSwitchToggle(event: SwitchToggleEvent): void {
    const { row, newValue } = event;
    this.onUserStatusToggled({ user: row as IUser, newStatus: newValue });
  }

  onUserAction(event: { action: string; user: IUser }): void {
    const { action, user } = event;

    switch (action) {
      case 'view':
        this.viewUserDetails(user);
        break;
      case 'edit':
        this.editUser(user);
        break;
      case 'resetPassword':
        this.resetUserPassword(user);
        break;
      case 'resendMessage':
        this.resendMessage(user);
        break;
      default:
        console.log('Unknown action:', action);
    }
  }

  onUserStatusToggled(event: { user: IUser; newStatus: boolean }): void {
    const { user, newStatus } = event;
    console.log('Toggling user status:', user, 'New value:', newStatus);

    const activateUserBody = {
      userId: user.id,
      activate: newStatus,
      sendNotification: true,
    };

    this.userManagementService
      .activateUser(activateUserBody)
      .subscribe((response) => {
        if (response) {
          console.log('User status updated successfully');
          this.loadUsers(); // Reload the user list to reflect changes
        }
      });
  }

  onSortChanged(event: { active: string; direction: string }): void {}

  onPageChanged(event: any): void {}

  onFiltersChanged(filters: IUserFilters): void {
    this.loadUsers();
  }

  // Search and filter methods
  onSearch(searchTerm: string): void {
    this.mobileSearchTerm = searchTerm;
    this.currentFilters.mobileNo = searchTerm || undefined;
    this.currentPage = 1;
    this.loadUsers();
  }

  openAdvancedFilters(): void {
    const dialogRef = this.dialog.open(UserFilterDialogComponent, {
      width: '500px',
      data: { ...this.currentFilters },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // Merge with mobile filter
        this.currentFilters = {
          ...result,
          mobileNo: this.mobileSearchTerm || undefined,
        };
        this.currentPage = 1; // Reset to first page when filters change
        this.loadUsers();
      }
    });
  }

  clearAllFilters(): void {
    this.mobileSearchTerm = '';
    this.currentFilters = {};
    this.currentPage = 1; // Reset to first page when filters are cleared
    this.loadUsers();
  }

  hasActiveFilters(): boolean {
    return (
      Object.keys(this.currentFilters).length > 0 || !!this.mobileSearchTerm
    );
  }

  private isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  // Action implementations
  private viewUserDetails(user: IUser): void {
    console.log('Viewing user details:', user);
    // Navigate to user details page
     this.router.navigate(['/admin/user-management/details', user.id]);
  }

  private editUser(user: IUser): void {
    console.log('Editing user:', user);
    // Navigate to edit user form
    this.router.navigate(['/admin/user-management/edit', user.id]);
  }

  private resetUserPassword(user: IUser): void {
    console.log('Resetting password for user:', user);
    // Show confirmation dialog and reset password
    // Only show if user is eligible (active, registration completed, message sent)
    if (
      user.isActive &&
      user.registrationIsCompleted &&
      user.registrationMessageIsSent
    ) {
      // TODO: Show confirmation dialog first
      const command = {
        id: user.id,
        currentPassword: undefined, // For admin reset, current password might not be required
        newPassword: 'TempPassword123!', // This should be generated or provided by user
        confirmPassword: 'TempPassword123!',
        isMandatoryReset: true,
      };
    }
  }

  private resendMessage(user: IUser): void {}

  onAddUser(): void {
    console.log('Add new user');
    this.router.navigate(['/admin/user-management/create']);
  }
}
