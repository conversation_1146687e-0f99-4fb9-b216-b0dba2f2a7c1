import { Routes } from '@angular/router';
import { AuthGuard } from '@core/guards/auth.guard';

export const USER_MANAGEMENT_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('./user-management.component').then(m => m.UserManagementComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'create',
    loadComponent: () =>
      import('./create-user/create-user.component').then(m => m.CreateUserComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'edit/:id',
    loadComponent: () =>
      import('./components/edit-user/edit-user.component').then(m => m.EditUserComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'details/:id',
    loadComponent: () =>
      import('./components/user-profile/user-profile.component').then(m => m.UserProfileComponent),
    canActivate: [AuthGuard]
  }
];
