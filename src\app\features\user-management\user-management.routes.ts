import { Routes } from '@angular/router';
import { AuthGuard } from '@core/guards/auth.guard';

export const USER_MANAGEMENT_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('./user-management.component').then(m => m.UserManagementComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'create',
    loadComponent: () =>
      import('./create-user/create-user.component').then(m => m.CreateUserComponent),
    canActivate: [AuthGuard]
  }
  // TODO: Add additional routes for edit and details components
  // {
  //   path: 'edit/:id',
  //   loadComponent: () =>
  //     import('./components/edit-user/edit-user.component').then(m => m.EditUserComponent),
  //   canActivate: [AuthGuard]
  // },
  // {
  //   path: 'details/:id',
  //   loadComponent: () =>
  //     import('./components/user-details/user-details.component').then(m => m.UserDetailsComponent),
  //   canActivate: [AuthGuard]
  // }
];
