@import "../../../../assets/scss/variables";

.breadcrumb-container {
  top: 2.25rem;
  .breadcrumb-item {
    cursor: pointer;

    &.active {
      font-weight: bold;
    }

    &:hover {
      color: $text-color;
      background-color: $neutral-background-hover;
    }

    &:focus {
      border: 2px solid $black;
      border-radius: 4px;
      -webkit-border-radius: 4px;
      -moz-border-radius: 4px;
      -ms-border-radius: 4px;
      -o-border-radius: 4px;
    }

    &:active {
      background-color: $border-dark;
    }

    .breadcrumb-icon {
      margin-right: 4px;
    }
  }

  display: flex;
  align-items: center;
  white-space: nowrap;
  width: fit-content;

  .disabled {
    cursor: not-allowed;
    font-weight: 700;
  }

  a {
    padding: 0 10px;
    text-decoration: none;
    color: $text-color;
    transition: color 0.3s ease;
    cursor: pointer;
  }

  .breadcrumb-divider {
    color: $text-color;
  }

  .breadcrumb-overflow {
    padding: 0 10px;
    cursor: pointer;
    color: $text-color;
    display: flex;
    align-items: center;
    position: relative;
  }
}

.dropdown {
  position: absolute;
  background-color: $white;
  border: 1px solid $border-light;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
  top: 100%;
  left: 100%;

  a {
    display: block;
    padding: 5px 10px;
    color: $text-color;
    text-decoration: none;
    cursor: pointer;

    &:hover {
      background-color: $neutral-background-hover;
    }
  }

  .mdc-list-item {
    cursor: pointer !important;
  }
}

.breadcrumb-small {
  font-size: 12px;
  padding: 0 5px;
}

.breadcrumb-medium {
  font-size: 16px;
  //   padding: 0 10px;
}

.breadcrumb-large {
  font-size: 22px;
  padding: 0 15px;
}
