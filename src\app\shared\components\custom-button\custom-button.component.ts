import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter } from '@angular/core';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';

@Component({
  selector: 'app-custom-button',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './custom-button.component.html',
  styleUrl: './custom-button.component.scss'
})
export class CustomButtonComponent {
  @Input() iconName:IconEnum | undefined;
  @Input() btnName!: string;
  @Input() class!: string;
  @Input() buttonType: ButtonTypeEnum = ButtonTypeEnum.Primary;
  @Input() disabled: boolean = false;
  @Output() click = new EventEmitter<any>();

  ButtonTypeEnum = ButtonTypeEnum; // لجعل enum متاح داخل HTML

  // getIconSrc(): string {
  //   return this.iconName || 'assets/icons/add-icon.png';
  // }

  action() {
    this.click.emit();
  }
}
