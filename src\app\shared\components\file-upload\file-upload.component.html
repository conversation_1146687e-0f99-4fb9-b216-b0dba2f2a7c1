<!-- Upload Box -->
<div class="upload-container" [ngClass]="fileUrl?'disabled':''" (drop)="onDrop($event)" (dragover)="onDragOver($event)"
    (click)="!fileUrl && fileInput.click()">
    <div class="upload-content">
        <img src="assets/images/file.png" alt="upload icon" />
        <p class="upload-text">{{ 'FILE_UPLOAD.DRAG_DROP_TEXT' | translate }}</p>
        <p class="subtext">
            {{ 'FILE_UPLOAD.MAX_SIZE_TEXT' | translate: {maxSize: maxSize} }}، {{ 'FILE_UPLOAD.SUPPORTED_FORMATS' | translate }}
            <strong>{{ getSupportedFormatsText() }}</strong>
        </p>
        <input type="file" [accept]="getAcceptAttribute()" (change)="onFileSelected($event)" hidden #fileInput [disabled]="selectedFile" />
    </div>
</div>

<div *ngIf="fileUrl" class="d-flex mt-2 gap-2">

    <div class="file-info tag">

        <div class="file-actions">
            <div>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="20" viewBox="0 0 16 20" fill="none">
                    <path
                        d="M11.5299 0H1.16414C0.875546 0 0.641602 0.23393 0.641602 0.687862V19.643C0.641602 19.7662 0.875546 20.0001 1.16414 20.0001H15.4773C15.7659 20.0001 15.9998 19.7662 15.9998 19.643V4.63503C15.9998 4.38646 15.9666 4.30646 15.908 4.24753L11.752 0.0917864C11.693 0.0330981 11.6131 0.000109214 11.5299 0Z"
                        fill="#E9E9E0" />
                    <path d="M11.7144 0.0527344V4.28455H15.9464L11.7144 0.0527344Z" fill="#D9D7CA" />
                    <path
                        d="M5.28978 11.9031C5.16548 11.9031 5.04619 11.8627 4.9444 11.7867C4.57259 11.5077 4.52258 11.1974 4.54616 10.986C4.61116 10.4045 5.33014 9.79595 6.6838 9.17594C7.22098 7.99879 7.73209 6.54842 8.03675 5.33663C7.6803 4.56091 7.33385 3.55447 7.58637 2.96411C7.67494 2.75732 7.78531 2.59875 7.99139 2.53018C8.10986 2.49703 8.23149 2.47644 8.35427 2.46875C8.53429 2.46875 8.69251 2.70054 8.80466 2.8434C8.91003 2.97768 9.14897 3.26233 8.67144 5.2727C9.1529 6.26699 9.83509 7.27986 10.4887 7.97343C10.957 7.88879 11.3598 7.84558 11.6881 7.84558C12.2474 7.84558 12.5864 7.97593 12.7246 8.24451C12.8389 8.46665 12.7921 8.7263 12.5853 9.01594C12.3863 9.29416 12.112 9.4413 11.7924 9.4413C11.3581 9.4413 10.8523 9.16701 10.2883 8.62522C9.27505 8.83701 8.09176 9.21487 7.13526 9.63309C6.83667 10.2667 6.55058 10.777 6.28413 11.1513C5.91804 11.6638 5.6023 11.9031 5.28978 11.9031ZM6.24056 10.0724C5.47729 10.5013 5.1662 10.8538 5.1437 11.0524C5.14013 11.0852 5.13048 11.1717 5.29764 11.2995C5.35085 11.2827 5.66159 11.141 6.24056 10.0724ZM11.1112 8.48594C11.4023 8.70987 11.4734 8.82308 11.6638 8.82308C11.7474 8.82308 11.9856 8.81951 12.096 8.66558C12.1492 8.59094 12.1699 8.54308 12.1781 8.51737C12.1342 8.49415 12.076 8.44701 11.7584 8.44701C11.5781 8.44737 11.3513 8.45522 11.1112 8.48594ZM8.44321 6.13521C8.17582 7.0529 7.85696 7.95481 7.48814 8.83666C8.24424 8.54375 9.01942 8.30271 9.8083 8.11522C9.32613 7.55522 8.84431 6.85593 8.44321 6.13521ZM8.22641 3.11304C8.19141 3.12483 7.75138 3.74055 8.2607 4.26162C8.59965 3.50626 8.24177 3.10804 8.22641 3.11304ZM15.4773 20.0017H1.16414C1.02555 20.0017 0.892643 19.9467 0.794649 19.8487C0.696654 19.7507 0.641602 19.6178 0.641602 19.4792V13.9303H15.9998V19.4792C15.9998 19.7678 15.7659 20.0017 15.4773 20.0017Z"
                        fill="#CC4B4C" />
                    <path
                        d="M4.52947 18.9277H3.94336V15.3291H4.97843C5.1313 15.3291 5.28274 15.3534 5.43239 15.4023C5.58204 15.4512 5.71634 15.5245 5.83528 15.622C5.95421 15.7195 6.05029 15.8377 6.12351 15.9759C6.19673 16.1141 6.23352 16.2695 6.23352 16.4423C6.23352 16.6248 6.20244 16.7898 6.14065 16.938C6.08307 17.0802 5.99462 17.2078 5.88171 17.3116C5.77098 17.4123 5.6374 17.4905 5.48132 17.5459C5.32524 17.6013 5.15237 17.6288 4.96379 17.6288H4.52911L4.52947 18.9277ZM4.52947 15.7734V17.1991H5.06665C5.13808 17.1991 5.2088 17.187 5.27917 17.1623C5.34917 17.138 5.41346 17.098 5.47204 17.0427C5.53061 16.9873 5.57776 16.9102 5.61347 16.8109C5.64919 16.7116 5.66705 16.5888 5.66705 16.4423C5.66705 16.3838 5.65883 16.3159 5.64276 16.2398C5.62507 16.1604 5.591 16.0856 5.54275 16.0202C5.48674 15.9457 5.41393 15.8855 5.33024 15.8445C5.23916 15.7973 5.11844 15.7737 4.96879 15.7737L4.52947 15.7734ZM9.82769 17.0284C9.82769 17.3245 9.7959 17.5777 9.73233 17.7877C9.66875 17.9977 9.58839 18.1734 9.49053 18.3152C9.39266 18.457 9.28301 18.5684 9.16086 18.6498C9.03871 18.7313 8.92084 18.792 8.80691 18.8331C8.70593 18.8706 8.60112 18.8968 8.49439 18.9113C8.40009 18.922 8.33009 18.9277 8.28437 18.9277H6.92214V15.3291H8.00614C8.30902 15.3291 8.57511 15.3773 8.80441 15.473C9.03371 15.5687 9.22444 15.697 9.37588 15.8562C9.52731 16.0155 9.64018 16.1973 9.71518 16.4005C9.79019 16.6041 9.82769 16.8134 9.82769 17.0284ZM8.08936 18.4981C8.48653 18.4981 8.77298 18.3713 8.9487 18.1173C9.12443 17.8634 9.21229 17.4955 9.21229 17.0138C9.21229 16.8641 9.19443 16.7159 9.15872 16.5695C9.12264 16.423 9.05371 16.2905 8.9512 16.1716C8.8487 16.0527 8.7094 15.9566 8.53367 15.8834C8.35795 15.8102 8.13007 15.7734 7.85006 15.7734H7.50825V18.4981H8.08936ZM11.2731 15.7734V16.9063H12.7772V17.3066H11.2731V18.9277H10.6774V15.3291H12.9283V15.7734H11.2731Z"
                        fill="white" />
                </svg>
                <span class="mx-1">{{ this.fileName}}</span>
                <span class="mx-1" (click)="downloadFile()" title="تحميل الملف" >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M0.842105 10.9482C1.06544 10.9482 1.27964 11.037 1.43756 11.1949C1.59549 11.3528 1.68421 11.567 1.68421 11.7903V13.4746C1.68421 13.6979 1.77293 13.9121 1.93086 14.07C2.08878 14.2279 2.30297 14.3167 2.52631 14.3167H12.6316C12.8549 14.3167 13.0691 14.2279 13.227 14.07C13.385 13.9121 13.4737 13.6979 13.4737 13.4746V11.7903C13.4737 11.567 13.5624 11.3528 13.7203 11.1949C13.8782 11.037 14.0924 10.9482 14.3158 10.9482C14.5391 10.9482 14.7533 11.037 14.9112 11.1949C15.0692 11.3528 15.1579 11.567 15.1579 11.7903V13.4746C15.1579 14.1446 14.8917 14.7872 14.4179 15.2609C13.9442 15.7347 13.3016 16.0009 12.6316 16.0009H2.52631C1.85629 16.0009 1.21372 15.7347 0.73994 15.2609C0.266165 14.7872 0 14.1446 0 13.4746V11.7903C0 11.567 0.0887215 11.3528 0.246647 11.1949C0.404572 11.037 0.618765 10.9482 0.842105 10.9482Z"
                            fill="#00205A" />
                        <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M2.77292 6.14283C2.93084 5.98496 3.145 5.89627 3.36829 5.89627C3.59159 5.89627 3.80574 5.98496 3.96366 6.14283L7.57882 9.75798L11.194 6.14283C11.2717 6.0624 11.3646 5.99824 11.4673 5.95411C11.5701 5.90998 11.6806 5.88675 11.7924 5.88577C11.9042 5.8848 12.0151 5.90611 12.1186 5.94845C12.2221 5.99079 12.3161 6.05332 12.3951 6.13239C12.4742 6.21146 12.5367 6.30548 12.5791 6.40897C12.6214 6.51246 12.6427 6.62335 12.6418 6.73516C12.6408 6.84698 12.6176 6.95748 12.5734 7.06022C12.5293 7.16296 12.4651 7.25588 12.3847 7.33356L8.17418 11.5441C8.01627 11.702 7.80211 11.7906 7.57882 11.7906C7.35552 11.7906 7.14137 11.702 6.98345 11.5441L2.77292 7.33356C2.61505 7.17565 2.52637 6.96149 2.52637 6.7382C2.52637 6.5149 2.61505 6.30075 2.77292 6.14283Z"
                            fill="#00205A" />
                        <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M7.57892 0.000976562C7.80226 0.000976563 8.01645 0.089698 8.17438 0.247623C8.3323 0.405549 8.42103 0.619741 8.42103 0.843081V10.9483C8.42103 11.1717 8.3323 11.3859 8.17438 11.5438C8.01645 11.7017 7.80226 11.7904 7.57892 11.7904C7.35558 11.7904 7.14139 11.7017 6.98346 11.5438C6.82554 11.3859 6.73682 11.1717 6.73682 10.9483V0.843081C6.73682 0.619741 6.82554 0.405549 6.98346 0.247623C7.14139 0.089698 7.35558 0.000976563 7.57892 0.000976562Z"
                            fill="#00205A" />
                    </svg>
                </span>
                <span class="mx-1" (click)="clearFile()" title="حذف الملف">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M3.20895 3.21885C3.34279 3.08506 3.52428 3.0099 3.71352 3.0099C3.90276 3.0099 4.08425 3.08506 4.21808 3.21885L7.99554 6.99631L11.773 3.21885C11.8388 3.15069 11.9176 3.09632 12.0046 3.05892C12.0917 3.02151 12.1854 3.00183 12.2801 3.001C12.3749 3.00018 12.4689 3.01824 12.5566 3.05412C12.6443 3.09 12.724 3.143 12.791 3.21001C12.858 3.27701 12.911 3.3567 12.9469 3.4444C12.9827 3.53211 13.0008 3.62609 13 3.72085C12.9991 3.81561 12.9795 3.90926 12.9421 3.99633C12.9047 4.0834 12.8503 4.16215 12.7821 4.22798L9.00467 8.00544L12.7821 11.7829C12.9121 11.9175 12.9841 12.0978 12.9824 12.2849C12.9808 12.472 12.9058 12.651 12.7734 12.7833C12.6411 12.9156 12.4621 12.9907 12.275 12.9923C12.0879 12.994 11.9076 12.922 11.773 12.792L7.99554 9.01457L4.21808 12.792C4.08348 12.922 3.90321 12.994 3.71609 12.9923C3.52896 12.9907 3.34997 12.9156 3.21765 12.7833C3.08533 12.651 3.01027 12.472 3.00864 12.2849C3.00702 12.0978 3.07895 11.9175 3.20895 11.7829L6.98641 8.00544L3.20895 4.22798C3.07516 4.09415 3 3.91266 3 3.72342C3 3.53418 3.07516 3.35268 3.20895 3.21885Z"
                            fill="#4F4F4F" />
                    </svg>
                </span>
            </div>
        </div>
    </div>
</div>
    <!-- Error Display -->
    <p *ngIf="error" class="is-invalid">
        {{ error }}
    </p>