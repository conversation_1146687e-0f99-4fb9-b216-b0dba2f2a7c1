import {
  Component,
  Output,
  EventEmitter,
  Input,
  ViewChild,
  ElementRef,
  OnInit,
  ChangeDetectorRef,
  SimpleChanges,
  Inject,
  Optional,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { AuthService } from 'src/app/features/auth/services/auth-service/auth.service';
import { ControlValueAccessor } from '@angular/forms';
import { TranslateService, TranslateModule } from '@ngx-translate/core';
import { API_BASE_URL, FileManagmentServiceProxy } from '@core/api/api.generated';
import { FileUploadService } from '@shared/services/file.service';
export enum SourceModuleEnum {
  creatFund = 1,
}

@Component({
  selector: 'app-file-upload',
  standalone: true,
  imports: [CommonModule, TranslateModule],
  providers: [FileUploadService],
  templateUrl: './file-upload.component.html',
  styleUrls: ['./file-upload.component.scss'],
})
export class FileUploadComponent implements OnInit, ControlValueAccessor {
  @ViewChild('fileInput') fileInput!: ElementRef;
  @Input() required: boolean = false;
  @Input() formSubmitted: boolean = false;
  @Input() name: string = '';
  @Input() moduleId: SourceModuleEnum | undefined;
  @Input() allowedTypes: string[] = ['pdf'];
  @Input() multiple: boolean = false;

  @Input() maxSize: number = 5;
  selectedFile: File | null = null;
  selectedFiles: File[] = [];
  error: string | null = null;
  @Output() fileUploaded = new EventEmitter<File | File[] | null>();

  file: any;
  fileName: string | null = null;
  isDisabled: boolean = false;
  translatedLabel: string = '';
  errorMessage: string = ''; // ✅ Stores validation errors

  private onChange: (value: File | File[] | null) => void = () => {};
  private onTouched: () => void = () => {};
  @Input() fileUrl: string | undefined;
  fileData: any;
  baseUrl: string;

  constructor(
    private translate: TranslateService,
    private cdr: ChangeDetectorRef,
    private apiClient: FileManagmentServiceProxy,
    private FileUploadService: FileUploadService,
    @Optional() @Inject(API_BASE_URL) baseUrl?: string
  ) {
    this.baseUrl = baseUrl ?? "";
  }

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {
    this.fileName = this.name;

    if (changes['fileUrl'] && changes['fileUrl'].currentValue) {
      this.fileData = changes['fileUrl'].currentValue;
      if(!this.fileName)
      this.buildDownloadUrl(this.fileUrl!);
    }
  }

  writeValue(value: File | File[] | null): void {
    if (Array.isArray(value)) {
      this.selectedFiles = value;
      this.selectedFile = value[0] || null;
    } else {
      this.file = value || null;
      this.selectedFile = value;
      this.selectedFiles = value ? [value] : [];
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.isDisabled = isDisabled;
  }

  /** ✅ Handles file selection */
  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      if (this.multiple) {
        this.handleMultipleFiles(Array.from(input.files));
      } else {
        this.handleFile(input.files[0]);
      }
    } else {
      this.clearAllFiles();
    }
    this.onTouched();
  }

  private handleFile(file: File): void {
    this.error = null;

    if (!this.validateFile(file)) {
      return;
    }

    this.selectedFile = file;
    this.selectedFiles = [file];
    this.onChange(file); // Update form control
    this.getFileUrl();
  }

  private handleMultipleFiles(files: File[]): void {
    this.error = null;
    const validFiles: File[] = [];

    for (const file of files) {
      if (this.validateFile(file)) {
        validFiles.push(file);
      } else {
        // If any file is invalid, stop processing
        return;
      }
    }

    this.selectedFiles = validFiles;
    this.selectedFile = validFiles[0] || null; // Keep first file for backward compatibility
    this.onChange(validFiles); // Update form control with array
    this.getMultipleFileUrls();
  }

  private validateFile(file: File): boolean {
    // Check extension - handle both with and without dots
    const ext = file.name.split('.').pop()?.toLowerCase();
    if (ext) {
      const isAllowed = this.allowedTypes.some(type => {
        const cleanType = type.startsWith('.') ? type.substring(1) : type;
        return cleanType.toLowerCase() === ext;
      });

      if (!isAllowed) {
        this.error = this.translate.instant('FILE_UPLOAD.FILE_NOT_SUPPORTED');
        return false;
      }
    } else {
      this.error = this.translate.instant('FILE_UPLOAD.FILE_NOT_SUPPORTED');
      return false;
    }

    // Check size
    if (file.size > this.maxSize * 1024 * 1024) {
      this.error = this.translate.instant('FILE_UPLOAD.FILE_TOO_LARGE', { maxSize: this.maxSize });
      return false;
    }

    return true;
  }

  getAcceptAttribute(): string {
    return this.allowedTypes.map(type =>
      type.startsWith('.') ? type : `.${type}`
    ).join(',');
  }

  getSupportedFormatsText(): string {
    return this.allowedTypes.map(type =>
      type.startsWith('.') ? type : `.${type}`
    ).join(', ');
  }

  buildDownloadUrl(path: string) {
    const fileName = path.split(/[/\\]/).pop();
    this.fileName = `${fileName}`;
  }

  getFileUrl() {
    if (this.selectedFile)
      this.FileUploadService.uploadFile(
        this.selectedFile,
        this.moduleId || 1
      ).subscribe({
        next: (response: any) => {
          this.fileUploaded.emit(response.data);
          this.fileName = response.data.fileName;
          this.fileUrl = response.data.url;
        },
        error: () => {
          this.fileName = null;
          this.fileUrl = '';
        },
      });
  }

  getMultipleFileUrls() {
    if (this.selectedFiles.length === 0) return;

    if (this.selectedFiles.length === 1) {
      // Handle single file case
      this.getFileUrl();
      return;
    }

    // Handle multiple files - upload sequentially to avoid overwhelming the server
    this.uploadFilesSequentially(this.selectedFiles, 0, []);
  }

  private uploadFilesSequentially(files: File[], index: number, uploadedFiles: any[]): void {
    if (index >= files.length) {
      // All files uploaded successfully

      // this.fileUploaded.emit(uploadedFiles);
      // this.fileName = `${uploadedFiles.length} files uploaded`;
      // this.fileUrl = uploadedFiles[0]?.url || '';

      console.log('All files uploaded successfully:', uploadedFiles);
      return;
    }

    this.FileUploadService.uploadFile(files[index], this.moduleId || 1).subscribe({
      next: (response: any) => {
        uploadedFiles.push(response.data);
        this.uploadFilesSequentially(files, index + 1, uploadedFiles);
      },
      error: () => {
        this.fileName = null;
        this.fileUrl = '';
        this.error = this.translate.instant('FILE_UPLOAD.UPLOAD_FAILED');
      }
    });
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
  }

  /** ✅ Handles file drop */
  onDrop(event: DragEvent): void {
    if(this.selectedFile || this.fileUrl) return;
    event.preventDefault();
    if (event.dataTransfer?.files.length) {
      if (this.multiple) {
        this.handleMultipleFiles(Array.from(event.dataTransfer.files));
      } else {
        this.handleFile(event.dataTransfer.files[0]);
      }
    } else {
      this.clearAllFiles();
    }
    this.onTouched();
  }

  clearFile(): void {
    this.selectedFile = null;
    this.selectedFiles = [];
    this.fileUrl = '';
    this.fileName = null;
    this.onChange(null); // Update form control
    this.fileUploaded.emit(null);
  }

  clearAllFiles(): void {
    this.selectedFile = null;
    this.selectedFiles = [];
    this.file = null;
    this.fileName = null;
    this.fileUrl = '';
    this.error = null;
    this.onChange(null); // Update form control
  }

  hasUploadedFiles(): boolean {
    return !!(this.selectedFile || this.selectedFiles.length > 0 || this.fileUrl);
  }

  removeFile(index: number): void {
    if (this.multiple && this.selectedFiles.length > 0) {
      this.selectedFiles.splice(index, 1);
      this.selectedFile = this.selectedFiles[0] || null;
      this.onChange(this.selectedFiles.length > 0 ? this.selectedFiles : null);

      if (this.selectedFiles.length === 0) {
        this.clearAllFiles();
      }
    } else {
      this.clearFile();
    }
  }

  getFileSize(file: File): string {
    const bytes = file.size;
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }

  downloadFile(fileUrl?: string, fileName?: string): void {
    if (this.selectedFile && !fileUrl) {
      const url = URL.createObjectURL(this.selectedFile);
      const a = document.createElement('a');
      a.href = url;
      a.download = this.selectedFile.name;
      a.click();
      URL.revokeObjectURL(url);
    } else if (fileUrl || this.fileUrl) {
      const fullUrl = fileUrl ? `${this.baseUrl}${fileUrl}` : `${this.baseUrl}${this.fileUrl}`;
      const link = document.createElement('a');
      link.href = fullUrl;
      link.target = '_blank';
      link.download = fileName || this.fileName || '';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }
}
