import {
  Component,
  Output,
  EventEmitter,
  Input,
  ViewChild,
  ElementRef,
  OnInit,
  ChangeDetectorRef,
  SimpleChanges,
  Inject,
  Optional,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { AuthService } from 'src/app/features/auth/services/auth-service/auth.service';
import { ControlValueAccessor } from '@angular/forms';
import { TranslateService, TranslateModule } from '@ngx-translate/core';
import { API_BASE_URL, FileManagmentServiceProxy } from '@core/api/api.generated';
import { FileUploadService } from '@shared/services/file.service';
export enum SourceModuleEnum {
  creatFund = 1,
}

@Component({
  selector: 'app-file-upload',
  standalone: true,
  imports: [CommonModule, TranslateModule],
  providers: [FileUploadService],
  templateUrl: './file-upload.component.html',
  styleUrls: ['./file-upload.component.scss'],
})
export class FileUploadComponent implements OnInit, ControlValueAccessor {
  @ViewChild('fileInput') fileInput!: ElementRef;
  @Input() required: boolean = false;
  @Input() formSubmitted: boolean = false;
  @Input() name: string = '';
  @Input() moduleId: SourceModuleEnum | undefined;
  @Input() allowedTypes: string[] = ['pdf'];
  @Input() multiple: boolean = false;

  @Input() maxSize: number = 5;
  selectedFile: File | null = null;
  error: string | null = null;
  @Output() fileUploaded = new EventEmitter<File | null>();

  file: any;
  fileName: string | null = null;
  isDisabled: boolean = false;
  translatedLabel: string = '';
  errorMessage: string = ''; // ✅ Stores validation errors

  private onChange: (value: File | null) => void = () => {};
  private onTouched: () => void = () => {};
  @Input() fileUrl: string | undefined;
  fileData: any;
  baseUrl: string;

  constructor(
    private translate: TranslateService,
    private cdr: ChangeDetectorRef,
    private apiClient: FileManagmentServiceProxy,
    private FileUploadService: FileUploadService,
    @Optional() @Inject(API_BASE_URL) baseUrl?: string
  ) {
    this.baseUrl = baseUrl ?? "";
  }

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {
    this.fileName = this.name;

    if (changes['fileUrl'] && changes['fileUrl'].currentValue) {
      this.fileData = changes['fileUrl'].currentValue;
      if(!this.fileName)
      this.buildDownloadUrl(this.fileUrl!);
    }
  }

  writeValue(file: File | null): void {
    this.file = file || null;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.isDisabled = isDisabled;
  }

  /** ✅ Handles file selection */
  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.handleFile(input.files[0]);
    } else {
      this.file = null;
      this.fileName = null;
    }
    this.onTouched();
  }

  private handleFile(file: File): void {
    this.error = null;

    // Check extension - handle both with and without dots
    const ext = file.name.split('.').pop()?.toLowerCase();
    if (ext) {
      const isAllowed = this.allowedTypes.some(type => {
        const cleanType = type.startsWith('.') ? type.substring(1) : type;
        return cleanType.toLowerCase() === ext;
      });

      if (!isAllowed) {
        this.error = this.translate.instant('FILE_UPLOAD.FILE_NOT_SUPPORTED');
        return;
      }
    } else {
      this.error = this.translate.instant('FILE_UPLOAD.FILE_NOT_SUPPORTED');
      return;
    }

    // Check size
    if (file.size > this.maxSize * 1024 * 1024) {
      this.error = this.translate.instant('FILE_UPLOAD.FILE_TOO_LARGE', { maxSize: this.maxSize });
      return;
    }

    this.selectedFile = file;
    this.getFileUrl();
  }

  getAcceptAttribute(): string {
    return this.allowedTypes.map(type =>
      type.startsWith('.') ? type : `.${type}`
    ).join(',');
  }

  getSupportedFormatsText(): string {
    return this.allowedTypes.map(type =>
      type.startsWith('.') ? type : `.${type}`
    ).join(', ');
  }

  buildDownloadUrl(path: string) {
    const fileName = path.split(/[/\\]/).pop();
    this.fileName = `${fileName}`;
  }

  getFileUrl() {
    if (this.selectedFile)
      this.FileUploadService.uploadFile(
        this.selectedFile,
        this.moduleId || 1
      ).subscribe({
        next: (response: any) => {
          this.fileUploaded.emit(response.data);
          this.fileName = response.data.fileName;
          this.fileUrl = response.data.url;
        },
        error: () => {
          this.fileName = null;
          this.fileUrl = '';
        },
      });
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
  }

  /** ✅ Handles file drop */
  onDrop(event: DragEvent): void {
    if(this.selectedFile || this.fileUrl)return;
    event.preventDefault();
    if (event.dataTransfer?.files.length) {
      this.handleFile(event.dataTransfer.files[0]);
    } else {
      this.file = null;
      this.fileName = null;
    }
    this.onTouched();
  }

  clearFile(): void {
    this.selectedFile = null;
    this.fileUrl='';
    this.fileUploaded.emit(null);
  }

  downloadFile(): void {
    debugger;

    if (this.selectedFile) {
      const url = URL.createObjectURL(this.selectedFile);
      const a = document.createElement('a');
      a.href = url;
      a.download = this.selectedFile.name;
      a.click();
      URL.revokeObjectURL(url);
    } else if (this.fileUrl) {
      const fullUrl = `${this.baseUrl}${this.fileUrl}`;
      const link = document.createElement('a');
      link.href = fullUrl;
      link.target = '_blank';
      link.download = this.fileName ?? ''; // Let the browser handle the name, or specify: 'filename.pdf'
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }
}
