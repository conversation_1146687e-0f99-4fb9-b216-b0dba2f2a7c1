<div *ngIf="formGroup">
  <form [formGroup]="formGroup" #f="ngForm" novalidate name="form" class="form">
    <div class="row">
      <ng-content select="[slot='top']"></ng-content>

      <ng-container *ngFor="let control of formControls;">

        <ng-content select="[slot='between']"></ng-content>
        <ng-content select="[slot='bottom']"></ng-content>

        <div class="form-group mb-24" [ngClass]="control.class" *ngIf="control.type != inputType.empty && isControlVisible(control)">
          <label class="label">
            {{ control.label |translate}}
            <!-- <span *ngIf="control.isRequired" class="danger-color"> *</span> -->
            <span *ngIf="control.isRequired == false" class=" fs-14 text-grey"> {{'FORM.optional' |translate}} </span>
          </label>
          <!--************ "text" **********-->
          <ng-container *ngIf="control.type == inputType.Text">
            <arabdt-reusable-rf-text-input [disabled]="control.isReadonly || false" [appearance]="appearance.Outline"
              [controlSize]="controlSize.Medium" [autoFocus]="control.autoFocus || false"
              [readonly]="control.isReadonly || false" [formControlName]="control.formControlName"
              [placeholder]="control.placeholder" [id]="control.id" (focusEvent)="onControlFocus($event, control)"
              (blurEvent)="onControlBlur($event, control)" (keyDownEvent)="onKeyPressed($event, control)"
              (inputChangeEvent)="onValueChange($event, control)"
              [isInvalid]="isFormSubmitted && !!getFormGroup[control.formControlName].errors"></arabdt-reusable-rf-text-input>
          </ng-container>

          <!--************ "textarea" **********-->
          <ng-container *ngIf="control.type ==inputType.Textarea">
            <textarea class="textarea form-control" [id]="control.id" [name]="control.name" [readonly]="control.isReadonly"
              [required]="control.isRequired || false" [formControlName]="control.formControlName"
              placeholder="" type="text" [ngClass]="{
                'is-invalid':
                  isFormSubmitted && getFormGroup[control.formControlName].errors
              }" [maxLength]="control.maxLength" (blur)="onControlBlur($event, control)" rows="10"></textarea>
          </ng-container>
          <!--************ "number" **********-->
          <ng-container *ngIf="control.type == inputType.Number">
            <arabdt-reusable-rf-text-input [appearance]="appearance.Outline" [controlSize]="controlSize.Medium"
              [autoFocus]="control.autoFocus || false" [readonly]="control.isReadonly || false"
              [formControlName]="control.formControlName" [placeholder]="control.placeholder" [id]="control.id"
              (focusEvent)="onControlFocus($event, control)" (blurEvent)="onControlBlur($event, control)"
              (keyDownEvent)="onKeyPressed($event, control)" (inputChangeEvent)="onValueChange($event, control)"
              [isInvalid]="isFormSubmitted && !!getFormGroup[control.formControlName].errors" type="number"[step]="control.step"
              [minLength]="control.minLength" [maxLength]="control.maxLength"></arabdt-reusable-rf-text-input>
          </ng-container>
          <!--************ "number decimal" **********-->
          <ng-container *ngIf="control.type == inputType.NumberDecimal">
            <arabdt-reusable-rf-text-input [appearance]="appearance.Outline" [controlSize]="controlSize.Medium"
              [autoFocus]="control.autoFocus || false" [readonly]="control.isReadonly || false"
              [formControlName]="control.formControlName" [placeholder]="control.placeholder" [id]="control.id"
              (focusEvent)="onControlFocus($event, control)" (blurEvent)="onControlBlur($event, control)"
              (keyDownEvent)="onKeyPressed($event, control)" (inputChangeEvent)="onValueChange($event, control)"
              [isInvalid]="isFormSubmitted && !!getFormGroup[control.formControlName].errors" type="number"
              [minLength]="control.minLength" [maxLength]="control.maxLength"></arabdt-reusable-rf-text-input>
          </ng-container>

          <!--************ "email" **********-->
          <ng-container *ngIf="control.type == inputType.Email">
            <arabdt-reusable-rf-text-input [appearance]="appearance.Outline" [controlSize]="controlSize.Medium"
              [autoFocus]="control.autoFocus || false" [readonly]="control.isReadonly || false"
              [formControlName]="control.formControlName" [placeholder]="control.placeholder" [id]="control.id"
              (focusEvent)="onControlFocus($event, control)" (blurEvent)="onControlBlur($event, control)"
              (keyDownEvent)="onKeyPressed($event, control)" (inputChangeEvent)="onValueChange($event, control)"
              [isInvalid]="isFormSubmitted && !!getFormGroup[control.formControlName].errors" type="email"
              [minLength]="control.minLength" [maxLength]="control.maxLength"></arabdt-reusable-rf-text-input>
          </ng-container>
          <!--************ "date" **********-->
          <ng-container *ngIf="control.type == inputType.Date">

            <arabdt-datepicker
            [disabled]="control.isReadonly || false"
            [calendarMode]="control.calendarMode || CalendarModeEnum.BOTH"
              [placeholder]="control.placeholder || ''  |translate " [readonly]="control.isReadonly || false" [ngClass]="{
                'is-invalid':
                  isFormSubmitted && getFormGroup[control.formControlName].errors
              }" [minGreg]="control.minGreg" [maxGreg]="control.maxGreg" [minHijri]="control.minHijri"
              [formControlName]="control.formControlName" [maxHijri]="control.maxHijri"
              (onDateChange)="onDateSelected($event, control)"></arabdt-datepicker>
          </ng-container>

          <!--************ "radio" **********-->
          <ng-container *ngIf="control.type == inputType.Radio">

            <!-- <div class="form-group"> -->
            <mat-radio-group [formControlName]="control.formControlName" class="d-flex align-items-center gap-4 mt-2"
              [ngClass]="{
                  'is-invalid':
                    isFormSubmitted && getFormGroup[control.formControlName].errors
                }" (change)="onRadioButtonChange($event, control)">
              <div *ngFor="let option of control.options">
                <mat-radio-button [value]="option.id" [id]="option.id" class="ml-4px">
                  {{ option.name |translate }}
                </mat-radio-button>
              </div>
            </mat-radio-group>
            <!-- </div> -->
          </ng-container>

          <!--************ "checkbox" **********-->
          <ng-container *ngIf="control.type == inputType.Checkbox">

            <div class="form-group w-100 mt-18">
              <div *ngFor="let option of control.options" class="field-checkbox mb-8">
                <mat-checkbox [formControlName]="control.formControlName" [value]="option.id" [ngClass]="{
                    'is-invalid':
                      isFormSubmitted &&
                      getFormGroup[control.formControlName].errors
                  }" (change)="onCheckboxChange($event, control)" [id]="option.id">
                  {{ option.name |translate }}
                </mat-checkbox>
              </div>
            </div>
          </ng-container>

          <!--************ "dropdown" **********-->
          <ng-container *ngIf="control.type == inputType.Dropdown">
            <!-- Dropdown Input -->
            <ng-select [items]="control.options || []" bindLabel="name" bindValue="id"   appendTo="body"

              [maxSelectedItems]="control.maxLength" [formControlName]="control.formControlName"
              [placeholder]="control.placeholder || '' | translate" [multiple]="control.multiple" [clearable]="true"
              [searchable]="true" [disabled]="control.isReadonly || false" [ngClass]="{
    'is-invalid': isFormSubmitted && getFormGroup[control.formControlName].errors,
    'multiple': control.multiple
  }" class="custom-ng-select" (change)="onDropdownChange($event, control)">
            </ng-select>

            <!-- Selected Items Below -->
            <div class="selected-tags-container d-flex gap-2 mt-1 flex-wrap-wrap "
              *ngIf="getFormGroup[control.formControlName].value?.length && control.multiple">
              <div class="tag border-rounded " *ngFor="let selectedId of getFormGroup[control.formControlName].value">
                {{ getSelectedName(control.options ||[], selectedId) }}
                <span class="remove-icon mx-1" (click)="removeSelectedItem(control, selectedId)">
                  <img src="assets/images/x.svg" alt="remove" />
                </span>
              </div>
            </div>


          </ng-container>

          <!--************ "file" **********-->
          <ng-container *ngIf="control.type == inputType.file">
            <app-file-upload [fileUrl]="control.fileUrl" [name]="control.fileName  ?? ''" [allowedTypes]="control.allowedTypes || []"
              (fileUploaded)="handleFileUpload($event,control)" [maxSize]="control.max ||10"></app-file-upload>

          </ng-container>

          <ng-content select="[slot='last']"></ng-content>


          <ng-container *ngIf="control.type === inputType.Custom">
            <ng-container *ngTemplateOutlet="customTemplate"></ng-container>

          </ng-container>

          <app-validation-messages [formSubmitted]="isFormSubmitted || false"
            [control]="getFormGroup[control.formControlName]">
          </app-validation-messages>

        </div>
        <div *ngIf="control.type==inputType.empty" [ngClass]="control.class">
          <div class="w-100"></div>
        </div>
      </ng-container>
    </div>
  </form>
</div>
