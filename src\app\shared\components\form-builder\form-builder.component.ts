import {
  Component,
  EventEmitter,
  Input,
  Output,
  NO_ERRORS_SCHEMA,
  TemplateRef,
} from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NgSelectModule } from '@ng-select/ng-select';
import { MatRadioModule } from '@angular/material/radio';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatExpansionModule } from '@angular/material/expansion';
import { SizeEnum } from '@core/enums/size';
import { AppearanceEnum } from '@shared/enum/appearance-enum';
import { LabelPositionEnum } from '@shared/enum/label-position-enum';
import { DatepickerComponent } from '../datepicker/datepicker.component';
import { ReusableRfTextInputComponent } from '../reusable-rf-text-input/reusable-rf-text-input.component';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { InputType } from '@shared/enum/input-type.enum';
import { TranslateModule } from '@ngx-translate/core';
import { CalendarModeEnum } from '@shared/enum/calender-mode';
import { FileUploadComponent } from '../file-upload/file-upload.component';
import { ValidationMessagesComponent } from '../validation/validation-messages.component';

@Component({
  selector: 'app-form-builder',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ReusableRfTextInputComponent,
    NgSelectModule,
    MatRadioModule,
    MatCheckboxModule,
    MatSlideToggleModule,
    MatExpansionModule,
    DatepickerComponent,
    TranslateModule,
    FileUploadComponent,
    ValidationMessagesComponent,
  ],
  schemas: [NO_ERRORS_SCHEMA],
  templateUrl: './form-builder.component.html',
  styleUrls: ['./form-builder.component.scss'],
})
export class FormBuilderComponent {
  CalendarModeEnum = CalendarModeEnum;
  @Output() radioButtonValueChanged = new EventEmitter<{
    event: Event;
    control: IControlOption;
  }>();
  @Output() onBlur = new EventEmitter<{
    event: Event;
    control: IControlOption;
  }>();
  @Input() formControls: IControlOption[] | undefined;
  @Input() formGroup: FormGroup | undefined;
  @Input() isFormSubmitted: boolean | undefined;
  @Input() customTemplate!: TemplateRef<any>;

  // Password visibility state
  showPassword = false;

  @Output() controlFocused = new EventEmitter<{
    event: any;
    control: IControlOption;
  }>();
  @Output() valueChanged = new EventEmitter<{
    event: any;
    control: IControlOption;
  }>();
  @Output() keyPressed = new EventEmitter<{
    event: any;
    control: IControlOption;
  }>();
  @Output() checkboxChanged = new EventEmitter<{
    event: any;
    control: IControlOption;
  }>();
  @Output() switchChanged = new EventEmitter<{
    event: any;
    control: IControlOption;
  }>();
  @Output() dateSelected = new EventEmitter<{
    event: any;
    control: IControlOption;
  }>();
  @Output() dropdownChanged = new EventEmitter<{
    event: any;
    control: IControlOption;
  }>();
  @Output() removeSelection = new EventEmitter<{
    idToRemove: any;
    control: IControlOption;
  }>();
  @Output() fileUploaded = new EventEmitter<{
    file: File | File[] | null;
    control: IControlOption;
  }>();

  inputType = InputType;
  appearance = AppearanceEnum;
  labelPosition = LabelPositionEnum;
  controlSize = SizeEnum;

  constructor() {}

  get getFormGroup() {
    return this.formGroup?.controls || {};
  }

  onControlBlur(event: any, control: IControlOption) {
    this.onBlur.emit({ event: event, control: control });
  }

  onRadioButtonChange(event: any, control: IControlOption) {
    this.radioButtonValueChanged.emit({ event: event, control: control });
  }

  onControlFocus(event: any, control: IControlOption): void {
    console.log('Focus Event:', event, control);
    this.controlFocused.emit({ event, control });
  }

  onValueChange(event: any, control: IControlOption): void {
    console.log('Value Change:', event, control);
    this.valueChanged.emit({ event, control });
  }

  onKeyPressed(event: any, control: IControlOption): void {
    console.log('Key Pressed:', event.key, control);

    // Restrict input to numbers only for Number input type
    if (control.type === InputType.Text && control?.actAsNumber) {
      const allowedKeys = ['Backspace', 'ArrowLeft', 'ArrowRight', 'Tab', 'Delete', 'Home', 'End', 'Enter', '/'];
      const isNumber = /^[0-9]$/.test(event.key);

      if (!isNumber && !allowedKeys.includes(event.key)) {
        event.preventDefault();
        return;
      }
    }

    this.keyPressed.emit({ event, control });
  }

  onTelKeyPressed(event: any, control: IControlOption): void {
    // Allow only numbers, backspace, delete, tab, escape, enter, and arrow keys
    const allowedKeys = [
      'Backspace', 'Delete', 'Tab', 'Escape', 'Enter',
      'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'
    ];

    if (allowedKeys.includes(event.key)) {
      this.keyPressed.emit({ event, control });
      return;
    }

    // Allow only digits 0-9
    if (!/^[0-9]$/.test(event.key)) {
      event.preventDefault();
      return;
    }

    // Prevent input if already at max length (9 digits)
    const currentValue = event.target.value || '';
    if (currentValue.length >= 9) {
      event.preventDefault();
      return;
    }

    this.keyPressed.emit({ event, control });
  }

  onTelValueChange(event: any, control: IControlOption): void {
    let value = event.target?.value || event || '';

    // Remove any non-digit characters
    value = value.replace(/\D/g, '');

    // Ensure it starts with 5 (Saudi mobile numbers start with 5)
    if (value.length > 0 && !value.startsWith('5')) {
      // If user types a different first digit, replace it with 5
      value = '5' + value.substring(1);
    }

    // Limit to 9 digits
    if (value.length > 9) {
      value = value.substring(0, 9);
    }

    // Update the form control value
    if (this.formGroup) {
      this.formGroup.get(control.formControlName)?.setValue(value, { emitEvent: false });
    }

    console.log('Tel Value Change:', value, control);
    this.valueChanged.emit({ event: value, control });
  }

  onCheckboxChange(event: any, control: IControlOption): void {
    console.log('Checkbox Change:', event, control);
    this.checkboxChanged.emit({ event, control });
  }

  onSwitchChange(event: any, control: IControlOption): void {
    console.log('Switch Change:', event, control);
    this.switchChanged.emit({ event, control });
  }

  onDateSelected(event: any, control: IControlOption): void {
    console.log('Date Selected:', event, control);
    this.dateSelected.emit({ event, control });
  }

  onDropdownChange(event: any, control: IControlOption): void {
    console.log('Dropdown Change:', event, control);
    debugger
    this.dropdownChanged.emit({ event, control });
  }

  trackByFormControl(index: number, control: IControlOption): string {
    return control.formControlName;
  }

  handleFileUpload(file: File | File[] | null, control: IControlOption): void {
    console.log('Uploaded file(s):', file);
    this.fileUploaded.emit({ file, control });
  }

  getSelectedName(options: any[], id: any): string {
    const selected = options?.find((opt) => opt.id === id);
    return selected?.name || '';
  }

  removeSelectedItem(control: any, idToRemove: any) {
    this.removeSelection.emit({ control, idToRemove });
  }

  isControlVisible(control: IControlOption): boolean {
    if (control.isVisible === undefined) {
      return true; // Default to visible if not specified
    }

    if (typeof control.isVisible === 'boolean') {
      return control.isVisible;
    }

    if (typeof control.isVisible === 'function') {
      return control.isVisible();
    }

    return true;
  }
  isControlOptional(control: IControlOption): boolean {
    if (control.showOptional === undefined) {
      return true; // Default to visible if not specified
    }

    if (typeof control.showOptional === 'boolean') {
      return control.showOptional;
    }

    if (typeof control.showOptional === 'function') {
      return control.showOptional();
    }

    return true;
  }
  getStatusBadgeClass(control: IControlOption): string {
    const value = this.getFormGroup[control.formControlName]?.value;

    if (!value) {
      return 'status-default';
    }

    // Extract status from the control's custom properties or use a default mapping
    const statusMapping = control.options || [];
    const statusOption = statusMapping.find(option => option.name === value);

    if (statusOption && statusOption.id) {
      return `status-${statusOption.id}`;
    }

    // Enhanced status mapping for resolution statuses
    if (typeof value === 'string') {
      // Handle Arabic and English status patterns
      const statusMap: { [key: string]: string } = {
        // English statuses
        'Draft': 'status-1',
        'Pending': 'status-2',
        'Pending Approval': 'status-2',
        'Approved': 'status-3',
        'Waiting for Confirmation': 'status-4',
        'Confirmed': 'status-5',
        'Not Approved': 'status-6',
        'Rejected': 'status-7',
        'Voting in Progress': 'status-8',
        'Completing Data': 'status-9',
        'Cancelled': 'status-4',
        // Arabic statuses (if needed)
        'مسودة': 'status-1',
        'في انتظار الموافقة': 'status-2',
        'موافق عليه': 'status-3',
        'في انتظار التأكيد': 'status-4',
        'مؤكد': 'status-5',
        'غير موافق عليه': 'status-6',
        'مرفوض': 'status-7',
        'التصويت قيد التقدم': 'status-8',
        'استكمال البيانات': 'status-9'
      };

      return statusMap[value] || 'status-default';
    }

    return 'status-default';
  }

  // Password visibility toggle
  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  // Get password strength class for styling
  getPasswordStrengthClass(control: IControlOption): string {
    const strength = this.calculatePasswordStrength(control);
    switch (strength) {
      case 'weak': return 'strength-weak';
      case 'medium': return 'strength-medium';
      case 'strong': return 'strength-strong';
      default: return 'strength-none';
    }
  }

  // Get password strength text
  getPasswordStrengthText(control: IControlOption): string {
    const strength = this.calculatePasswordStrength(control);
    switch (strength) {
      case 'weak': return 'FORM.PASSWORD_STRENGTH_WEAK';
      case 'medium': return 'FORM.PASSWORD_STRENGTH_MEDIUM';
      case 'strong': return 'FORM.PASSWORD_STRENGTH_STRONG';
      default: return '';
    }
  }

  // Calculate password strength
  private calculatePasswordStrength(control: IControlOption): 'weak' | 'medium' | 'strong' | 'none' {
    if (!this.formGroup) return 'none';

    const value = this.formGroup.get(control.formControlName)?.value;
    if (!value) return 'none';

    let score = 0;

    // Length check
    if (value.length >= 8) score++;
    if (value.length >= 12) score++;

    // Character type checks
    if (/[a-z]/.test(value)) score++;
    if (/[A-Z]/.test(value)) score++;
    if (/[0-9]/.test(value)) score++;
    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(value)) score++;

    if (score <= 2) return 'weak';
    if (score <= 4) return 'medium';
    return 'strong';
  }
  allowOnlyLetters(event: KeyboardEvent): void {
    const char = event.key;
    const regex = /^[a-zA-Z\u0600-\u06FF\s]$/;
    if (!regex.test(char)) {
      event.preventDefault();
    }
  }
  
}
