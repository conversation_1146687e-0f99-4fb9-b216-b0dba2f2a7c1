<div class="fund-history-container" >
  <div class="tabs">

    <button class="tab-button" [class.active]="activeTab === 'history'"  (click)="setActiveTab('history')" >
      <img src="assets/images/calendar.png" alt="Fund Details" />

      سجل التنبيهات
    </button>
    <button class="tab-button" [class.active]="activeTab === 'details'"   (click)="setActiveTab('details')">
      <img src="assets/images/history.png" alt="Fund History" />
      سجل الحركات
     </button>
  </div>

  <div class="timeline-container">
    <div *ngIf="activeTab === 'history' && filteredItems.length ===0" >
      {{ 'FUND_DETAILS.NO_RECORDS' | translate }}
    </div>
    <div class="timeline-item" *ngFor="let item of filteredItems; let last = last">
      <div
        class="timeline-icon"
        [ngStyle]="{ backgroundColor: item.type === 'fundNotifications' ? '#27AE6029' : '#FEEDE8' }"
      >
        <img [src]="getIconForType(item.type)" [alt]="item.type" />
      </div>

      <div class="timeline-content" [ngClass]="{ active: item.status === 'active' }">
        <div class="d-flex justify-content-between">
          <div>

          <h3 class="title">{{ item.title  }}</h3>
          <p class="description">{{ item.description }}</p>
        </div>
        <p  [ngClass]="getStatusClass(item.statusId)" *ngIf="item.type !== 'fundNotifications'" class="status">
        <span class="circle"></span>
          {{ item.status }}</p>
       </div>
        <div class="timestamp">
          <span class="time"> {{period}}   </span>
          <span class="time mx-1">
               {{time}} {{ formatTimetoOtherSide(item.time) }}
          </span>
          <span class="date">
           {{item.createdAt | dateHijriConverter }}

           <span class="mx-2">
            {{ item.date }}
           </span>
          </span>

        </div>
      </div>
      <hr *ngIf="!last" />

    </div>

  </div>

</div>
