<div class="page-header">
    <div class="container-fluid px-0">
        <div class="row align-items-center">
            <div class="col-12">
                <h2 [ngStyle]="{'margin-bottom' :showSearch ? '0px':'26px'}" class="header">{{ title | translate }}</h2>
            </div>
            <div class="col-md-4">
              <div class="search-filter mt-3">
                <mat-form-field appearance="outline" class="search-input" *ngIf="showSearch"  title="">
                  <input
                    matInput  title=""
                    [placeholder]="searchPlaceholder | translate"
                    [(ngModel)]="searchValue"
                    (keydown.enter)="onSearch()" />
                  <span matPrefix class="search-icon">
                    <img src="assets/images/search.png" alt="" />
                  </span>
                </mat-form-field>

                <div *ngIf="showFilter" (click)="onFilterClick()" [title]="'COMMON.FILTER' | translate">
                  <img src="assets/images/filter.png" alt="" /> <!-- Disables img tooltip -->
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <ng-content select="[slot='between']"></ng-content>
            </div>
            <div class="col-md-2 create-button-container">
                <app-custom-button class="header-button" [disabled]="exceedMaxNumber" *ngIf="showCreateButton" [btnName]="createButtonText | translate"
                    [iconName]="createButtonIcon" (click)="onCreateClick()"></app-custom-button>
            </div>
        </div>
    </div>

</div>
