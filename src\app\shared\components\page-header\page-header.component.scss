@import "../../../../assets/scss/variables";

.page-header {
    .header {
        color: $navy-blue;
        font-size: 24px;
        font-weight: 600;
        margin: 0;

    }

    .create-button-container {
        display: flex;
        justify-content: flex-end;

        @media (max-width: 767px) {
            margin-top: 24px;
            justify-content: flex-start;
        }
    }

    .search-filter {
        display: flex;
        gap: 10px;
        align-items: center;

        .search-input {
            width: 344px;
            height: 40px;
            margin: 0;

            .search-icon {
                display: flex;
                align-items: center;
                margin-right: 12px;
                margin-left: 12px;

                img {
                    width: 20px;
                    height: 20px;
                    opacity: 0.5;
                    transition: opacity 0.2s ease;

                    &:hover {
                        opacity: 0.7;
                    }
                }
            }

            ::ng-deep {
                .mat-mdc-form-field-flex {
                    background-color: #FFFFFF;
                    height: 40px;
                    border-radius: 10px;
                }

                .mat-mdc-form-field-outline {
                    border-radius: 10px;
                    border-color: #D1D1D1;
                }

                .mat-mdc-text-field-wrapper {
                    padding: 0;
                    background-color: transparent;
                }

                .mat-mdc-form-field-infix {
                    padding: 0;
                    border: none;
                }

                .mat-mdc-form-field-subscript-wrapper {
                    display: none;
                }

                input.mat-mdc-input-element {
                    height: 40px;
                    line-height: 40px;
                    padding: 0;
                }

                .mdc-notched-outline__leading,
                .mdc-notched-outline__notch,
                .mdc-notched-outline__trailing {
                    border-color: #D1D1D1 !important;
                    border-width: 1px !important;
                }

                .mat-mdc-form-field-focus-overlay {
                    opacity: 0;
                }
            }
        }

        .filter-btn {
            height: 40px;
            width: 40px;
            border: 1px solid #D1D1D1;
            color: var(--text-grey);
            border-radius: 10px;
            padding: 0;
            min-width: unset;
            line-height: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            background-color: #FFFFFF;

            img {
                width: 20px;
                height: 20px;
                opacity: 0.5;
                transition: opacity 0.2s ease;
            }

            &:hover {
                background-color: rgba(0, 0, 0, 0.04);
                border-color: #D1D1D1;

                img {
                    opacity: 0.7;
                }
            }

            &:active {
                background-color: rgba(0, 0, 0, 0.08);
            }
        }
    }
}
.mat-form-field .mat-input-element::placeholder {
  opacity: 0 !important;
}

.search-icon img:hover {
  pointer-events: none;
}
