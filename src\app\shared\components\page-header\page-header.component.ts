import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CustomButtonComponent } from '../custom-button/custom-button.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { FormsModule } from '@angular/forms';
import { IconEnum } from '@core/enums/icon-enum';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-page-header',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    CustomButtonComponent,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatButtonModule,
    FormsModule,
    MatTooltipModule 
  ],
  templateUrl: './page-header.component.html',
  styleUrls: ['./page-header.component.scss']
})
export class PageHeaderComponent {
  @Input() title: string = '';
  @Input() subHeaderValue: string = '';
  @Input() showCreateButton: boolean = false;
  @Input() showSearch: boolean = false;
  @Input() showFilter: boolean = false;
  @Input() showSubHeader: boolean = false;
  @Input() createButtonText: string = '';
  @Input() searchPlaceholder: string = '';
  @Input() createButtonIcon = IconEnum.plus;
  @Input() createButtonDisabled: boolean = false;
  @Input() exceedMaxNumber: boolean = false;


  @Output() create = new EventEmitter<void>();
  @Output() search = new EventEmitter<string>();
  @Output() filter = new EventEmitter<void>();

  searchValue: string = '';

  onCreateClick() {
    this.create.emit();
  }

  onSearch() {
    this.search.emit(this.searchValue);
  }

  restrictToNumbers(event: KeyboardEvent) {
    const allowedKeys = ['Backspace', 'ArrowLeft', 'ArrowRight', 'Tab', 'Delete', 'Home', 'End', 'Enter', '/'];
    const isNumber = /^[0-9]$/.test(event.key);

    if (!isNumber && !allowedKeys.includes(event.key)) {
      event.preventDefault();
    }
  }
  onFilterClick(){
    this.filter.emit();
  }
}
