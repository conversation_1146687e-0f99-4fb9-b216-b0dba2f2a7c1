::ng-deep.mat-mdc-table thead{
background-color: #EBF3FC !important;

::ng-deep.mat-sort-header-content{
  font-weight: 700;
  color: #4F4F4F;
  font-size: 14px;
}

}

::ng-deep.mdc-data-table__cell {
  color:  #333;
font-size: 14px;
font-weight: 500;
}

  ::ng-deep .mat-mdc-table thead tr th.mat-mdc-header-cell:nth-child(n+3) .mat-sort-header-arrow {
    opacity: 0.54 !important;
    transform: translateY(0%) !important;
  }

.flex-buttons{
  display: flex;
  gap: 8px;
  align-items: center;

  .action-button {
    background-color: transparent;
    border: 1px solid transparent;
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: #f5f5f5;
      border-color: #e0e0e0;
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(15, 108, 189, 0.2);
    }

    span {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

// Action menu styles
.action-menu-item {
  width: 100%;

  .action-menu-button {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: transparent;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f5f5f5;
    }

    .action-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }

    .action-label {
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }
  }
}
::ng-deep.mat-sort-header-arrow{
color: #0F6CBD !important;
}

table td  tr:nth-child(odd):nth-child(n+1) {
  background-color: #FFFFFF;
}

table  tr:nth-child(even):nth-child(n+1) {
  background-color: #FAFAFA;
}

table {
  box-shadow: none;
}

