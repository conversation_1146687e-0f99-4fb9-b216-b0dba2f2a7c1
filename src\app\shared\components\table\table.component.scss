::ng-deep.mat-mdc-table thead{
background-color: #EBF3FC !important;

::ng-deep.mat-sort-header-content{
  font-weight: 700;
  color: #4F4F4F;
  font-size: 14px;
}

}

::ng-deep.mdc-data-table__cell {
  color:  #333;
font-size: 14px;
font-weight: 500;
}

  ::ng-deep .mat-mdc-table thead tr th.mat-mdc-header-cell:nth-child(n+3) .mat-sort-header-arrow {
    opacity: 0.54 !important;
    transform: translateY(0%) !important;
  }

.flex-buttons{
  button{
    background-color: transparent;
    border: 1px solid transparent;
  }
}
::ng-deep.mat-sort-header-arrow{
color: #0F6CBD !important;
}

table td  tr:nth-child(odd):nth-child(n+1) {
  background-color: #FFFFFF;
}

table  tr:nth-child(even):nth-child(n+1) {
  background-color: #FAFAFA;
}

table {
  box-shadow: none;
}

