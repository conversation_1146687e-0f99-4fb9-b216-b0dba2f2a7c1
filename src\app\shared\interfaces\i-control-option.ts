import { SizeEnum } from '@core/enums/size';
import { NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { AppearanceEnum } from '@shared/enum/appearance-enum';
import { CalendarModeEnum } from '@shared/enum/calender-mode';
import { InputType } from '@shared/enum/input-type.enum';
import { LabelPositionEnum } from '@shared/enum/label-position-enum';

export interface IControlOption {
  formControlName: string;
  type: InputType;
  id: string;
  name: string;
  label: string;
  placeholder?: string;
  isRequired?: boolean;
  isReadonly?: boolean;
  appearance?: AppearanceEnum;
  labelPosition?: LabelPositionEnum;
  controlSize?: SizeEnum;
  autoFocus?: boolean;
  minLength?: number;
  maxLength?: number;
  options?: { name: string; id: any }[];
  step?: number;
  pattern?: string;
  class?: string;
  minGreg?: NgbDateStruct;
  maxGreg?: NgbDateStruct;
  minHijri?: NgbDateStruct;
  maxHijri?: NgbDateStruct;
  calendarMode?: CalendarModeEnum;
  allowedTypes?: string[];
  fileUrl?: string;
  fileName?: string;
  multiple?: boolean;
  max?:number;
  min?:number;
  onChange?: (value: any) => void;
  isVisible?: boolean | (() => boolean);
}
