// file-upload.service.ts
import { HttpClient, HttpParams, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class FileUploadService {
  private apiUrl =environment.apiUrl+'/api/Users/<USER>/UploadFile';

  constructor(private http: HttpClient) {}

  uploadFile(file: File, moduleId: number) {
    const formData = new FormData();
    formData.append('File', file);

    const params = new HttpParams()
      .set('FileName', file.name)
      .set('ModuleId', moduleId.toString());

    return this.http.post(this.apiUrl, formData, { params });
  }
}
