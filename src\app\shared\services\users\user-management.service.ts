// user-management.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { PaginatedResponse, UserDto } from '@shared/interfaces/user-management-response';

@Injectable({
  providedIn: 'root',
})
export class UserManagementService {
  private baseUrl =environment.apiUrl+ '/api/Users/<USER>';

  constructor(private http: HttpClient) {}

  getFundManagerUsers(): Observable<PaginatedResponse<UserDto>> {
    return this.http.get<PaginatedResponse<UserDto>>(`${this.baseUrl}/GetFundManagerUsers`);
  }

  getBoardSecretaryUsers(): Observable<PaginatedResponse<UserDto>> {
    return this.http.get<PaginatedResponse<UserDto>>(`${this.baseUrl}/GetBoardSecretaryUsers`);
  }

  getLegalCouncilUsers(): Observable<PaginatedResponse<UserDto>> {
    return this.http.get<PaginatedResponse<UserDto>>(`${this.baseUrl}/GetLegalCouncilUsers`);
  }

  userListForBoardMembers(fundId: number): Observable<PaginatedResponse<UserDto>> {
    return this.http.get<PaginatedResponse<UserDto>>(`${this.baseUrl}/UserListForBoardMembers?fundId=${fundId}`);
  }
}
