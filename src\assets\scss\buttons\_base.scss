@import "../variables";

.btn {
  padding: 12px 36px;
  border: none;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 700;
  line-height: 20px;
  gap: 6px;

  &.btn-primary {
    background-color: $navy-blue !important;
    color: white !important;
  }

  &.primary-btn {
    background-color: $navy-blue;
    color: white;
  }
  &.outline-btn {
    background-color: white !important;
    color: $navy-blue !important;
    border: 1px solid $navy-blue !important;
  }

  &.primary-btn:focus,
  &.primary-btn:active {
    background-color: $navy-blue;
    color: white;
    outline: none;
    box-shadow: none;
  }
  &.secondary-btn {
    background-color: #bdbdbd;
    color: #4f4f4f;
  }

  &.danger-btn {
    background-color: #dc2626;
    color: white;
  }

  &.cancel-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: $mid-gray-bg;
    color: $light-dark;
    border: 1px solid transparent;
    border-radius: 10px;
  }
  &.cancel-btn:focus,
  &.cancel-btn:active {
    background-color: $mid-gray-bg;
    color: $light-dark;
    outline: none;
    box-shadow: none;
  }

  &.save-draft-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: transparent;
    color: $navy-blue;
    border: 1px solid $navy-blue;
    border-radius: 10px;
  }
  // &.save-draft-btn:focus,
  // &.save-draft-btn:active {
  //   background-color: $navy-blue;
  //   color: white;
  //   outline: none;
  //   box-shadow: none;
  // }
}
div {
  &.margin-bottom-24 {
    margin-bottom: $margin-bottom-24;
  }
  &.margin-bottom-56 {
    margin-bottom: $margin-bottom-56;
  }
  &.w-57{
    width: 57%;
  }
}
p,
label {
  &.font-size-xxs {
    font-size: $font-size-xxs;
  }
  &.font-size-xs {
    font-size: $font-size-xs;
  }
  &.font-size-sm {
    font-size: $font-size-sm;
  }
  &.font-size-m {
    font-size: $font-size-m;
  }
  &.font-size-lg {
    font-size: $font-size-lg;
  }
  &.font-size-xl {
    font-size: $font-size-xl;
  }
  &.navy-color {
    color: $navy-blue;
  }
  &.bold-700 {
    font-weight: $bold-700;
  }
  &.bold-400 {
    font-weight: $bold-400;
  }
  &.light-dark-color {
    color: $light-dark;
  }
  &.dark-color {
    color: $dark;
  }
  &.dark-blue {
    color: $dark-blue;
  }
  &.dark-gray {
    color: $dark-gray;
  }

  &.margin-bottom-24 {
    margin-bottom: $margin-bottom-24;
  }
}
.header-button.btn {
  font-weight: 700;
  padding: 12px 11px;
  font-size: 1rem;
}
