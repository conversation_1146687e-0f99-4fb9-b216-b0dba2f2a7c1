<testsuites id="" name="" tests="906" failures="0" skipped="906" errors="0" time="0.4448430000000001">
<testsuite name="setup\global.cleanup.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="cleanup" tests="5" failures="0" skipped="5" time="0" errors="0">
<testcase name="cleanup test data" classname="setup\global.cleanup.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="cleanup authentication states" classname="setup\global.cleanup.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="generate performance report" classname="setup\global.cleanup.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="generate test summary report" classname="setup\global.cleanup.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="validate test environment state" classname="setup\global.cleanup.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="setup\global.setup.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="setup" tests="5" failures="0" skipped="5" time="0" errors="0">
<testcase name="authenticate users and prepare test environment" classname="setup\global.setup.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="seed test database" classname="setup\global.setup.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="validate test data integrity" classname="setup\global.setup.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="performance baseline measurement" classname="setup\global.setup.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="browser compatibility check" classname="setup\global.setup.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\authentication-and-rbac.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="chromium-ar" tests="22" failures="0" skipped="22" time="0" errors="0">
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Successful Login with Valid Credentials" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Failed Login with Invalid Credentials" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Login Form Validation" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Logout Functionality" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Session Timeout Handling" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Remember Me Functionality" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Dashboard and Navigation Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Fund Management Permissions" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Resolution Management Permissions" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Board Member View-Only Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Legal Council Role Access Control › Legal Council: Full Fund and Resolution Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Legal Council Role Access Control › Legal Council: Board Member Management Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Secretary Role Access Control › Board Secretary: Resolution and Member Management" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Secretary Role Access Control › Board Secretary: Fund View-Only Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Member Role Access Control › Board Member: Limited Dashboard Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Member Role Access Control › Board Member: Resolution Voting Access Only" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Member Role Access Control › Board Member: View-Only Fund and Member Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund-Specific Permissions › User Access Limited to Assigned Funds Only" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund-Specific Permissions › Cross-Fund Access Prevention" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Security Features › JWT Token Validation and Refresh" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Security Features › Unauthorized API Access Prevention" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Security Features › CSRF Protection Validation" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\board-member-management.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="chromium-ar" tests="15" failures="0" skipped="15" time="0" errors="0">
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Legal Council: Add First Independent Board Member as Chairman" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Board Secretary: Add Second Independent Member to Activate Fund" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Add Dependent Board Member" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Verify Maximum Independent Members Limit (14 members)" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Verify Chairman Uniqueness Constraint" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › View Board Members › Fund Manager: View All Board Members" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › View Board Members › Board Member: View Limited Member Information" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › View Board Members › Display No Members Message When Fund Has No Members" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Legal Council: Full CRUD Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Board Secretary: Full CRUD Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Fund Manager: View Only Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Board Member: View Only Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Business Rule Validation › Verify Fund Activities Disabled Without Minimum Members" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Business Rule Validation › Verify Required Field Validation" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Business Rule Validation › Verify Member Cannot Be Added Twice to Same Fund" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\localization-and-error-handling.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="chromium-ar" tests="19" failures="0" skipped="19" time="0" errors="0">
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Login Page Layout and Functionality" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Dashboard Layout and Navigation" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Fund Management Interface" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Resolution Management Interface" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Font Rendering and Unicode Support" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › Verify English Login Page Layout" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › Verify English Dashboard and Navigation" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › Verify English Fund and Resolution Management" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › Verify Language Switch Functionality" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › Verify Language Preference Persistence" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG001 (Required Field) in Both Languages" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG002 (Record Saved Successfully) Localization" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG006/MSG007 (Voting Suspension) Localization" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG008/MSG009 (New Resolution Creation) Localization" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › Verify Arabic Text Rendering Across Browsers" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › Verify Form Input Handling Across Browsers" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › Verify Error Message Positioning and Styling" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › Verify Error Message Accessibility" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › Verify Error Message Auto-Dismiss" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\localization-error-handling.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="chromium-ar" tests="29" failures="0" skipped="29" time="0" errors="0">
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should display Arabic login page with proper RTL layout" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should handle Arabic text input correctly" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should display Arabic dashboard with proper navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should maintain Arabic layout across page navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › should display English login page with proper LTR layout" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › should display English dashboard with proper navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › should switch languages dynamically" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › should persist language preference across page navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › should persist language preference across browser refresh" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG001 (Required Field) in Arabic" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG001 (Required Field) in English" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG002 (Record Saved Successfully) in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG003 (Record Sent Successfully) in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG006 (Voting Suspension Confirmation) in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should display Arabic correctly in chromium" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should handle form validation in chromium" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should display Arabic correctly in firefox" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should handle form validation in firefox" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should display Arabic correctly in webkit" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should handle form validation in webkit" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Font Rendering and Unicode Support › should render Arabic fonts correctly" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Font Rendering and Unicode Support › should handle mixed Arabic-English text" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Responsive Design and Mobile Testing › should display Arabic layout correctly on mobile" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Responsive Design and Mobile Testing › should handle touch interactions with Arabic interface" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Performance and Accessibility › should load Arabic pages within performance thresholds" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Performance and Accessibility › should maintain accessibility standards in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Performance and Accessibility › should support keyboard navigation in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › should display error messages with proper positioning in RTL" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › should auto-dismiss error messages appropriately" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\resolution-alternative-workflows.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="chromium-ar" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Legal Council: Suspend Voting by Editing Resolution Data" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Board Secretary: Suspend Voting by Adding Resolution Items" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Verify Voting Suspension Notifications to All Stakeholders" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Verify Voting Data Preservation After Suspension" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Legal Council: Create New Resolution from Approved Resolution" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Board Secretary: Create New Resolution from Not Approved Resolution" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Verify New Resolution Code Generation and Relationship" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Verify Notifications for New Resolution Creation" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative Workflow Business Rules › Verify Only Authorized Roles Can Trigger Alternative Workflows" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative Workflow Business Rules › Verify Alternative Workflows Maintain Audit Trail" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative Workflow Business Rules › Verify State Consistency After Alternative Workflows" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\resolution-lifecycle.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="chromium-ar" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Fund Manager: Create Draft Resolution" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Fund Manager: Send Draft Resolution to Pending" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Legal Council: Complete Resolution Data" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Fund Manager: Confirm Resolution for Voting" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Board Member: Vote on Resolution" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Resolution Approval: All Members Vote Yes" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Resolution Rejection: Majority Vote No" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Draft Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Pending Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Voting in Progress Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Approved Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Business Rule Validation › Verify Fund Must Have 2 Independent Members for Voting" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Business Rule Validation › Verify Resolution Code Generation" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Business Rule Validation › Verify Voting Methodology Enforcement" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Error Handling and Edge Cases › Handle Network Errors During State Transitions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Error Handling and Edge Cases › Verify Concurrent Editing Prevention" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\authentication-and-rbac.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="firefox-ar" tests="22" failures="0" skipped="22" time="0" errors="0">
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Successful Login with Valid Credentials" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Failed Login with Invalid Credentials" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Login Form Validation" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Logout Functionality" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Session Timeout Handling" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Remember Me Functionality" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Dashboard and Navigation Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Fund Management Permissions" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Resolution Management Permissions" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Board Member View-Only Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Legal Council Role Access Control › Legal Council: Full Fund and Resolution Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Legal Council Role Access Control › Legal Council: Board Member Management Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Secretary Role Access Control › Board Secretary: Resolution and Member Management" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Secretary Role Access Control › Board Secretary: Fund View-Only Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Member Role Access Control › Board Member: Limited Dashboard Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Member Role Access Control › Board Member: Resolution Voting Access Only" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Member Role Access Control › Board Member: View-Only Fund and Member Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund-Specific Permissions › User Access Limited to Assigned Funds Only" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund-Specific Permissions › Cross-Fund Access Prevention" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Security Features › JWT Token Validation and Refresh" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Security Features › Unauthorized API Access Prevention" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Security Features › CSRF Protection Validation" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\board-member-management.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="firefox-ar" tests="15" failures="0" skipped="15" time="0" errors="0">
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Legal Council: Add First Independent Board Member as Chairman" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Board Secretary: Add Second Independent Member to Activate Fund" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Add Dependent Board Member" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Verify Maximum Independent Members Limit (14 members)" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Verify Chairman Uniqueness Constraint" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › View Board Members › Fund Manager: View All Board Members" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › View Board Members › Board Member: View Limited Member Information" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › View Board Members › Display No Members Message When Fund Has No Members" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Legal Council: Full CRUD Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Board Secretary: Full CRUD Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Fund Manager: View Only Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Board Member: View Only Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Business Rule Validation › Verify Fund Activities Disabled Without Minimum Members" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Business Rule Validation › Verify Required Field Validation" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Business Rule Validation › Verify Member Cannot Be Added Twice to Same Fund" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\localization-and-error-handling.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="firefox-ar" tests="19" failures="0" skipped="19" time="0" errors="0">
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Login Page Layout and Functionality" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Dashboard Layout and Navigation" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Fund Management Interface" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Resolution Management Interface" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Font Rendering and Unicode Support" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › Verify English Login Page Layout" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › Verify English Dashboard and Navigation" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › Verify English Fund and Resolution Management" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › Verify Language Switch Functionality" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › Verify Language Preference Persistence" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG001 (Required Field) in Both Languages" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG002 (Record Saved Successfully) Localization" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG006/MSG007 (Voting Suspension) Localization" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG008/MSG009 (New Resolution Creation) Localization" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › Verify Arabic Text Rendering Across Browsers" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › Verify Form Input Handling Across Browsers" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › Verify Error Message Positioning and Styling" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › Verify Error Message Accessibility" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › Verify Error Message Auto-Dismiss" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\localization-error-handling.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="firefox-ar" tests="29" failures="0" skipped="29" time="0" errors="0">
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should display Arabic login page with proper RTL layout" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should handle Arabic text input correctly" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should display Arabic dashboard with proper navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should maintain Arabic layout across page navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › should display English login page with proper LTR layout" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › should display English dashboard with proper navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › should switch languages dynamically" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › should persist language preference across page navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › should persist language preference across browser refresh" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG001 (Required Field) in Arabic" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG001 (Required Field) in English" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG002 (Record Saved Successfully) in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG003 (Record Sent Successfully) in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG006 (Voting Suspension Confirmation) in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should display Arabic correctly in chromium" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should handle form validation in chromium" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should display Arabic correctly in firefox" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should handle form validation in firefox" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should display Arabic correctly in webkit" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should handle form validation in webkit" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Font Rendering and Unicode Support › should render Arabic fonts correctly" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Font Rendering and Unicode Support › should handle mixed Arabic-English text" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Responsive Design and Mobile Testing › should display Arabic layout correctly on mobile" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Responsive Design and Mobile Testing › should handle touch interactions with Arabic interface" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Performance and Accessibility › should load Arabic pages within performance thresholds" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Performance and Accessibility › should maintain accessibility standards in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Performance and Accessibility › should support keyboard navigation in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › should display error messages with proper positioning in RTL" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › should auto-dismiss error messages appropriately" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\resolution-alternative-workflows.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="firefox-ar" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Legal Council: Suspend Voting by Editing Resolution Data" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Board Secretary: Suspend Voting by Adding Resolution Items" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Verify Voting Suspension Notifications to All Stakeholders" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Verify Voting Data Preservation After Suspension" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Legal Council: Create New Resolution from Approved Resolution" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Board Secretary: Create New Resolution from Not Approved Resolution" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Verify New Resolution Code Generation and Relationship" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Verify Notifications for New Resolution Creation" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative Workflow Business Rules › Verify Only Authorized Roles Can Trigger Alternative Workflows" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative Workflow Business Rules › Verify Alternative Workflows Maintain Audit Trail" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative Workflow Business Rules › Verify State Consistency After Alternative Workflows" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\resolution-lifecycle.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="firefox-ar" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Fund Manager: Create Draft Resolution" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Fund Manager: Send Draft Resolution to Pending" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Legal Council: Complete Resolution Data" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Fund Manager: Confirm Resolution for Voting" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Board Member: Vote on Resolution" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Resolution Approval: All Members Vote Yes" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Resolution Rejection: Majority Vote No" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Draft Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Pending Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Voting in Progress Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Approved Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Business Rule Validation › Verify Fund Must Have 2 Independent Members for Voting" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Business Rule Validation › Verify Resolution Code Generation" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Business Rule Validation › Verify Voting Methodology Enforcement" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Error Handling and Edge Cases › Handle Network Errors During State Transitions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Error Handling and Edge Cases › Verify Concurrent Editing Prevention" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\authentication-and-rbac.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="webkit-ar" tests="22" failures="0" skipped="22" time="0" errors="0">
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Successful Login with Valid Credentials" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Failed Login with Invalid Credentials" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Login Form Validation" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Logout Functionality" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Session Timeout Handling" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Remember Me Functionality" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Dashboard and Navigation Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Fund Management Permissions" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Resolution Management Permissions" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Board Member View-Only Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Legal Council Role Access Control › Legal Council: Full Fund and Resolution Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Legal Council Role Access Control › Legal Council: Board Member Management Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Secretary Role Access Control › Board Secretary: Resolution and Member Management" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Secretary Role Access Control › Board Secretary: Fund View-Only Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Member Role Access Control › Board Member: Limited Dashboard Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Member Role Access Control › Board Member: Resolution Voting Access Only" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Member Role Access Control › Board Member: View-Only Fund and Member Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund-Specific Permissions › User Access Limited to Assigned Funds Only" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund-Specific Permissions › Cross-Fund Access Prevention" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Security Features › JWT Token Validation and Refresh" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Security Features › Unauthorized API Access Prevention" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Security Features › CSRF Protection Validation" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\board-member-management.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="webkit-ar" tests="15" failures="0" skipped="15" time="0" errors="0">
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Legal Council: Add First Independent Board Member as Chairman" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Board Secretary: Add Second Independent Member to Activate Fund" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Add Dependent Board Member" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Verify Maximum Independent Members Limit (14 members)" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Verify Chairman Uniqueness Constraint" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › View Board Members › Fund Manager: View All Board Members" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › View Board Members › Board Member: View Limited Member Information" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › View Board Members › Display No Members Message When Fund Has No Members" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Legal Council: Full CRUD Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Board Secretary: Full CRUD Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Fund Manager: View Only Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Board Member: View Only Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Business Rule Validation › Verify Fund Activities Disabled Without Minimum Members" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Business Rule Validation › Verify Required Field Validation" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Business Rule Validation › Verify Member Cannot Be Added Twice to Same Fund" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\localization-and-error-handling.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="webkit-ar" tests="19" failures="0" skipped="19" time="0" errors="0">
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Login Page Layout and Functionality" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Dashboard Layout and Navigation" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Fund Management Interface" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Resolution Management Interface" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Font Rendering and Unicode Support" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › Verify English Login Page Layout" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › Verify English Dashboard and Navigation" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › Verify English Fund and Resolution Management" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › Verify Language Switch Functionality" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › Verify Language Preference Persistence" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG001 (Required Field) in Both Languages" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG002 (Record Saved Successfully) Localization" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG006/MSG007 (Voting Suspension) Localization" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG008/MSG009 (New Resolution Creation) Localization" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › Verify Arabic Text Rendering Across Browsers" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › Verify Form Input Handling Across Browsers" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › Verify Error Message Positioning and Styling" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › Verify Error Message Accessibility" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › Verify Error Message Auto-Dismiss" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\localization-error-handling.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="webkit-ar" tests="29" failures="0" skipped="29" time="0" errors="0">
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should display Arabic login page with proper RTL layout" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should handle Arabic text input correctly" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should display Arabic dashboard with proper navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should maintain Arabic layout across page navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › should display English login page with proper LTR layout" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › should display English dashboard with proper navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › should switch languages dynamically" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › should persist language preference across page navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › should persist language preference across browser refresh" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG001 (Required Field) in Arabic" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG001 (Required Field) in English" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG002 (Record Saved Successfully) in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG003 (Record Sent Successfully) in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG006 (Voting Suspension Confirmation) in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should display Arabic correctly in chromium" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should handle form validation in chromium" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should display Arabic correctly in firefox" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should handle form validation in firefox" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should display Arabic correctly in webkit" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should handle form validation in webkit" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Font Rendering and Unicode Support › should render Arabic fonts correctly" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Font Rendering and Unicode Support › should handle mixed Arabic-English text" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Responsive Design and Mobile Testing › should display Arabic layout correctly on mobile" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Responsive Design and Mobile Testing › should handle touch interactions with Arabic interface" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Performance and Accessibility › should load Arabic pages within performance thresholds" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Performance and Accessibility › should maintain accessibility standards in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Performance and Accessibility › should support keyboard navigation in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › should display error messages with proper positioning in RTL" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › should auto-dismiss error messages appropriately" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\resolution-alternative-workflows.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="webkit-ar" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Legal Council: Suspend Voting by Editing Resolution Data" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Board Secretary: Suspend Voting by Adding Resolution Items" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Verify Voting Suspension Notifications to All Stakeholders" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Verify Voting Data Preservation After Suspension" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Legal Council: Create New Resolution from Approved Resolution" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Board Secretary: Create New Resolution from Not Approved Resolution" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Verify New Resolution Code Generation and Relationship" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Verify Notifications for New Resolution Creation" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative Workflow Business Rules › Verify Only Authorized Roles Can Trigger Alternative Workflows" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative Workflow Business Rules › Verify Alternative Workflows Maintain Audit Trail" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative Workflow Business Rules › Verify State Consistency After Alternative Workflows" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\resolution-lifecycle.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="webkit-ar" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Fund Manager: Create Draft Resolution" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Fund Manager: Send Draft Resolution to Pending" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Legal Council: Complete Resolution Data" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Fund Manager: Confirm Resolution for Voting" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Board Member: Vote on Resolution" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Resolution Approval: All Members Vote Yes" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Resolution Rejection: Majority Vote No" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Draft Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Pending Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Voting in Progress Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Approved Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Business Rule Validation › Verify Fund Must Have 2 Independent Members for Voting" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Business Rule Validation › Verify Resolution Code Generation" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Business Rule Validation › Verify Voting Methodology Enforcement" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Error Handling and Edge Cases › Handle Network Errors During State Transitions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Error Handling and Edge Cases › Verify Concurrent Editing Prevention" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\authentication-and-rbac.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="chromium-en" tests="22" failures="0" skipped="22" time="0" errors="0">
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Successful Login with Valid Credentials" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Failed Login with Invalid Credentials" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Login Form Validation" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Logout Functionality" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Session Timeout Handling" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Remember Me Functionality" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Dashboard and Navigation Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Fund Management Permissions" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Resolution Management Permissions" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Board Member View-Only Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Legal Council Role Access Control › Legal Council: Full Fund and Resolution Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Legal Council Role Access Control › Legal Council: Board Member Management Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Secretary Role Access Control › Board Secretary: Resolution and Member Management" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Secretary Role Access Control › Board Secretary: Fund View-Only Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Member Role Access Control › Board Member: Limited Dashboard Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Member Role Access Control › Board Member: Resolution Voting Access Only" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Member Role Access Control › Board Member: View-Only Fund and Member Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund-Specific Permissions › User Access Limited to Assigned Funds Only" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund-Specific Permissions › Cross-Fund Access Prevention" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Security Features › JWT Token Validation and Refresh" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Security Features › Unauthorized API Access Prevention" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Security Features › CSRF Protection Validation" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\board-member-management.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="chromium-en" tests="15" failures="0" skipped="15" time="0" errors="0">
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Legal Council: Add First Independent Board Member as Chairman" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Board Secretary: Add Second Independent Member to Activate Fund" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Add Dependent Board Member" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Verify Maximum Independent Members Limit (14 members)" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Verify Chairman Uniqueness Constraint" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › View Board Members › Fund Manager: View All Board Members" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › View Board Members › Board Member: View Limited Member Information" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › View Board Members › Display No Members Message When Fund Has No Members" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Legal Council: Full CRUD Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Board Secretary: Full CRUD Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Fund Manager: View Only Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Board Member: View Only Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Business Rule Validation › Verify Fund Activities Disabled Without Minimum Members" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Business Rule Validation › Verify Required Field Validation" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Business Rule Validation › Verify Member Cannot Be Added Twice to Same Fund" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\localization-and-error-handling.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="chromium-en" tests="19" failures="0" skipped="19" time="0" errors="0">
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Login Page Layout and Functionality" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Dashboard Layout and Navigation" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Fund Management Interface" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Resolution Management Interface" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Font Rendering and Unicode Support" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › Verify English Login Page Layout" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › Verify English Dashboard and Navigation" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › Verify English Fund and Resolution Management" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › Verify Language Switch Functionality" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › Verify Language Preference Persistence" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG001 (Required Field) in Both Languages" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG002 (Record Saved Successfully) Localization" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG006/MSG007 (Voting Suspension) Localization" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG008/MSG009 (New Resolution Creation) Localization" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › Verify Arabic Text Rendering Across Browsers" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › Verify Form Input Handling Across Browsers" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › Verify Error Message Positioning and Styling" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › Verify Error Message Accessibility" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › Verify Error Message Auto-Dismiss" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\localization-error-handling.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="chromium-en" tests="29" failures="0" skipped="29" time="0" errors="0">
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should display Arabic login page with proper RTL layout" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should handle Arabic text input correctly" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should display Arabic dashboard with proper navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should maintain Arabic layout across page navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › should display English login page with proper LTR layout" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › should display English dashboard with proper navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › should switch languages dynamically" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › should persist language preference across page navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › should persist language preference across browser refresh" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG001 (Required Field) in Arabic" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG001 (Required Field) in English" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG002 (Record Saved Successfully) in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG003 (Record Sent Successfully) in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG006 (Voting Suspension Confirmation) in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should display Arabic correctly in chromium" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should handle form validation in chromium" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should display Arabic correctly in firefox" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should handle form validation in firefox" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should display Arabic correctly in webkit" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should handle form validation in webkit" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Font Rendering and Unicode Support › should render Arabic fonts correctly" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Font Rendering and Unicode Support › should handle mixed Arabic-English text" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Responsive Design and Mobile Testing › should display Arabic layout correctly on mobile" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Responsive Design and Mobile Testing › should handle touch interactions with Arabic interface" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Performance and Accessibility › should load Arabic pages within performance thresholds" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Performance and Accessibility › should maintain accessibility standards in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Performance and Accessibility › should support keyboard navigation in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › should display error messages with proper positioning in RTL" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › should auto-dismiss error messages appropriately" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\resolution-alternative-workflows.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="chromium-en" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Legal Council: Suspend Voting by Editing Resolution Data" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Board Secretary: Suspend Voting by Adding Resolution Items" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Verify Voting Suspension Notifications to All Stakeholders" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Verify Voting Data Preservation After Suspension" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Legal Council: Create New Resolution from Approved Resolution" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Board Secretary: Create New Resolution from Not Approved Resolution" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Verify New Resolution Code Generation and Relationship" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Verify Notifications for New Resolution Creation" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative Workflow Business Rules › Verify Only Authorized Roles Can Trigger Alternative Workflows" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative Workflow Business Rules › Verify Alternative Workflows Maintain Audit Trail" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative Workflow Business Rules › Verify State Consistency After Alternative Workflows" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\resolution-lifecycle.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="chromium-en" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Fund Manager: Create Draft Resolution" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Fund Manager: Send Draft Resolution to Pending" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Legal Council: Complete Resolution Data" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Fund Manager: Confirm Resolution for Voting" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Board Member: Vote on Resolution" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Resolution Approval: All Members Vote Yes" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Resolution Rejection: Majority Vote No" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Draft Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Pending Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Voting in Progress Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Approved Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Business Rule Validation › Verify Fund Must Have 2 Independent Members for Voting" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Business Rule Validation › Verify Resolution Code Generation" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Business Rule Validation › Verify Voting Methodology Enforcement" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Error Handling and Edge Cases › Handle Network Errors During State Transitions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Error Handling and Edge Cases › Verify Concurrent Editing Prevention" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\authentication-and-rbac.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="firefox-en" tests="22" failures="0" skipped="22" time="0" errors="0">
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Successful Login with Valid Credentials" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Failed Login with Invalid Credentials" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Login Form Validation" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Logout Functionality" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Session Timeout Handling" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Remember Me Functionality" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Dashboard and Navigation Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Fund Management Permissions" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Resolution Management Permissions" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Board Member View-Only Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Legal Council Role Access Control › Legal Council: Full Fund and Resolution Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Legal Council Role Access Control › Legal Council: Board Member Management Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Secretary Role Access Control › Board Secretary: Resolution and Member Management" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Secretary Role Access Control › Board Secretary: Fund View-Only Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Member Role Access Control › Board Member: Limited Dashboard Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Member Role Access Control › Board Member: Resolution Voting Access Only" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Member Role Access Control › Board Member: View-Only Fund and Member Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund-Specific Permissions › User Access Limited to Assigned Funds Only" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund-Specific Permissions › Cross-Fund Access Prevention" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Security Features › JWT Token Validation and Refresh" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Security Features › Unauthorized API Access Prevention" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Security Features › CSRF Protection Validation" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\board-member-management.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="firefox-en" tests="15" failures="0" skipped="15" time="0" errors="0">
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Legal Council: Add First Independent Board Member as Chairman" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Board Secretary: Add Second Independent Member to Activate Fund" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Add Dependent Board Member" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Verify Maximum Independent Members Limit (14 members)" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Verify Chairman Uniqueness Constraint" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › View Board Members › Fund Manager: View All Board Members" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › View Board Members › Board Member: View Limited Member Information" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › View Board Members › Display No Members Message When Fund Has No Members" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Legal Council: Full CRUD Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Board Secretary: Full CRUD Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Fund Manager: View Only Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Board Member: View Only Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Business Rule Validation › Verify Fund Activities Disabled Without Minimum Members" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Business Rule Validation › Verify Required Field Validation" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Business Rule Validation › Verify Member Cannot Be Added Twice to Same Fund" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\localization-and-error-handling.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="firefox-en" tests="19" failures="0" skipped="19" time="0" errors="0">
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Login Page Layout and Functionality" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Dashboard Layout and Navigation" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Fund Management Interface" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Resolution Management Interface" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Font Rendering and Unicode Support" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › Verify English Login Page Layout" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › Verify English Dashboard and Navigation" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › Verify English Fund and Resolution Management" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › Verify Language Switch Functionality" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › Verify Language Preference Persistence" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG001 (Required Field) in Both Languages" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG002 (Record Saved Successfully) Localization" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG006/MSG007 (Voting Suspension) Localization" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG008/MSG009 (New Resolution Creation) Localization" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › Verify Arabic Text Rendering Across Browsers" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › Verify Form Input Handling Across Browsers" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › Verify Error Message Positioning and Styling" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › Verify Error Message Accessibility" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › Verify Error Message Auto-Dismiss" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\localization-error-handling.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="firefox-en" tests="29" failures="0" skipped="29" time="0" errors="0">
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should display Arabic login page with proper RTL layout" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should handle Arabic text input correctly" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should display Arabic dashboard with proper navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should maintain Arabic layout across page navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › should display English login page with proper LTR layout" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › should display English dashboard with proper navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › should switch languages dynamically" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › should persist language preference across page navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › should persist language preference across browser refresh" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG001 (Required Field) in Arabic" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG001 (Required Field) in English" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG002 (Record Saved Successfully) in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG003 (Record Sent Successfully) in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG006 (Voting Suspension Confirmation) in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should display Arabic correctly in chromium" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should handle form validation in chromium" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should display Arabic correctly in firefox" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should handle form validation in firefox" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should display Arabic correctly in webkit" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should handle form validation in webkit" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Font Rendering and Unicode Support › should render Arabic fonts correctly" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Font Rendering and Unicode Support › should handle mixed Arabic-English text" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Responsive Design and Mobile Testing › should display Arabic layout correctly on mobile" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Responsive Design and Mobile Testing › should handle touch interactions with Arabic interface" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Performance and Accessibility › should load Arabic pages within performance thresholds" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Performance and Accessibility › should maintain accessibility standards in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Performance and Accessibility › should support keyboard navigation in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › should display error messages with proper positioning in RTL" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › should auto-dismiss error messages appropriately" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\resolution-alternative-workflows.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="firefox-en" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Legal Council: Suspend Voting by Editing Resolution Data" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Board Secretary: Suspend Voting by Adding Resolution Items" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Verify Voting Suspension Notifications to All Stakeholders" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Verify Voting Data Preservation After Suspension" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Legal Council: Create New Resolution from Approved Resolution" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Board Secretary: Create New Resolution from Not Approved Resolution" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Verify New Resolution Code Generation and Relationship" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Verify Notifications for New Resolution Creation" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative Workflow Business Rules › Verify Only Authorized Roles Can Trigger Alternative Workflows" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative Workflow Business Rules › Verify Alternative Workflows Maintain Audit Trail" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative Workflow Business Rules › Verify State Consistency After Alternative Workflows" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\resolution-lifecycle.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="firefox-en" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Fund Manager: Create Draft Resolution" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Fund Manager: Send Draft Resolution to Pending" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Legal Council: Complete Resolution Data" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Fund Manager: Confirm Resolution for Voting" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Board Member: Vote on Resolution" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Resolution Approval: All Members Vote Yes" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Resolution Rejection: Majority Vote No" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Draft Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Pending Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Voting in Progress Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Approved Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Business Rule Validation › Verify Fund Must Have 2 Independent Members for Voting" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Business Rule Validation › Verify Resolution Code Generation" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Business Rule Validation › Verify Voting Methodology Enforcement" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Error Handling and Edge Cases › Handle Network Errors During State Transitions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Error Handling and Edge Cases › Verify Concurrent Editing Prevention" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\authentication-and-rbac.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="Mobile Chrome" tests="22" failures="0" skipped="22" time="0" errors="0">
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Successful Login with Valid Credentials" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Failed Login with Invalid Credentials" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Login Form Validation" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Logout Functionality" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Session Timeout Handling" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Remember Me Functionality" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Dashboard and Navigation Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Fund Management Permissions" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Resolution Management Permissions" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Board Member View-Only Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Legal Council Role Access Control › Legal Council: Full Fund and Resolution Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Legal Council Role Access Control › Legal Council: Board Member Management Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Secretary Role Access Control › Board Secretary: Resolution and Member Management" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Secretary Role Access Control › Board Secretary: Fund View-Only Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Member Role Access Control › Board Member: Limited Dashboard Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Member Role Access Control › Board Member: Resolution Voting Access Only" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Member Role Access Control › Board Member: View-Only Fund and Member Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund-Specific Permissions › User Access Limited to Assigned Funds Only" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund-Specific Permissions › Cross-Fund Access Prevention" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Security Features › JWT Token Validation and Refresh" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Security Features › Unauthorized API Access Prevention" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Security Features › CSRF Protection Validation" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\board-member-management.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="Mobile Chrome" tests="15" failures="0" skipped="15" time="0" errors="0">
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Legal Council: Add First Independent Board Member as Chairman" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Board Secretary: Add Second Independent Member to Activate Fund" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Add Dependent Board Member" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Verify Maximum Independent Members Limit (14 members)" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Verify Chairman Uniqueness Constraint" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › View Board Members › Fund Manager: View All Board Members" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › View Board Members › Board Member: View Limited Member Information" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › View Board Members › Display No Members Message When Fund Has No Members" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Legal Council: Full CRUD Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Board Secretary: Full CRUD Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Fund Manager: View Only Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Board Member: View Only Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Business Rule Validation › Verify Fund Activities Disabled Without Minimum Members" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Business Rule Validation › Verify Required Field Validation" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Business Rule Validation › Verify Member Cannot Be Added Twice to Same Fund" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\localization-and-error-handling.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="Mobile Chrome" tests="19" failures="0" skipped="19" time="0" errors="0">
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Login Page Layout and Functionality" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Dashboard Layout and Navigation" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Fund Management Interface" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Resolution Management Interface" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Font Rendering and Unicode Support" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › Verify English Login Page Layout" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › Verify English Dashboard and Navigation" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › Verify English Fund and Resolution Management" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › Verify Language Switch Functionality" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › Verify Language Preference Persistence" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG001 (Required Field) in Both Languages" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG002 (Record Saved Successfully) Localization" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG006/MSG007 (Voting Suspension) Localization" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG008/MSG009 (New Resolution Creation) Localization" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › Verify Arabic Text Rendering Across Browsers" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › Verify Form Input Handling Across Browsers" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › Verify Error Message Positioning and Styling" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › Verify Error Message Accessibility" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › Verify Error Message Auto-Dismiss" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\localization-error-handling.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="Mobile Chrome" tests="29" failures="0" skipped="29" time="0" errors="0">
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should display Arabic login page with proper RTL layout" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should handle Arabic text input correctly" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should display Arabic dashboard with proper navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should maintain Arabic layout across page navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › should display English login page with proper LTR layout" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › should display English dashboard with proper navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › should switch languages dynamically" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › should persist language preference across page navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › should persist language preference across browser refresh" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG001 (Required Field) in Arabic" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG001 (Required Field) in English" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG002 (Record Saved Successfully) in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG003 (Record Sent Successfully) in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG006 (Voting Suspension Confirmation) in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should display Arabic correctly in chromium" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should handle form validation in chromium" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should display Arabic correctly in firefox" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should handle form validation in firefox" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should display Arabic correctly in webkit" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should handle form validation in webkit" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Font Rendering and Unicode Support › should render Arabic fonts correctly" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Font Rendering and Unicode Support › should handle mixed Arabic-English text" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Responsive Design and Mobile Testing › should display Arabic layout correctly on mobile" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Responsive Design and Mobile Testing › should handle touch interactions with Arabic interface" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Performance and Accessibility › should load Arabic pages within performance thresholds" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Performance and Accessibility › should maintain accessibility standards in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Performance and Accessibility › should support keyboard navigation in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › should display error messages with proper positioning in RTL" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › should auto-dismiss error messages appropriately" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\resolution-alternative-workflows.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="Mobile Chrome" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Legal Council: Suspend Voting by Editing Resolution Data" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Board Secretary: Suspend Voting by Adding Resolution Items" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Verify Voting Suspension Notifications to All Stakeholders" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Verify Voting Data Preservation After Suspension" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Legal Council: Create New Resolution from Approved Resolution" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Board Secretary: Create New Resolution from Not Approved Resolution" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Verify New Resolution Code Generation and Relationship" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Verify Notifications for New Resolution Creation" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative Workflow Business Rules › Verify Only Authorized Roles Can Trigger Alternative Workflows" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative Workflow Business Rules › Verify Alternative Workflows Maintain Audit Trail" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative Workflow Business Rules › Verify State Consistency After Alternative Workflows" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\resolution-lifecycle.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="Mobile Chrome" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Fund Manager: Create Draft Resolution" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Fund Manager: Send Draft Resolution to Pending" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Legal Council: Complete Resolution Data" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Fund Manager: Confirm Resolution for Voting" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Board Member: Vote on Resolution" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Resolution Approval: All Members Vote Yes" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Resolution Rejection: Majority Vote No" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Draft Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Pending Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Voting in Progress Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Approved Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Business Rule Validation › Verify Fund Must Have 2 Independent Members for Voting" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Business Rule Validation › Verify Resolution Code Generation" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Business Rule Validation › Verify Voting Methodology Enforcement" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Error Handling and Edge Cases › Handle Network Errors During State Transitions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Error Handling and Edge Cases › Verify Concurrent Editing Prevention" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\authentication-and-rbac.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="Mobile Safari" tests="22" failures="0" skipped="22" time="0" errors="0">
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Successful Login with Valid Credentials" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Failed Login with Invalid Credentials" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Login Form Validation" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Logout Functionality" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Session Timeout Handling" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Remember Me Functionality" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Dashboard and Navigation Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Fund Management Permissions" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Resolution Management Permissions" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Board Member View-Only Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Legal Council Role Access Control › Legal Council: Full Fund and Resolution Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Legal Council Role Access Control › Legal Council: Board Member Management Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Secretary Role Access Control › Board Secretary: Resolution and Member Management" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Secretary Role Access Control › Board Secretary: Fund View-Only Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Member Role Access Control › Board Member: Limited Dashboard Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Member Role Access Control › Board Member: Resolution Voting Access Only" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Member Role Access Control › Board Member: View-Only Fund and Member Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund-Specific Permissions › User Access Limited to Assigned Funds Only" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund-Specific Permissions › Cross-Fund Access Prevention" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Security Features › JWT Token Validation and Refresh" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Security Features › Unauthorized API Access Prevention" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Security Features › CSRF Protection Validation" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\board-member-management.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="Mobile Safari" tests="15" failures="0" skipped="15" time="0" errors="0">
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Legal Council: Add First Independent Board Member as Chairman" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Board Secretary: Add Second Independent Member to Activate Fund" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Add Dependent Board Member" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Verify Maximum Independent Members Limit (14 members)" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Verify Chairman Uniqueness Constraint" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › View Board Members › Fund Manager: View All Board Members" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › View Board Members › Board Member: View Limited Member Information" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › View Board Members › Display No Members Message When Fund Has No Members" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Legal Council: Full CRUD Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Board Secretary: Full CRUD Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Fund Manager: View Only Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Board Member: View Only Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Business Rule Validation › Verify Fund Activities Disabled Without Minimum Members" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Business Rule Validation › Verify Required Field Validation" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Business Rule Validation › Verify Member Cannot Be Added Twice to Same Fund" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\localization-and-error-handling.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="Mobile Safari" tests="19" failures="0" skipped="19" time="0" errors="0">
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Login Page Layout and Functionality" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Dashboard Layout and Navigation" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Fund Management Interface" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Resolution Management Interface" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Font Rendering and Unicode Support" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › Verify English Login Page Layout" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › Verify English Dashboard and Navigation" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › Verify English Fund and Resolution Management" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › Verify Language Switch Functionality" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › Verify Language Preference Persistence" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG001 (Required Field) in Both Languages" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG002 (Record Saved Successfully) Localization" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG006/MSG007 (Voting Suspension) Localization" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG008/MSG009 (New Resolution Creation) Localization" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › Verify Arabic Text Rendering Across Browsers" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › Verify Form Input Handling Across Browsers" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › Verify Error Message Positioning and Styling" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › Verify Error Message Accessibility" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › Verify Error Message Auto-Dismiss" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\localization-error-handling.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="Mobile Safari" tests="29" failures="0" skipped="29" time="0" errors="0">
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should display Arabic login page with proper RTL layout" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should handle Arabic text input correctly" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should display Arabic dashboard with proper navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should maintain Arabic layout across page navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › should display English login page with proper LTR layout" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › should display English dashboard with proper navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › should switch languages dynamically" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › should persist language preference across page navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › should persist language preference across browser refresh" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG001 (Required Field) in Arabic" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG001 (Required Field) in English" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG002 (Record Saved Successfully) in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG003 (Record Sent Successfully) in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG006 (Voting Suspension Confirmation) in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should display Arabic correctly in chromium" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should handle form validation in chromium" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should display Arabic correctly in firefox" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should handle form validation in firefox" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should display Arabic correctly in webkit" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should handle form validation in webkit" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Font Rendering and Unicode Support › should render Arabic fonts correctly" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Font Rendering and Unicode Support › should handle mixed Arabic-English text" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Responsive Design and Mobile Testing › should display Arabic layout correctly on mobile" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Responsive Design and Mobile Testing › should handle touch interactions with Arabic interface" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Performance and Accessibility › should load Arabic pages within performance thresholds" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Performance and Accessibility › should maintain accessibility standards in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Performance and Accessibility › should support keyboard navigation in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › should display error messages with proper positioning in RTL" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › should auto-dismiss error messages appropriately" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\resolution-alternative-workflows.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="Mobile Safari" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Legal Council: Suspend Voting by Editing Resolution Data" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Board Secretary: Suspend Voting by Adding Resolution Items" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Verify Voting Suspension Notifications to All Stakeholders" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Verify Voting Data Preservation After Suspension" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Legal Council: Create New Resolution from Approved Resolution" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Board Secretary: Create New Resolution from Not Approved Resolution" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Verify New Resolution Code Generation and Relationship" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Verify Notifications for New Resolution Creation" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative Workflow Business Rules › Verify Only Authorized Roles Can Trigger Alternative Workflows" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative Workflow Business Rules › Verify Alternative Workflows Maintain Audit Trail" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative Workflow Business Rules › Verify State Consistency After Alternative Workflows" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\resolution-lifecycle.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="Mobile Safari" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Fund Manager: Create Draft Resolution" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Fund Manager: Send Draft Resolution to Pending" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Legal Council: Complete Resolution Data" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Fund Manager: Confirm Resolution for Voting" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Board Member: Vote on Resolution" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Resolution Approval: All Members Vote Yes" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Resolution Rejection: Majority Vote No" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Draft Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Pending Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Voting in Progress Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Approved Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Business Rule Validation › Verify Fund Must Have 2 Independent Members for Voting" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Business Rule Validation › Verify Resolution Code Generation" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Business Rule Validation › Verify Voting Methodology Enforcement" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Error Handling and Edge Cases › Handle Network Errors During State Transitions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Error Handling and Edge Cases › Verify Concurrent Editing Prevention" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\authentication-and-rbac.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="Microsoft Edge" tests="22" failures="0" skipped="22" time="0" errors="0">
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Successful Login with Valid Credentials" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Failed Login with Invalid Credentials" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Login Form Validation" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Logout Functionality" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Session Timeout Handling" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Authentication Flow › Remember Me Functionality" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Dashboard and Navigation Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Fund Management Permissions" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Resolution Management Permissions" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund Manager Role Access Control › Fund Manager: Board Member View-Only Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Legal Council Role Access Control › Legal Council: Full Fund and Resolution Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Legal Council Role Access Control › Legal Council: Board Member Management Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Secretary Role Access Control › Board Secretary: Resolution and Member Management" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Secretary Role Access Control › Board Secretary: Fund View-Only Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Member Role Access Control › Board Member: Limited Dashboard Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Member Role Access Control › Board Member: Resolution Voting Access Only" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Board Member Role Access Control › Board Member: View-Only Fund and Member Access" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund-Specific Permissions › User Access Limited to Assigned Funds Only" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Fund-Specific Permissions › Cross-Fund Access Prevention" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Security Features › JWT Token Validation and Refresh" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Security Features › Unauthorized API Access Prevention" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication and Role-Based Access Control › Security Features › CSRF Protection Validation" classname="tests\authentication-and-rbac.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\board-member-management.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="Microsoft Edge" tests="15" failures="0" skipped="15" time="0" errors="0">
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Legal Council: Add First Independent Board Member as Chairman" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Board Secretary: Add Second Independent Member to Activate Fund" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Add Dependent Board Member" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Verify Maximum Independent Members Limit (14 members)" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Add Board Members - Legal Council/Board Secretary › Verify Chairman Uniqueness Constraint" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › View Board Members › Fund Manager: View All Board Members" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › View Board Members › Board Member: View Limited Member Information" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › View Board Members › Display No Members Message When Fund Has No Members" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Legal Council: Full CRUD Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Board Secretary: Full CRUD Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Fund Manager: View Only Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Role-Based Access Control › Board Member: View Only Access" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Business Rule Validation › Verify Fund Activities Disabled Without Minimum Members" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Business Rule Validation › Verify Required Field Validation" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Board Member Management › Business Rule Validation › Verify Member Cannot Be Added Twice to Same Fund" classname="tests\board-member-management.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\localization-and-error-handling.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="Microsoft Edge" tests="19" failures="0" skipped="19" time="0" errors="0">
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Login Page Layout and Functionality" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Dashboard Layout and Navigation" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Fund Management Interface" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Resolution Management Interface" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › Verify Arabic Font Rendering and Unicode Support" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › Verify English Login Page Layout" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › Verify English Dashboard and Navigation" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › Verify English Fund and Resolution Management" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › Verify Language Switch Functionality" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › Verify Language Preference Persistence" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG001 (Required Field) in Both Languages" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG002 (Record Saved Successfully) Localization" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG006/MSG007 (Voting Suspension) Localization" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › Verify MSG008/MSG009 (New Resolution Creation) Localization" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › Verify Arabic Text Rendering Across Browsers" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › Verify Form Input Handling Across Browsers" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › Verify Error Message Positioning and Styling" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › Verify Error Message Accessibility" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › Verify Error Message Auto-Dismiss" classname="tests\localization-and-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\localization-error-handling.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="Microsoft Edge" tests="29" failures="0" skipped="29" time="0" errors="0">
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should display Arabic login page with proper RTL layout" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should handle Arabic text input correctly" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should display Arabic dashboard with proper navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Arabic Language and RTL Layout › should maintain Arabic layout across page navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › should display English login page with proper LTR layout" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › English Language and LTR Layout › should display English dashboard with proper navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › should switch languages dynamically" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › should persist language preference across page navigation" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Language Switching and Persistence › should persist language preference across browser refresh" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG001 (Required Field) in Arabic" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG001 (Required Field) in English" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG002 (Record Saved Successfully) in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG003 (Record Sent Successfully) in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › System Message Localization (MSG Codes) › should display MSG006 (Voting Suspension Confirmation) in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should display Arabic correctly in chromium" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should handle form validation in chromium" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should display Arabic correctly in firefox" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should handle form validation in firefox" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should display Arabic correctly in webkit" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Cross-Browser Compatibility › should handle form validation in webkit" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Font Rendering and Unicode Support › should render Arabic fonts correctly" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Font Rendering and Unicode Support › should handle mixed Arabic-English text" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Responsive Design and Mobile Testing › should display Arabic layout correctly on mobile" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Responsive Design and Mobile Testing › should handle touch interactions with Arabic interface" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Performance and Accessibility › should load Arabic pages within performance thresholds" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Performance and Accessibility › should maintain accessibility standards in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Performance and Accessibility › should support keyboard navigation in both languages" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › should display error messages with proper positioning in RTL" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Localization and Error Handling › Error Message Display and UX › should auto-dismiss error messages appropriately" classname="tests\localization-error-handling.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\resolution-alternative-workflows.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="Microsoft Edge" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Legal Council: Suspend Voting by Editing Resolution Data" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Board Secretary: Suspend Voting by Adding Resolution Items" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Verify Voting Suspension Notifications to All Stakeholders" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 1: Voting Suspension Workflow › Verify Voting Data Preservation After Suspension" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Legal Council: Create New Resolution from Approved Resolution" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Board Secretary: Create New Resolution from Not Approved Resolution" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Verify New Resolution Code Generation and Relationship" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative 2: New Resolution from Approved/NotApproved › Verify Notifications for New Resolution Creation" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative Workflow Business Rules › Verify Only Authorized Roles Can Trigger Alternative Workflows" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative Workflow Business Rules › Verify Alternative Workflows Maintain Audit Trail" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Alternative Workflows › Alternative Workflow Business Rules › Verify State Consistency After Alternative Workflows" classname="tests\resolution-alternative-workflows.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="tests\resolution-lifecycle.spec.ts" timestamp="2025-07-07T06:30:36.730Z" hostname="Microsoft Edge" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Fund Manager: Create Draft Resolution" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Fund Manager: Send Draft Resolution to Pending" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Legal Council: Complete Resolution Data" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Fund Manager: Confirm Resolution for Voting" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Board Member: Vote on Resolution" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Resolution Approval: All Members Vote Yes" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Standard Resolution Workflow (Alternative 3) › Resolution Rejection: Majority Vote No" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Draft Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Pending Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Voting in Progress Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Resolution State Validation › Verify Approved Resolution Actions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Business Rule Validation › Verify Fund Must Have 2 Independent Members for Voting" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Business Rule Validation › Verify Resolution Code Generation" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Business Rule Validation › Verify Voting Methodology Enforcement" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Error Handling and Edge Cases › Handle Network Errors During State Transitions" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Resolution Lifecycle Management › Error Handling and Edge Cases › Verify Concurrent Editing Prevention" classname="tests\resolution-lifecycle.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>