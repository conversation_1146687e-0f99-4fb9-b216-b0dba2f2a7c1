# Jadwa Fund Management System - E2E Test Suite

This comprehensive end-to-end test suite validates all user stories, alternative workflows, and business scenarios for the Jadwa Fund Management System using both **Playwright** and **Selenium WebDriver**. The test suite is designed to align with Clean Architecture patterns, CQRS implementation, and Arabic/English localization requirements.

## 🎭 Dual Framework Support

The test suite now includes both automation frameworks:

- **Playwright Tests** (`tests/e2e/`): Modern, fast automation with excellent debugging capabilities
- **Selenium Tests** (`tests/selenium/`): Traditional WebDriver automation with broad browser support

Both frameworks share the same:
- Environment configuration and test data fixtures
- Page object patterns and business rule validation
- Arabic/English localization testing and cross-browser support

## 📋 Table of Contents

- [Dual Framework Support](#-dual-framework-support)
- [Overview](#overview)
- [Framework Comparison](#-framework-comparison)
- [Test Coverage](#test-coverage)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Configuration](#configuration)
- [Running Tests](#running-tests)
- [Test Structure](#test-structure)
- [Reporting](#reporting)
- [Troubleshooting](#troubleshooting)

## 🎯 Overview

The test suite covers:

- **Complete User Story Coverage**: All documented scenarios from Sprint.md & Stories.md
- **Alternative Workflow Testing**: 17 alternative scenarios for Resolution lifecycle management
- **Role-Based Access Control**: Testing for all 4 user roles with fund-specific permissions
- **Localization**: Dual-language testing (Arabic/English) with RTL/LTR layout validation
- **Cross-Browser Compatibility**: Chrome, Firefox, Safari, and Edge support
- **Performance Validation**: Load time assertions and performance metrics

## 📊 Test Coverage

### User Stories Covered
- ✅ Fund Management (Create, Edit, View, Delete)
- ✅ Resolution Lifecycle Management (Draft → Approved/NotApproved)
- ✅ Board Member Management (CRUD operations)
- ✅ Voting System (All Members, Majority voting)
- ✅ Alternative Workflows (Voting suspension, New resolution creation)
- ✅ Role-Based Access Control (4 user roles)
- ✅ Localization (Arabic/English)
- ✅ Error Handling (MSG001-MSG009)

### Alternative Workflows
1. **Alternative 1**: Voting suspension workflow with MSG006/MSG007 validation
2. **Alternative 2**: New resolution creation from Approved/NotApproved with MSG008/MSG009
3. **Alternative 3**: Standard resolution workflow without alternatives

### User Roles Tested
- **Fund Manager**: Fund and resolution creation, view board members
- **Legal Council**: Full CRUD access to funds, resolutions, and board members
- **Board Secretary**: Resolution completion, board member management, fund view-only
- **Board Member**: Resolution voting, view-only access to funds and members

## 🔧 Prerequisites

- Node.js 18+ 
- npm or yarn package manager
- Access to Jadwa Fund Management System (local, test, or staging environment)
- Valid user credentials for all 4 roles

## 📦 Installation

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Install Playwright Browsers**
   ```bash
   npx playwright install
   ```

3. **Verify Installation**
   ```bash
   npx playwright --version
   ```

4. **Selenium Setup** (Automatic)
   ```bash
   # WebDriver dependencies are automatically managed
   # No additional setup required for Selenium tests
   ```

## 🚀 Running Tests

### Playwright Tests (Recommended)
```bash
# Run all Playwright tests
npm run test:e2e

# Environment-specific execution
npm run test:e2e:local
npm run test:e2e:test
npm run test:e2e:production

# Browser-specific execution
npx playwright test --project=chromium-ar
npx playwright test --project=firefox-en

# Interactive mode
npm run test:e2e:ui

# Debug mode
npm run test:e2e:debug

# Generate and view reports
npm run test:e2e:report
```

### Selenium Tests (Alternative Framework)
```bash
# Run all Selenium tests
npm run test:selenium

# Browser-specific execution
npm run test:selenium:chrome
npm run test:selenium:firefox
npm run test:selenium:edge

# Environment-specific execution
npm run test:selenium:local
npm run test:selenium:test
npm run test:selenium:production

# Test suite-specific execution
npm run test:selenium:auth
npm run test:selenium:resolution
npm run test:selenium:localization

# View Selenium reports
npm run test:selenium:report
```

### Framework Comparison
```bash
# Run both frameworks
npm run test:all

# Compare results between frameworks
npm run test:compare
```

## 🔄 Framework Comparison

| Feature | Playwright | Selenium |
|---------|------------|----------|
| **Execution Speed** | ⚡ Fast | 🐌 Standard |
| **Browser Support** | Chrome, Firefox, Safari, Edge | Chrome, Firefox, Safari, Edge |
| **Language Support** | Arabic/English RTL/LTR | Arabic/English RTL/LTR |
| **Test Framework** | Built-in test runner | Mocha + Chai |
| **Page Objects** | Custom implementation | Custom implementation |
| **Reporting** | HTML, JSON, Allure | HTML, JSON, Mochawesome |
| **Debugging** | 🔧 Excellent tools | 🔧 Standard debugging |
| **CI/CD Integration** | ✅ Excellent | ✅ Good |
| **Learning Curve** | 📚 Moderate | 📚 Moderate |
| **Maintenance** | 🛠️ Low | 🛠️ Medium |

### When to Use Each Framework

**Use Playwright when:**
- You need fast test execution
- You want excellent debugging capabilities
- You prefer modern automation features
- You're starting a new test suite

**Use Selenium when:**
- You need maximum browser compatibility
- You have existing Selenium expertise
- You want industry-standard WebDriver
- You need specific WebDriver features

## ⚙️ Configuration

### Environment Configuration

1. **Set Environment Variables**
   ```bash
   # Local environment (default)
   export TEST_ENV=local

   # Test environment
   export TEST_ENV=test

   # Production environment (read-only tests)
   export TEST_ENV=production
   ```

2. **Configure Credentials**
   
   Update `tests/e2e/config/environments.ts` with your environment-specific credentials:
   
   ```typescript
   credentials: {
     fundManager: {
       username: '<EMAIL>',
       password: 'your-password'
     },
     // ... other roles
   }
   ```

3. **Browser Configuration**
   
   The test suite is configured to run on multiple browsers. Modify `playwright.config.ts` to adjust browser settings:
   
   ```typescript
   projects: [
     { name: 'chromium-ar', use: { ...devices['Desktop Chrome'], locale: 'ar-EG' } },
     { name: 'firefox-en', use: { ...devices['Desktop Firefox'], locale: 'en-US' } },
     // ... other configurations
   ]
   ```

## 🚀 Running Tests

### Basic Commands

```bash
# Run all tests
npm run test:e2e

# Run tests in headed mode (visible browser)
npm run test:e2e:headed

# Run specific test file
npx playwright test resolution-lifecycle.spec.ts

# Run tests for specific browser
npx playwright test --project=chromium-ar

# Run tests in debug mode
npx playwright test --debug
```

### Advanced Commands

```bash
# Run tests with specific tag
npx playwright test --grep "@smoke"

# Run tests in parallel
npx playwright test --workers=4

# Run tests with video recording
npx playwright test --video=on

# Generate and open HTML report
npx playwright test --reporter=html
npx playwright show-report
```

### Environment-Specific Execution

```bash
# Local environment
TEST_ENV=local npx playwright test

# Test environment
TEST_ENV=test npx playwright test

# Production environment (read-only)
TEST_ENV=production npx playwright test --grep "@readonly"
```

## 📁 Test Structure

```
tests/e2e/
├── config/
│   └── environments.ts          # Environment configurations
├── fixtures/
│   └── test-data.ts            # Test data fixtures
├── page-objects/
│   ├── base.page.ts            # Base page object class
│   ├── login.page.ts           # Login page interactions
│   ├── dashboard.page.ts       # Dashboard page interactions
│   ├── funds.page.ts           # Funds management page
│   └── resolutions.page.ts     # Resolutions management page
├── setup/
│   ├── global.setup.ts         # Global test setup
│   └── global.cleanup.ts       # Global test cleanup
├── tests/
│   ├── authentication-and-rbac.spec.ts        # Auth and role tests
│   ├── resolution-lifecycle.spec.ts           # Resolution state machine
│   ├── resolution-alternative-workflows.spec.ts # Alternative scenarios
│   ├── board-member-management.spec.ts        # Board member CRUD
│   └── localization-and-error-handling.spec.ts # I18n and error tests
├── utils/
│   ├── auth.ts                 # Authentication utilities
│   └── test-setup.ts           # Test setup helpers
└── README.md                   # This file
```

## 📈 Reporting

### HTML Report
```bash
npx playwright test --reporter=html
npx playwright show-report
```

### JSON Report
```bash
npx playwright test --reporter=json --output-dir=test-results
```

### Custom Reports
The test suite generates additional reports:
- **Performance Report**: `test-results/performance-report.json`
- **Test Summary**: `test-results/test-summary.html`
- **Coverage Report**: Detailed coverage of user stories and workflows

### CI/CD Integration
```yaml
# Example GitHub Actions workflow
- name: Run E2E Tests
  run: |
    npm ci
    npx playwright install --with-deps
    npm run test:e2e
    
- name: Upload Test Results
  uses: actions/upload-artifact@v3
  if: always()
  with:
    name: playwright-report
    path: test-results/
```

## 🔍 Debugging

### Debug Mode
```bash
# Run specific test in debug mode
npx playwright test resolution-lifecycle.spec.ts --debug

# Debug with specific browser
npx playwright test --project=chromium-ar --debug
```

### Screenshots and Videos
```bash
# Enable screenshots on failure
npx playwright test --screenshot=only-on-failure

# Enable video recording
npx playwright test --video=retain-on-failure

# Enable tracing
npx playwright test --trace=retain-on-failure
```

### Verbose Logging
```bash
# Enable verbose logging
DEBUG=pw:api npx playwright test

# Enable browser console logs
npx playwright test --reporter=line --verbose
```

## 🛠️ Troubleshooting

### Common Issues

1. **Authentication Failures**
   ```bash
   # Clear stored auth states
   rm -rf tests/e2e/.auth/
   
   # Verify credentials in environment config
   # Check network connectivity to application
   ```

2. **Timeout Issues**
   ```bash
   # Increase timeout in playwright.config.ts
   timeout: 60000,
   expect: { timeout: 10000 }
   ```

3. **Localization Issues**
   ```bash
   # Verify browser locale settings
   # Check font rendering for Arabic text
   # Validate RTL/LTR layout switching
   ```

4. **Performance Issues**
   ```bash
   # Run tests with fewer workers
   npx playwright test --workers=1
   
   # Check system resources
   # Verify network latency to test environment
   ```

### Environment-Specific Issues

**Local Environment:**
- Ensure application is running on correct port
- Verify database is seeded with test data
- Check CORS settings for API calls

**Test Environment:**
- Validate environment URLs are accessible
- Confirm test user accounts are active
- Check firewall/VPN requirements

**Production Environment:**
- Use read-only test credentials
- Avoid data modification tests
- Monitor for rate limiting

### Getting Help

1. **Check Logs**: Review test execution logs and browser console
2. **Screenshots**: Examine failure screenshots in `test-results/`
3. **Video Recordings**: Watch test execution videos for visual debugging
4. **Trace Files**: Use Playwright trace viewer for detailed debugging

```bash
# Open trace viewer
npx playwright show-trace test-results/trace.zip
```

## 📝 Contributing

When adding new tests:

1. Follow the existing page object pattern
2. Add appropriate test data to fixtures
3. Include both Arabic and English language testing
4. Validate role-based access control
5. Add performance assertions where appropriate
6. Update this README with new test coverage

## 📄 License

This test suite is part of the Jadwa Fund Management System project and follows the same licensing terms.
