/**
 * Environment Configuration for Jadwa Fund Management System E2E Tests
 * 
 * This file contains environment-specific configurations for different
 * testing environments (local, test, production).
 */

export interface Environment {
  name: string;
  baseUrl: string;
  apiUrl: string;
  timeout: {
    default: number;
    navigation: number;
    api: number;
  };
  credentials: {
    fundManager: {
      username: string;
      password: string;
    };
    legalCouncil: {
      username: string;
      password: string;
    };
    boardSecretary: {
      username: string;
      password: string;
    };
    boardMember: {
      username: string;
      password: string;
    };
  };
  features: {
    enableNotifications: boolean;
    enableFileUpload: boolean;
    enableRealTimeUpdates: boolean;
  };
}

export const environments: Record<string, Environment> = {
  local: {
    name: 'local',
    baseUrl: 'https://localhost:4200',
    apiUrl: 'https://localhost:7010',
    timeout: {
      default: 30000,
      navigation: 30000,
      api: 15000
    },
    credentials: {
      fundManager: {
        username: '<EMAIL>',
        password: 'Test123!'
      },
      legalCouncil: {
        username: '<EMAIL>',
        password: 'Test123!'
      },
      boardSecretary: {
        username: '<EMAIL>',
        password: 'Test123!'
      },
      boardMember: {
        username: '<EMAIL>',
        password: 'Test123!'
      }
    },
    features: {
      enableNotifications: true,
      enableFileUpload: true,
      enableRealTimeUpdates: true
    }
  },

  test: {
    name: 'test',
    baseUrl: 'https://test.jadwa.com',
    apiUrl: 'https://************:7010',
    timeout: {
      default: 45000,
      navigation: 45000,
      api: 20000
    },
    credentials: {
      fundManager: {
        username: '<EMAIL>',
        password: 'TestEnv123!'
      },
      legalCouncil: {
        username: '<EMAIL>',
        password: 'TestEnv123!'
      },
      boardSecretary: {
        username: '<EMAIL>',
        password: 'TestEnv123!'
      },
      boardMember: {
        username: '<EMAIL>',
        password: 'TestEnv123!'
      }
    },
    features: {
      enableNotifications: true,
      enableFileUpload: true,
      enableRealTimeUpdates: true
    }
  },

  production: {
    name: 'production',
    baseUrl: 'https://jadwa.com',
    apiUrl: 'http://************:44301/',
    timeout: {
      default: 60000,
      navigation: 60000,
      api: 30000
    },
    credentials: {
      fundManager: {
        username: process.env.PROD_FUND_MANAGER_USERNAME || '',
        password: process.env.PROD_FUND_MANAGER_PASSWORD || ''
      },
      legalCouncil: {
        username: process.env.PROD_LEGAL_USERNAME || '',
        password: process.env.PROD_LEGAL_PASSWORD || ''
      },
      boardSecretary: {
        username: process.env.PROD_SECRETARY_USERNAME || '',
        password: process.env.PROD_SECRETARY_PASSWORD || ''
      },
      boardMember: {
        username: process.env.PROD_MEMBER_USERNAME || '',
        password: process.env.PROD_MEMBER_PASSWORD || ''
      }
    },
    features: {
      enableNotifications: true,
      enableFileUpload: true,
      enableRealTimeUpdates: true
    }
  }
};

/**
 * Get current environment configuration
 */
export function getCurrentEnvironment(): Environment {
  const envName = process.env.TEST_ENV || 'local';
  const environment = environments[envName];
  
  if (!environment) {
    throw new Error(`Environment '${envName}' not found. Available environments: ${Object.keys(environments).join(', ')}`);
  }
  
  return environment;
}

/**
 * Get environment-specific base URL
 */
export function getBaseUrl(): string {
  return getCurrentEnvironment().baseUrl;
}

/**
 * Get environment-specific API URL
 */
export function getApiUrl(): string {
  return getCurrentEnvironment().apiUrl;
}

/**
 * Get credentials for specific role
 */
export function getCredentials(role: 'fundManager' | 'legalCouncil' | 'boardSecretary' | 'boardMember') {
  return getCurrentEnvironment().credentials[role];
}

/**
 * Check if feature is enabled in current environment
 */
export function isFeatureEnabled(feature: keyof Environment['features']): boolean {
  return getCurrentEnvironment().features[feature];
}
