/**
 * Test Data Fixtures for Jadwa Fund Management System
 * 
 * This module contains test data fixtures with Arabic/English content
 * for comprehensive testing scenarios.
 */

export interface FundTestData {
  id?: number;
  name: {
    ar: string;
    en: string;
  };
  description: {
    ar: string;
    en: string;
  };
  strategy: string;
  exitDate: string;
  status: string;
  code?: string;
}

export interface ResolutionTestData {
  id?: number;
  code?: string;
  date: string;
  type: {
    ar: string;
    en: string;
  };
  description: {
    ar: string;
    en: string;
  };
  votingMethodology: string;
  memberVotingResult: string;
  status: string;
  fundId: number;
  items?: ResolutionItemTestData[];
}

export interface ResolutionItemTestData {
  title: {
    ar: string;
    en: string;
  };
  description: {
    ar: string;
    en: string;
  };
  hasConflict: boolean;
  conflictMembers?: string[];
}

export interface BoardMemberTestData {
  id?: number;
  userId: string;
  memberName: {
    ar: string;
    en: string;
  };
  memberType: 'independent' | 'dependent';
  isChairman: boolean;
  isActive: boolean;
  fundId: number;
}

export interface UserTestData {
  id: string;
  username: string;
  email: string;
  name: {
    ar: string;
    en: string;
  };
  role: string;
  permissions: string[];
}

// Sample Fund Data
export const sampleFunds: FundTestData[] = [
  {
    id: 1,
    name: {
      ar: 'صندوق جدوى للاستثمار المتوازن',
      en: 'Jadwa Balanced Investment Fund'
    },
    description: {
      ar: 'صندوق استثماري متوازن يهدف إلى تحقيق نمو رأس المال على المدى الطويل',
      en: 'A balanced investment fund aimed at achieving long-term capital growth'
    },
    strategy: 'Balanced Growth',
    exitDate: '2025-12-31',
    status: 'Active',
    code: 'JBF001'
  },
  {
    id: 2,
    name: {
      ar: 'صندوق جدوى للأسهم السعودية',
      en: 'Jadwa Saudi Equity Fund'
    },
    description: {
      ar: 'صندوق يستثمر في الأسهم السعودية المدرجة في السوق المالية السعودية',
      en: 'A fund that invests in Saudi equities listed on the Saudi Stock Exchange'
    },
    strategy: 'Equity Growth',
    exitDate: '2026-06-30',
    status: 'Active',
    code: 'JSE002'
  }
];

// Sample Resolution Data
export const sampleResolutions: ResolutionTestData[] = [
  {
    id: 1,
    code: 'JBF001/2024/001',
    date: '2024-01-15',
    type: {
      ar: 'استحواذ',
      en: 'Acquisition'
    },
    description: {
      ar: 'قرار بشأن الاستحواذ على شركة التقنية المتقدمة',
      en: 'Resolution regarding the acquisition of Advanced Technology Company'
    },
    votingMethodology: 'All Members',
    memberVotingResult: 'Majority',
    status: 'Draft',
    fundId: 1,
    items: [
      {
        title: {
          ar: 'الموافقة على سعر الاستحواذ',
          en: 'Approval of acquisition price'
        },
        description: {
          ar: 'الموافقة على السعر المقترح للاستحواذ البالغ 50 مليون ريال',
          en: 'Approval of the proposed acquisition price of 50 million SAR'
        },
        hasConflict: false
      },
      {
        title: {
          ar: 'تعيين مستشار قانوني',
          en: 'Appointment of legal advisor'
        },
        description: {
          ar: 'تعيين مكتب المحاماة المختص للإشراف على عملية الاستحواذ',
          en: 'Appointment of specialized law firm to oversee the acquisition process'
        },
        hasConflict: true,
        conflictMembers: ['member1', 'member2']
      }
    ]
  },
  {
    id: 2,
    code: 'JSE002/2024/001',
    date: '2024-02-01',
    type: {
      ar: 'توزيع أرباح',
      en: 'Dividend Distribution'
    },
    description: {
      ar: 'قرار بشأن توزيع أرباح الربع الأول من عام 2024',
      en: 'Resolution regarding Q1 2024 dividend distribution'
    },
    votingMethodology: 'Majority',
    memberVotingResult: 'All Items',
    status: 'Pending',
    fundId: 2
  }
];

// Sample Board Member Data
export const sampleBoardMembers: BoardMemberTestData[] = [
  {
    id: 1,
    userId: 'user001',
    memberName: {
      ar: 'أحمد محمد الأحمد',
      en: 'Ahmed Mohammed Al-Ahmed'
    },
    memberType: 'independent',
    isChairman: true,
    isActive: true,
    fundId: 1
  },
  {
    id: 2,
    userId: 'user002',
    memberName: {
      ar: 'فاطمة علي السالم',
      en: 'Fatima Ali Al-Salem'
    },
    memberType: 'independent',
    isChairman: false,
    isActive: true,
    fundId: 1
  },
  {
    id: 3,
    userId: 'user003',
    memberName: {
      ar: 'محمد عبدالله الخالد',
      en: 'Mohammed Abdullah Al-Khalid'
    },
    memberType: 'dependent',
    isChairman: false,
    isActive: true,
    fundId: 1
  }
];

// Sample User Data
export const sampleUsers: UserTestData[] = [
  {
    id: 'user001',
    username: '<EMAIL>',
    email: '<EMAIL>',
    name: {
      ar: 'مدير الصندوق الأول',
      en: 'Fund Manager One'
    },
    role: 'fundmanager',
    permissions: [
      'Fund.Create',
      'Fund.Edit',
      'Fund.View',
      'Resolution.Create',
      'Resolution.Edit',
      'Resolution.View',
      'BoardMember.View'
    ]
  },
  {
    id: 'user002',
    username: '<EMAIL>',
    email: '<EMAIL>',
    name: {
      ar: 'المستشار القانوني',
      en: 'Legal Counsel'
    },
    role: 'legalcouncil',
    permissions: [
      'Fund.Create',
      'Fund.Edit',
      'Fund.View',
      'Resolution.Complete',
      'Resolution.Edit',
      'Resolution.View',
      'BoardMember.Create',
      'BoardMember.Edit',
      'BoardMember.View'
    ]
  },
  {
    id: 'user003',
    username: '<EMAIL>',
    email: '<EMAIL>',
    name: {
      ar: 'أمين سر مجلس الإدارة',
      en: 'Board Secretary'
    },
    role: 'boardsecretary',
    permissions: [
      'Fund.View',
      'Resolution.Complete',
      'Resolution.Edit',
      'Resolution.View',
      'BoardMember.Create',
      'BoardMember.Edit',
      'BoardMember.View'
    ]
  },
  {
    id: 'user004',
    username: '<EMAIL>',
    email: '<EMAIL>',
    name: {
      ar: 'عضو مجلس الإدارة',
      en: 'Board Member'
    },
    role: 'boardmember',
    permissions: [
      'Fund.View',
      'Resolution.View',
      'Resolution.Vote',
      'BoardMember.View'
    ]
  }
];

// System Messages (MSG codes) for validation
export const systemMessages = {
  MSG001: {
    ar: 'حقل إلزامي',
    en: 'Required Field'
  },
  MSG002: {
    ar: 'تم حفظ البيانات بنجاح',
    en: 'Record Saved Successfully'
  },
  MSG003: {
    ar: 'حدث خطأ بالنظام , لم يتم حفظ البيانات',
    en: 'An error is occurred while saving data'
  },
  MSG004: {
    ar: 'تم تعديل تاريخ التخارج للصندوق',
    en: 'Fund exit date has been modified'
  },
  MSG005: {
    ar: 'تم تعيينك كمدير صندوق',
    en: 'You have been assigned as fund manager'
  },
  MSG006: {
    ar: 'هذا القرار في مرحلة التصويت, في حالة التعديل على البيانات سوف يتم إلغاء التصويت',
    en: 'This resolution voting is in progress, updating its data will cancel the vote'
  },
  MSG007: {
    ar: 'تم تحديث بيانات القرار مما يترتب عليه إلغاء التصويت الجارى',
    en: 'Resolution data updated which impacts voting cancellation'
  },
  MSG008: {
    ar: 'تحديث بيانات القرار المعتمد يترتب عليه إنشاء قرار جديد',
    en: 'Updating approved resolution initiates a new resolution'
  },
  MSG009: {
    ar: 'تم إضافة قرار جديد كتحديث على القرار السابق',
    en: 'A new resolution is added as an update to the previous resolution'
  }
};

// Test data generators
export class TestDataGenerator {
  static generateFund(overrides?: Partial<FundTestData>): FundTestData {
    const timestamp = Date.now();
    return {
      name: {
        ar: `صندوق اختبار ${timestamp}`,
        en: `Test Fund ${timestamp}`
      },
      description: {
        ar: `وصف صندوق الاختبار ${timestamp}`,
        en: `Test fund description ${timestamp}`
      },
      strategy: 'Test Strategy',
      exitDate: '2025-12-31',
      status: 'Active',
      ...overrides
    };
  }

  static generateResolution(fundId: number, overrides?: Partial<ResolutionTestData>): ResolutionTestData {
    const timestamp = Date.now();
    return {
      date: new Date().toISOString().split('T')[0],
      type: {
        ar: 'نوع قرار اختبار',
        en: 'Test Resolution Type'
      },
      description: {
        ar: `وصف قرار الاختبار ${timestamp}`,
        en: `Test resolution description ${timestamp}`
      },
      votingMethodology: 'All Members',
      memberVotingResult: 'Majority',
      status: 'Draft',
      fundId,
      ...overrides
    };
  }

  static generateBoardMember(fundId: number, overrides?: Partial<BoardMemberTestData>): BoardMemberTestData {
    const timestamp = Date.now();
    return {
      userId: `testuser${timestamp}`,
      memberName: {
        ar: `عضو اختبار ${timestamp}`,
        en: `Test Member ${timestamp}`
      },
      memberType: 'independent',
      isChairman: false,
      isActive: true,
      fundId,
      ...overrides
    };
  }
}
