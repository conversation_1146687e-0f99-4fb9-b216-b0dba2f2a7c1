/**
 * Dashboard Page Object for Jadwa Fund Management System
 * 
 * This page object handles all interactions with the main dashboard,
 * including navigation, fund overview, and notifications.
 */

import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './base.page';

export class DashboardPage extends BasePage {
  // Navigation elements
  private readonly sideMenu: Locator;
  private readonly fundsMenuItem: Locator;
  private readonly strategiesMenuItem: Locator;
  private readonly userMenu: Locator;
  private readonly logoutButton: Locator;
  private readonly languageToggle: Locator;

  // Dashboard content
  private readonly pageTitle: Locator;
  private readonly welcomeMessage: Locator;
  private readonly fundCards: Locator;
  private readonly notificationCounter: Locator;
  private readonly notificationPanel: Locator;

  // Fund overview cards
  private readonly totalFundsCard: Locator;
  private readonly activeFundsCard: Locator;
  private readonly pendingResolutionsCard: Locator;
  private readonly votingInProgressCard: Locator;

  // Quick actions
  private readonly createFundButton: Locator;
  private readonly viewAllFundsButton: Locator;
  private readonly viewNotificationsButton: Locator;

  constructor(page: Page) {
    super(page);
    
    // Initialize navigation locators
    this.sideMenu = page.locator('[data-testid="side-menu"]');
    this.fundsMenuItem = page.locator('[data-testid="funds-menu-item"]');
    this.strategiesMenuItem = page.locator('[data-testid="strategies-menu-item"]');
    this.userMenu = page.locator('[data-testid="user-menu"]');
    this.logoutButton = page.locator('[data-testid="logout-button"]');
    this.languageToggle = page.locator('[data-testid="language-toggle"]');

    // Initialize dashboard content locators
    this.pageTitle = page.locator('[data-testid="page-title"]');
    this.welcomeMessage = page.locator('[data-testid="welcome-message"]');
    this.fundCards = page.locator('[data-testid="fund-card"]');
    this.notificationCounter = page.locator('[data-testid="notification-counter"]');
    this.notificationPanel = page.locator('[data-testid="notification-panel"]');

    // Initialize overview cards
    this.totalFundsCard = page.locator('[data-testid="total-funds-card"]');
    this.activeFundsCard = page.locator('[data-testid="active-funds-card"]');
    this.pendingResolutionsCard = page.locator('[data-testid="pending-resolutions-card"]');
    this.votingInProgressCard = page.locator('[data-testid="voting-in-progress-card"]');

    // Initialize quick actions
    this.createFundButton = page.locator('[data-testid="create-fund-button"]');
    this.viewAllFundsButton = page.locator('[data-testid="view-all-funds-button"]');
    this.viewNotificationsButton = page.locator('[data-testid="view-notifications-button"]');
  }

  /**
   * Navigate to dashboard
   */
  async navigateToDashboard(): Promise<void> {
    await this.goto('/admin/dashboard');
    await this.waitForPageLoad();
  }

  /**
   * Verify dashboard is loaded
   */
  async verifyDashboardLoaded(): Promise<void> {
    await expect(this.pageTitle).toBeVisible();
    await expect(this.sideMenu).toBeVisible();
    await this.verifyUrlContains('/admin/dashboard');
  }

  /**
   * Verify welcome message for user
   */
  async verifyWelcomeMessage(userName?: string): Promise<void> {
    await expect(this.welcomeMessage).toBeVisible();
    
    if (userName) {
      await expect(this.welcomeMessage).toContainText(userName);
    }
  }

  /**
   * Navigate to funds page
   */
  async navigateToFunds(): Promise<void> {
    await this.fundsMenuItem.click();
    await this.waitForPageLoad();
    await this.verifyUrlContains('/admin/investment-funds');
  }

  /**
   * Navigate to fund strategies page
   */
  async navigateToStrategies(): Promise<void> {
    await this.strategiesMenuItem.click();
    await this.waitForPageLoad();
    await this.verifyUrlContains('/admin/fund-strategies');
  }

  /**
   * Open user menu
   */
  async openUserMenu(): Promise<void> {
    await this.userMenu.click();
  }

  /**
   * Logout from application
   */
  async logout(): Promise<void> {
    await this.openUserMenu();
    await this.logoutButton.click();
    await this.verifyUrlContains('/auth/login');
  }

  /**
   * Switch language
   */
  async switchLanguage(): Promise<void> {
    await this.languageToggle.click();
    await this.waitForPageLoad();
  }

  /**
   * Get total funds count
   */
  async getTotalFundsCount(): Promise<number> {
    const countText = await this.totalFundsCard.locator('[data-testid="count"]').textContent();
    return parseInt(countText || '0', 10);
  }

  /**
   * Get active funds count
   */
  async getActiveFundsCount(): Promise<number> {
    const countText = await this.activeFundsCard.locator('[data-testid="count"]').textContent();
    return parseInt(countText || '0', 10);
  }

  /**
   * Get pending resolutions count
   */
  async getPendingResolutionsCount(): Promise<number> {
    const countText = await this.pendingResolutionsCard.locator('[data-testid="count"]').textContent();
    return parseInt(countText || '0', 10);
  }

  /**
   * Get voting in progress count
   */
  async getVotingInProgressCount(): Promise<number> {
    const countText = await this.votingInProgressCard.locator('[data-testid="count"]').textContent();
    return parseInt(countText || '0', 10);
  }

  /**
   * Click create fund button
   */
  async clickCreateFund(): Promise<void> {
    await this.createFundButton.click();
    await this.waitForPageLoad();
  }

  /**
   * Click view all funds button
   */
  async clickViewAllFunds(): Promise<void> {
    await this.viewAllFundsButton.click();
    await this.waitForPageLoad();
  }

  /**
   * Get notification count
   */
  async getNotificationCount(): Promise<number> {
    const countText = await this.notificationCounter.textContent();
    return parseInt(countText || '0', 10);
  }

  /**
   * Open notifications panel
   */
  async openNotifications(): Promise<void> {
    await this.viewNotificationsButton.click();
    await expect(this.notificationPanel).toBeVisible();
  }

  /**
   * Close notifications panel
   */
  async closeNotifications(): Promise<void> {
    const closeButton = this.notificationPanel.locator('[data-testid="close-notifications"]');
    await closeButton.click();
    await expect(this.notificationPanel).toBeHidden();
  }

  /**
   * Get fund cards count
   */
  async getFundCardsCount(): Promise<number> {
    return await this.fundCards.count();
  }

  /**
   * Click on specific fund card
   */
  async clickFundCard(fundName: string): Promise<void> {
    const fundCard = this.fundCards.filter({ hasText: fundName }).first();
    await fundCard.click();
    await this.waitForPageLoad();
  }

  /**
   * Verify fund card exists
   */
  async verifyFundCardExists(fundName: string): Promise<void> {
    const fundCard = this.fundCards.filter({ hasText: fundName }).first();
    await expect(fundCard).toBeVisible();
  }

  /**
   * Verify dashboard statistics
   */
  async verifyDashboardStatistics(): Promise<void> {
    await expect(this.totalFundsCard).toBeVisible();
    await expect(this.activeFundsCard).toBeVisible();
    await expect(this.pendingResolutionsCard).toBeVisible();
    await expect(this.votingInProgressCard).toBeVisible();
  }

  /**
   * Verify user has access to create fund
   */
  async verifyCreateFundAccess(): Promise<void> {
    await expect(this.createFundButton).toBeVisible();
    await expect(this.createFundButton).toBeEnabled();
  }

  /**
   * Verify user does not have access to create fund
   */
  async verifyNoCreateFundAccess(): Promise<void> {
    await expect(this.createFundButton).toBeHidden();
  }

  /**
   * Verify side menu items based on user role
   */
  async verifySideMenuForRole(role: string): Promise<void> {
    await expect(this.sideMenu).toBeVisible();
    await expect(this.fundsMenuItem).toBeVisible();
    
    if (role === 'admin' || role === 'fundmanager') {
      await expect(this.strategiesMenuItem).toBeVisible();
    }
  }

  /**
   * Verify Arabic layout
   */
  async verifyArabicLayout(): Promise<void> {
    const bodyDir = await this.page.getAttribute('body', 'dir');
    expect(bodyDir).toBe('rtl');
    
    // Verify Arabic text in page title
    await expect(this.pageTitle).toContainText('لوحة التحكم');
  }

  /**
   * Verify English layout
   */
  async verifyEnglishLayout(): Promise<void> {
    const bodyDir = await this.page.getAttribute('body', 'dir');
    expect(bodyDir).toBe('ltr');
    
    // Verify English text in page title
    await expect(this.pageTitle).toContainText('Dashboard');
  }

  /**
   * Wait for dashboard data to load
   */
  async waitForDashboardDataLoad(): Promise<void> {
    await this.waitForLoadingToComplete();
    
    // Wait for statistics to be populated
    await this.page.waitForFunction(() => {
      const totalFunds = document.querySelector('[data-testid="total-funds-card"] [data-testid="count"]');
      return totalFunds && totalFunds.textContent !== '0';
    }, {}, { timeout: 10000 });
  }

  /**
   * Verify dashboard performance
   */
  async verifyDashboardPerformance(): Promise<void> {
    const metrics = await this.getPerformanceMetrics();
    
    // Dashboard should load quickly
    expect(metrics.domContentLoaded).toBeLessThan(2000); // 2 seconds
    expect(metrics.loadComplete).toBeLessThan(4000); // 4 seconds
  }

  /**
   * Verify responsive design
   */
  async verifyResponsiveDesign(): Promise<void> {
    // Test mobile view
    await this.page.setViewportSize({ width: 375, height: 667 });
    await this.verifyDashboardLoaded();
    
    // Test tablet view
    await this.page.setViewportSize({ width: 768, height: 1024 });
    await this.verifyDashboardLoaded();
    
    // Test desktop view
    await this.page.setViewportSize({ width: 1280, height: 720 });
    await this.verifyDashboardLoaded();
  }

  /**
   * Search for specific fund
   */
  async searchFund(fundName: string): Promise<void> {
    const searchInput = this.page.locator('[data-testid="fund-search"]');
    await searchInput.fill(fundName);
    await this.waitForLoadingToComplete();
  }

  /**
   * Clear fund search
   */
  async clearFundSearch(): Promise<void> {
    const searchInput = this.page.locator('[data-testid="fund-search"]');
    await searchInput.clear();
    await this.waitForLoadingToComplete();
  }
}
