/**
 * Funds Page Object for Jadwa Fund Management System
 * 
 * This page object handles all interactions with the funds list page,
 * including fund management, search, filtering, and navigation.
 */

import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './base.page';

export class FundsPage extends BasePage {
  // Page elements
  private readonly pageTitle: Locator;
  private readonly breadcrumb: Locator;
  private readonly createFundButton: Locator;
  private readonly searchInput: Locator;
  private readonly filterButton: Locator;
  private readonly fundCards: Locator;
  private readonly noFundsMessage: Locator;
  private readonly loadingSpinner: Locator;

  // Fund card elements
  private readonly fundCardTitle: Locator;
  private readonly fundCardStatus: Locator;
  private readonly fundCardStrategy: Locator;
  private readonly fundCardActions: Locator;
  private readonly viewDetailsButton: Locator;
  private readonly editFundButton: Locator;

  // Filter panel
  private readonly filterPanel: Locator;
  private readonly statusFilter: Locator;
  private readonly strategyFilter: Locator;
  private readonly dateRangeFilter: Locator;
  private readonly applyFiltersButton: Locator;
  private readonly clearFiltersButton: Locator;

  // Pagination
  private readonly paginationContainer: Locator;
  private readonly previousPageButton: Locator;
  private readonly nextPageButton: Locator;
  private readonly pageNumbers: Locator;

  constructor(page: Page) {
    super(page);
    
    // Initialize page elements
    this.pageTitle = page.locator('[data-testid="page-title"]');
    this.breadcrumb = page.locator('[data-testid="breadcrumb"]');
    this.createFundButton = page.locator('[data-testid="create-fund-button"]');
    this.searchInput = page.locator('[data-testid="fund-search-input"]');
    this.filterButton = page.locator('[data-testid="filter-button"]');
    this.fundCards = page.locator('[data-testid="fund-card"]');
    this.noFundsMessage = page.locator('[data-testid="no-funds-message"]');
    this.loadingSpinner = page.locator('[data-testid="loading-spinner"]');

    // Initialize fund card elements
    this.fundCardTitle = page.locator('[data-testid="fund-card-title"]');
    this.fundCardStatus = page.locator('[data-testid="fund-card-status"]');
    this.fundCardStrategy = page.locator('[data-testid="fund-card-strategy"]');
    this.fundCardActions = page.locator('[data-testid="fund-card-actions"]');
    this.viewDetailsButton = page.locator('[data-testid="view-details-button"]');
    this.editFundButton = page.locator('[data-testid="edit-fund-button"]');

    // Initialize filter panel
    this.filterPanel = page.locator('[data-testid="filter-panel"]');
    this.statusFilter = page.locator('[data-testid="status-filter"]');
    this.strategyFilter = page.locator('[data-testid="strategy-filter"]');
    this.dateRangeFilter = page.locator('[data-testid="date-range-filter"]');
    this.applyFiltersButton = page.locator('[data-testid="apply-filters-button"]');
    this.clearFiltersButton = page.locator('[data-testid="clear-filters-button"]');

    // Initialize pagination
    this.paginationContainer = page.locator('[data-testid="pagination"]');
    this.previousPageButton = page.locator('[data-testid="previous-page"]');
    this.nextPageButton = page.locator('[data-testid="next-page"]');
    this.pageNumbers = page.locator('[data-testid="page-number"]');
  }

  /**
   * Navigate to funds page
   */
  async navigateToFunds(): Promise<void> {
    await this.goto('/admin/investment-funds');
    await this.waitForPageLoad();
  }

  /**
   * Verify funds page is loaded
   */
  async verifyFundsPageLoaded(): Promise<void> {
    await expect(this.pageTitle).toBeVisible();
    await this.verifyUrlContains('/admin/investment-funds');
  }

  /**
   * Click create fund button
   */
  async clickCreateFund(): Promise<void> {
    await this.createFundButton.click();
    await this.waitForPageLoad();
  }

  /**
   * Search for funds
   */
  async searchFunds(searchTerm: string): Promise<void> {
    await this.searchInput.fill(searchTerm);
    await this.waitForLoadingToComplete();
  }

  /**
   * Clear search
   */
  async clearSearch(): Promise<void> {
    await this.searchInput.clear();
    await this.waitForLoadingToComplete();
  }

  /**
   * Open filter panel
   */
  async openFilterPanel(): Promise<void> {
    await this.filterButton.click();
    await expect(this.filterPanel).toBeVisible();
  }

  /**
   * Close filter panel
   */
  async closeFilterPanel(): Promise<void> {
    const closeButton = this.filterPanel.locator('[data-testid="close-filter"]');
    await closeButton.click();
    await expect(this.filterPanel).toBeHidden();
  }

  /**
   * Apply status filter
   */
  async filterByStatus(status: string): Promise<void> {
    await this.openFilterPanel();
    await this.statusFilter.selectOption(status);
    await this.applyFiltersButton.click();
    await this.waitForLoadingToComplete();
  }

  /**
   * Apply strategy filter
   */
  async filterByStrategy(strategy: string): Promise<void> {
    await this.openFilterPanel();
    await this.strategyFilter.selectOption(strategy);
    await this.applyFiltersButton.click();
    await this.waitForLoadingToComplete();
  }

  /**
   * Clear all filters
   */
  async clearAllFilters(): Promise<void> {
    await this.openFilterPanel();
    await this.clearFiltersButton.click();
    await this.waitForLoadingToComplete();
  }

  /**
   * Get fund cards count
   */
  async getFundCardsCount(): Promise<number> {
    return await this.fundCards.count();
  }

  /**
   * Get fund card by name
   */
  getFundCardByName(fundName: string): Locator {
    return this.fundCards.filter({ hasText: fundName }).first();
  }

  /**
   * Click fund card to view details
   */
  async clickFundCard(fundName: string): Promise<void> {
    const fundCard = this.getFundCardByName(fundName);
    await fundCard.click();
    await this.waitForPageLoad();
  }

  /**
   * Click view details button for specific fund
   */
  async clickViewDetails(fundName: string): Promise<void> {
    const fundCard = this.getFundCardByName(fundName);
    const detailsButton = fundCard.locator('[data-testid="view-details-button"]');
    await detailsButton.click();
    await this.waitForPageLoad();
  }

  /**
   * Click edit fund button for specific fund
   */
  async clickEditFund(fundName: string): Promise<void> {
    const fundCard = this.getFundCardByName(fundName);
    const editButton = fundCard.locator('[data-testid="edit-fund-button"]');
    await editButton.click();
    await this.waitForPageLoad();
  }

  /**
   * Verify fund card exists
   */
  async verifyFundCardExists(fundName: string): Promise<void> {
    const fundCard = this.getFundCardByName(fundName);
    await expect(fundCard).toBeVisible();
  }

  /**
   * Verify fund card does not exist
   */
  async verifyFundCardNotExists(fundName: string): Promise<void> {
    const fundCard = this.getFundCardByName(fundName);
    await expect(fundCard).toBeHidden();
  }

  /**
   * Get fund status from card
   */
  async getFundStatus(fundName: string): Promise<string> {
    const fundCard = this.getFundCardByName(fundName);
    const statusElement = fundCard.locator('[data-testid="fund-status"]');
    return await statusElement.textContent() || '';
  }

  /**
   * Get fund strategy from card
   */
  async getFundStrategy(fundName: string): Promise<string> {
    const fundCard = this.getFundCardByName(fundName);
    const strategyElement = fundCard.locator('[data-testid="fund-strategy"]');
    return await strategyElement.textContent() || '';
  }

  /**
   * Verify no funds message is displayed
   */
  async verifyNoFundsMessage(): Promise<void> {
    await expect(this.noFundsMessage).toBeVisible();
  }

  /**
   * Verify funds are displayed
   */
  async verifyFundsDisplayed(): Promise<void> {
    await expect(this.fundCards.first()).toBeVisible();
  }

  /**
   * Go to next page
   */
  async goToNextPage(): Promise<void> {
    await this.nextPageButton.click();
    await this.waitForLoadingToComplete();
  }

  /**
   * Go to previous page
   */
  async goToPreviousPage(): Promise<void> {
    await this.previousPageButton.click();
    await this.waitForLoadingToComplete();
  }

  /**
   * Go to specific page
   */
  async goToPage(pageNumber: number): Promise<void> {
    const pageButton = this.pageNumbers.filter({ hasText: pageNumber.toString() }).first();
    await pageButton.click();
    await this.waitForLoadingToComplete();
  }

  /**
   * Verify pagination is visible
   */
  async verifyPaginationVisible(): Promise<void> {
    await expect(this.paginationContainer).toBeVisible();
  }

  /**
   * Verify create fund button visibility based on permissions
   */
  async verifyCreateFundButtonVisibility(shouldBeVisible: boolean): Promise<void> {
    if (shouldBeVisible) {
      await expect(this.createFundButton).toBeVisible();
      await expect(this.createFundButton).toBeEnabled();
    } else {
      await expect(this.createFundButton).toBeHidden();
    }
  }

  /**
   * Verify fund card actions based on permissions
   */
  async verifyFundCardActions(fundName: string, canEdit: boolean): Promise<void> {
    const fundCard = this.getFundCardByName(fundName);
    const editButton = fundCard.locator('[data-testid="edit-fund-button"]');
    
    if (canEdit) {
      await expect(editButton).toBeVisible();
    } else {
      await expect(editButton).toBeHidden();
    }
  }

  /**
   * Verify funds are grouped by strategy
   */
  async verifyFundsGroupedByStrategy(): Promise<void> {
    const strategyGroups = this.page.locator('[data-testid="strategy-group"]');
    await expect(strategyGroups.first()).toBeVisible();
  }

  /**
   * Expand strategy group
   */
  async expandStrategyGroup(strategyName: string): Promise<void> {
    const strategyGroup = this.page.locator(`[data-testid="strategy-group"][data-strategy="${strategyName}"]`);
    const expandButton = strategyGroup.locator('[data-testid="expand-button"]');
    await expandButton.click();
  }

  /**
   * Collapse strategy group
   */
  async collapseStrategyGroup(strategyName: string): Promise<void> {
    const strategyGroup = this.page.locator(`[data-testid="strategy-group"][data-strategy="${strategyName}"]`);
    const collapseButton = strategyGroup.locator('[data-testid="collapse-button"]');
    await collapseButton.click();
  }

  /**
   * Verify search results
   */
  async verifySearchResults(searchTerm: string): Promise<void> {
    const fundCards = await this.fundCards.all();
    
    for (const card of fundCards) {
      const cardText = await card.textContent();
      expect(cardText?.toLowerCase()).toContain(searchTerm.toLowerCase());
    }
  }

  /**
   * Verify filter results
   */
  async verifyFilterResults(filterType: 'status' | 'strategy', filterValue: string): Promise<void> {
    const fundCards = await this.fundCards.all();
    
    for (const card of fundCards) {
      const filterElement = card.locator(`[data-testid="fund-${filterType}"]`);
      const elementText = await filterElement.textContent();
      expect(elementText).toContain(filterValue);
    }
  }

  /**
   * Verify Arabic layout
   */
  async verifyArabicLayout(): Promise<void> {
    const bodyDir = await this.page.getAttribute('body', 'dir');
    expect(bodyDir).toBe('rtl');
    
    // Verify Arabic text in page title
    await expect(this.pageTitle).toContainText('الصناديق');
  }

  /**
   * Verify English layout
   */
  async verifyEnglishLayout(): Promise<void> {
    const bodyDir = await this.page.getAttribute('body', 'dir');
    expect(bodyDir).toBe('ltr');
    
    // Verify English text in page title
    await expect(this.pageTitle).toContainText('Funds');
  }

  /**
   * Wait for funds to load
   */
  async waitForFundsToLoad(): Promise<void> {
    await this.waitForLoadingToComplete();
    
    // Wait for either funds to appear or no funds message
    await Promise.race([
      this.fundCards.first().waitFor({ state: 'visible' }),
      this.noFundsMessage.waitFor({ state: 'visible' })
    ]);
  }
}
