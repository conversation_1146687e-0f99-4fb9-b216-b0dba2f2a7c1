/**
 * Login Page Object for Jadwa Fund Management System
 * 
 * This page object handles all interactions with the login page,
 * including authentication, validation, and error handling.
 */

import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './base.page';

export class LoginPage extends BasePage {
  // Page elements
  private readonly usernameInput: Locator;
  private readonly passwordInput: Locator;
  private readonly loginButton: Locator;
  private readonly forgotPasswordLink: Locator;
  private readonly errorMessage: Locator;
  private readonly loadingSpinner: Locator;
  private readonly languageToggle: Locator;
  private readonly rememberMeCheckbox: Locator;
  private readonly loginForm: Locator;

  constructor(page: Page) {
    super(page);
    
    // Initialize locators
    this.usernameInput = page.locator('[data-testid="username-input"]');
    this.passwordInput = page.locator('[data-testid="password-input"]');
    this.loginButton = page.locator('[data-testid="login-button"]');
    this.forgotPasswordLink = page.locator('[data-testid="forgot-password-link"]');
    this.errorMessage = page.locator('[data-testid="error-message"]');
    this.loadingSpinner = page.locator('[data-testid="loading-spinner"]');
    this.languageToggle = page.locator('[data-testid="language-toggle"]');
    this.rememberMeCheckbox = page.locator('[data-testid="remember-me-checkbox"]');
    this.loginForm = page.locator('[data-testid="login-form"]');
  }

  /**
   * Navigate to login page
   */
  async navigateToLogin(): Promise<void> {
    await this.goto('/auth/login');
    await this.waitForPageLoad();
  }

  /**
   * Verify login page is loaded
   */
  async verifyLoginPageLoaded(): Promise<void> {
    await expect(this.loginForm).toBeVisible();
    await expect(this.usernameInput).toBeVisible();
    await expect(this.passwordInput).toBeVisible();
    await expect(this.loginButton).toBeVisible();
  }

  /**
   * Enter username
   */
  async enterUsername(username: string): Promise<void> {
    await this.usernameInput.clear();
    await this.usernameInput.fill(username);
  }

  /**
   * Enter password
   */
  async enterPassword(password: string): Promise<void> {
    await this.passwordInput.clear();
    await this.passwordInput.fill(password);
  }

  /**
   * Click login button
   */
  async clickLogin(): Promise<void> {
    await this.loginButton.click();
  }

  /**
   * Perform complete login process
   */
  async login(username: string, password: string): Promise<void> {
    await this.enterUsername(username);
    await this.enterPassword(password);
    await this.clickLogin();
  }

  /**
   * Login and wait for dashboard
   */
  async loginAndWaitForDashboard(username: string, password: string): Promise<void> {
    await this.login(username, password);
    await this.waitForSuccessfulLogin();
  }

  /**
   * Wait for successful login (redirect to dashboard)
   */
  async waitForSuccessfulLogin(): Promise<void> {
    await expect(this.page).toHaveURL(/\/admin\/dashboard/);
    await this.waitForPageLoad();
  }

  /**
   * Verify login error message
   */
  async verifyLoginError(expectedMessage?: string): Promise<void> {
    await expect(this.errorMessage).toBeVisible();
    
    if (expectedMessage) {
      await expect(this.errorMessage).toContainText(expectedMessage);
    }
  }

  /**
   * Check if login button is disabled
   */
  async isLoginButtonDisabled(): Promise<boolean> {
    return await this.loginButton.isDisabled();
  }

  /**
   * Check if loading spinner is visible
   */
  async isLoadingSpinnerVisible(): Promise<boolean> {
    return await this.loadingSpinner.isVisible();
  }

  /**
   * Toggle remember me checkbox
   */
  async toggleRememberMe(): Promise<void> {
    await this.rememberMeCheckbox.click();
  }

  /**
   * Check if remember me is checked
   */
  async isRememberMeChecked(): Promise<boolean> {
    return await this.rememberMeCheckbox.isChecked();
  }

  /**
   * Click forgot password link
   */
  async clickForgotPassword(): Promise<void> {
    await this.forgotPasswordLink.click();
  }

  /**
   * Switch language
   */
  async switchLanguage(): Promise<void> {
    await this.languageToggle.click();
    await this.waitForPageLoad();
  }

  /**
   * Verify page is in Arabic
   */
  async verifyArabicLanguage(): Promise<void> {
    // Check for Arabic text or RTL direction
    const bodyDir = await this.page.getAttribute('body', 'dir');
    expect(bodyDir).toBe('rtl');
    
    // Check for Arabic login button text
    await expect(this.loginButton).toContainText('تسجيل الدخول');
  }

  /**
   * Verify page is in English
   */
  async verifyEnglishLanguage(): Promise<void> {
    // Check for LTR direction
    const bodyDir = await this.page.getAttribute('body', 'dir');
    expect(bodyDir).toBe('ltr');
    
    // Check for English login button text
    await expect(this.loginButton).toContainText('Login');
  }

  /**
   * Verify username field validation
   */
  async verifyUsernameValidation(): Promise<void> {
    // Clear username and try to login
    await this.usernameInput.clear();
    await this.clickLogin();
    
    // Check for validation message
    const validationMessage = this.page.locator('[data-testid="username-validation"]');
    await expect(validationMessage).toBeVisible();
  }

  /**
   * Verify password field validation
   */
  async verifyPasswordValidation(): Promise<void> {
    // Clear password and try to login
    await this.passwordInput.clear();
    await this.clickLogin();
    
    // Check for validation message
    const validationMessage = this.page.locator('[data-testid="password-validation"]');
    await expect(validationMessage).toBeVisible();
  }

  /**
   * Verify invalid credentials error
   */
  async verifyInvalidCredentialsError(): Promise<void> {
    await this.verifyLoginError('Invalid username or password');
  }

  /**
   * Clear login form
   */
  async clearForm(): Promise<void> {
    await this.usernameInput.clear();
    await this.passwordInput.clear();
  }

  /**
   * Get username field value
   */
  async getUsernameValue(): Promise<string> {
    return await this.usernameInput.inputValue();
  }

  /**
   * Get password field value
   */
  async getPasswordValue(): Promise<string> {
    return await this.passwordInput.inputValue();
  }

  /**
   * Verify login form accessibility
   */
  async verifyAccessibility(): Promise<void> {
    // Check for proper labels
    await expect(this.usernameInput).toHaveAttribute('aria-label');
    await expect(this.passwordInput).toHaveAttribute('aria-label');
    
    // Check for proper form structure
    await expect(this.loginForm).toHaveAttribute('role', 'form');
  }

  /**
   * Verify login page performance
   */
  async verifyPerformance(): Promise<void> {
    const metrics = await this.getPerformanceMetrics();
    
    // Verify page loads within acceptable time
    expect(metrics.domContentLoaded).toBeLessThan(3000); // 3 seconds
    expect(metrics.loadComplete).toBeLessThan(5000); // 5 seconds
  }

  /**
   * Test login with different browsers
   */
  async testCrossBrowserCompatibility(): Promise<void> {
    // Verify all elements are visible and functional
    await this.verifyLoginPageLoaded();
    
    // Test form submission
    await this.enterUsername('<EMAIL>');
    await this.enterPassword('password');
    
    // Verify button is clickable
    await expect(this.loginButton).toBeEnabled();
  }

  /**
   * Test mobile responsiveness
   */
  async testMobileView(): Promise<void> {
    // Set mobile viewport
    await this.page.setViewportSize({ width: 375, height: 667 });
    
    // Verify elements are still visible and accessible
    await this.verifyLoginPageLoaded();
    
    // Test touch interactions
    await this.usernameInput.tap();
    await this.passwordInput.tap();
    await this.loginButton.tap();
  }

  /**
   * Verify security features
   */
  async verifySecurityFeatures(): Promise<void> {
    // Check password field type
    await expect(this.passwordInput).toHaveAttribute('type', 'password');
    
    // Verify form uses HTTPS
    const url = this.page.url();
    expect(url).toMatch(/^https:/);
    
    // Check for CSRF protection (if implemented)
    const csrfToken = this.page.locator('[name="_token"]');
    if (await csrfToken.isVisible()) {
      await expect(csrfToken).toHaveAttribute('value');
    }
  }
}
