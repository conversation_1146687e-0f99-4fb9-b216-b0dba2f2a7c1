/**
 * Resolutions Page Object for Jadwa Fund Management System
 * 
 * This page object handles all interactions with the resolutions page,
 * including resolution lifecycle management, voting, and state transitions.
 */

import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './base.page';

export class ResolutionsPage extends BasePage {
  // Page elements
  private readonly pageTitle: Locator;
  private readonly breadcrumb: Locator;
  private readonly createResolutionButton: Locator;
  private readonly resolutionCards: Locator;
  private readonly noResolutionsMessage: Locator;
  private readonly loadingSpinner: Locator;

  // Resolution card elements
  private readonly resolutionCode: Locator;
  private readonly resolutionStatus: Locator;
  private readonly resolutionType: Locator;
  private readonly resolutionDate: Locator;
  private readonly resolutionActions: Locator;

  // Action buttons
  private readonly viewDetailsButton: Locator;
  private readonly editButton: Locator;
  private readonly deleteButton: Locator;
  private readonly cancelButton: Locator;
  private readonly sendButton: Locator;
  private readonly completeButton: Locator;
  private readonly confirmButton: Locator;
  private readonly rejectButton: Locator;
  private readonly voteButton: Locator;

  // Filter and search
  private readonly statusFilter: Locator;
  private readonly typeFilter: Locator;
  private readonly searchInput: Locator;

  // Resolution details modal/page
  private readonly resolutionDetailsModal: Locator;
  private readonly resolutionDescription: Locator;
  private readonly resolutionItems: Locator;
  private readonly votingMethodology: Locator;
  private readonly memberVotingResult: Locator;
  private readonly resolutionFile: Locator;

  // Voting elements
  private readonly voteModal: Locator;
  private readonly voteYesButton: Locator;
  private readonly voteNoButton: Locator;
  private readonly voteAbstainButton: Locator;
  private readonly submitVoteButton: Locator;

  // Confirmation dialogs
  private readonly confirmationDialog: Locator;
  private readonly confirmYesButton: Locator;
  private readonly confirmNoButton: Locator;

  constructor(page: Page) {
    super(page);
    
    // Initialize page elements
    this.pageTitle = page.locator('[data-testid="page-title"]');
    this.breadcrumb = page.locator('[data-testid="breadcrumb"]');
    this.createResolutionButton = page.locator('[data-testid="create-resolution-button"]');
    this.resolutionCards = page.locator('[data-testid="resolution-card"]');
    this.noResolutionsMessage = page.locator('[data-testid="no-resolutions-message"]');
    this.loadingSpinner = page.locator('[data-testid="loading-spinner"]');

    // Initialize resolution card elements
    this.resolutionCode = page.locator('[data-testid="resolution-code"]');
    this.resolutionStatus = page.locator('[data-testid="resolution-status"]');
    this.resolutionType = page.locator('[data-testid="resolution-type"]');
    this.resolutionDate = page.locator('[data-testid="resolution-date"]');
    this.resolutionActions = page.locator('[data-testid="resolution-actions"]');

    // Initialize action buttons
    this.viewDetailsButton = page.locator('[data-testid="view-details-button"]');
    this.editButton = page.locator('[data-testid="edit-button"]');
    this.deleteButton = page.locator('[data-testid="delete-button"]');
    this.cancelButton = page.locator('[data-testid="cancel-button"]');
    this.sendButton = page.locator('[data-testid="send-button"]');
    this.completeButton = page.locator('[data-testid="complete-button"]');
    this.confirmButton = page.locator('[data-testid="confirm-button"]');
    this.rejectButton = page.locator('[data-testid="reject-button"]');
    this.voteButton = page.locator('[data-testid="vote-button"]');

    // Initialize filter and search
    this.statusFilter = page.locator('[data-testid="status-filter"]');
    this.typeFilter = page.locator('[data-testid="type-filter"]');
    this.searchInput = page.locator('[data-testid="search-input"]');

    // Initialize resolution details
    this.resolutionDetailsModal = page.locator('[data-testid="resolution-details-modal"]');
    this.resolutionDescription = page.locator('[data-testid="resolution-description"]');
    this.resolutionItems = page.locator('[data-testid="resolution-items"]');
    this.votingMethodology = page.locator('[data-testid="voting-methodology"]');
    this.memberVotingResult = page.locator('[data-testid="member-voting-result"]');
    this.resolutionFile = page.locator('[data-testid="resolution-file"]');

    // Initialize voting elements
    this.voteModal = page.locator('[data-testid="vote-modal"]');
    this.voteYesButton = page.locator('[data-testid="vote-yes"]');
    this.voteNoButton = page.locator('[data-testid="vote-no"]');
    this.voteAbstainButton = page.locator('[data-testid="vote-abstain"]');
    this.submitVoteButton = page.locator('[data-testid="submit-vote"]');

    // Initialize confirmation dialogs
    this.confirmationDialog = page.locator('[data-testid="confirmation-dialog"]');
    this.confirmYesButton = page.locator('[data-testid="confirm-yes"]');
    this.confirmNoButton = page.locator('[data-testid="confirm-no"]');
  }

  /**
   * Navigate to resolutions page for specific fund
   */
  async navigateToResolutions(fundId: number): Promise<void> {
    await this.goto(`/admin/investment-funds/${fundId}/resolutions`);
    await this.waitForPageLoad();
  }

  /**
   * Verify resolutions page is loaded
   */
  async verifyResolutionsPageLoaded(): Promise<void> {
    await expect(this.pageTitle).toBeVisible();
    await this.verifyUrlContains('/resolutions');
  }

  /**
   * Click create resolution button
   */
  async clickCreateResolution(): Promise<void> {
    await this.createResolutionButton.click();
    await this.waitForPageLoad();
  }

  /**
   * Get resolution card by code
   */
  getResolutionCardByCode(resolutionCode: string): Locator {
    return this.resolutionCards.filter({ hasText: resolutionCode }).first();
  }

  /**
   * Click resolution card to view details
   */
  async clickResolutionCard(resolutionCode: string): Promise<void> {
    const resolutionCard = this.getResolutionCardByCode(resolutionCode);
    await resolutionCard.click();
    await this.waitForPageLoad();
  }

  /**
   * Get resolution status
   */
  async getResolutionStatus(resolutionCode: string): Promise<string> {
    const resolutionCard = this.getResolutionCardByCode(resolutionCode);
    const statusElement = resolutionCard.locator('[data-testid="resolution-status"]');
    return await statusElement.textContent() || '';
  }

  /**
   * Edit resolution
   */
  async editResolution(resolutionCode: string): Promise<void> {
    const resolutionCard = this.getResolutionCardByCode(resolutionCode);
    const editButton = resolutionCard.locator('[data-testid="edit-button"]');
    await editButton.click();
    await this.waitForPageLoad();
  }

  /**
   * Delete resolution (draft only)
   */
  async deleteResolution(resolutionCode: string): Promise<void> {
    const resolutionCard = this.getResolutionCardByCode(resolutionCode);
    const deleteButton = resolutionCard.locator('[data-testid="delete-button"]');
    await deleteButton.click();
    
    // Confirm deletion
    await this.confirmAction();
  }

  /**
   * Cancel resolution (pending only)
   */
  async cancelResolution(resolutionCode: string): Promise<void> {
    const resolutionCard = this.getResolutionCardByCode(resolutionCode);
    const cancelButton = resolutionCard.locator('[data-testid="cancel-button"]');
    await cancelButton.click();
    
    // Confirm cancellation
    await this.confirmAction();
  }

  /**
   * Send resolution for approval
   */
  async sendResolution(resolutionCode: string): Promise<void> {
    const resolutionCard = this.getResolutionCardByCode(resolutionCode);
    const sendButton = resolutionCard.locator('[data-testid="send-button"]');
    await sendButton.click();
    await this.waitForLoadingToComplete();
  }

  /**
   * Complete resolution data
   */
  async completeResolution(resolutionCode: string): Promise<void> {
    const resolutionCard = this.getResolutionCardByCode(resolutionCode);
    const completeButton = resolutionCard.locator('[data-testid="complete-button"]');
    await completeButton.click();
    await this.waitForPageLoad();
  }

  /**
   * Confirm resolution
   */
  async confirmResolution(resolutionCode: string): Promise<void> {
    const resolutionCard = this.getResolutionCardByCode(resolutionCode);
    const confirmButton = resolutionCard.locator('[data-testid="confirm-button"]');
    await confirmButton.click();
    await this.waitForLoadingToComplete();
  }

  /**
   * Reject resolution
   */
  async rejectResolution(resolutionCode: string): Promise<void> {
    const resolutionCard = this.getResolutionCardByCode(resolutionCode);
    const rejectButton = resolutionCard.locator('[data-testid="reject-button"]');
    await rejectButton.click();
    await this.waitForLoadingToComplete();
  }

  /**
   * Vote on resolution
   */
  async voteOnResolution(resolutionCode: string, vote: 'yes' | 'no' | 'abstain'): Promise<void> {
    const resolutionCard = this.getResolutionCardByCode(resolutionCode);
    const voteButton = resolutionCard.locator('[data-testid="vote-button"]');
    await voteButton.click();
    
    // Wait for vote modal
    await expect(this.voteModal).toBeVisible();
    
    // Select vote
    switch (vote) {
      case 'yes':
        await this.voteYesButton.click();
        break;
      case 'no':
        await this.voteNoButton.click();
        break;
      case 'abstain':
        await this.voteAbstainButton.click();
        break;
    }
    
    // Submit vote
    await this.submitVoteButton.click();
    await this.waitForLoadingToComplete();
  }

  /**
   * Filter resolutions by status
   */
  async filterByStatus(status: string): Promise<void> {
    await this.statusFilter.selectOption(status);
    await this.waitForLoadingToComplete();
  }

  /**
   * Filter resolutions by type
   */
  async filterByType(type: string): Promise<void> {
    await this.typeFilter.selectOption(type);
    await this.waitForLoadingToComplete();
  }

  /**
   * Search resolutions
   */
  async searchResolutions(searchTerm: string): Promise<void> {
    await this.searchInput.fill(searchTerm);
    await this.waitForLoadingToComplete();
  }

  /**
   * Verify resolution exists
   */
  async verifyResolutionExists(resolutionCode: string): Promise<void> {
    const resolutionCard = this.getResolutionCardByCode(resolutionCode);
    await expect(resolutionCard).toBeVisible();
  }

  /**
   * Verify resolution status
   */
  async verifyResolutionStatus(resolutionCode: string, expectedStatus: string): Promise<void> {
    const status = await this.getResolutionStatus(resolutionCode);
    expect(status).toContain(expectedStatus);
  }

  /**
   * Verify resolution actions based on status and role
   */
  async verifyResolutionActions(
    resolutionCode: string, 
    expectedActions: string[]
  ): Promise<void> {
    const resolutionCard = this.getResolutionCardByCode(resolutionCode);
    
    for (const action of expectedActions) {
      const actionButton = resolutionCard.locator(`[data-testid="${action}-button"]`);
      await expect(actionButton).toBeVisible();
    }
  }

  /**
   * Verify no resolutions message
   */
  async verifyNoResolutionsMessage(): Promise<void> {
    await expect(this.noResolutionsMessage).toBeVisible();
  }

  /**
   * Confirm action in dialog
   */
  async confirmAction(): Promise<void> {
    await expect(this.confirmationDialog).toBeVisible();
    await this.confirmYesButton.click();
    await this.waitForLoadingToComplete();
  }

  /**
   * Cancel action in dialog
   */
  async cancelAction(): Promise<void> {
    await expect(this.confirmationDialog).toBeVisible();
    await this.confirmNoButton.click();
  }

  /**
   * Verify system message
   */
  async verifySystemMessage(messageCode: string): Promise<void> {
    const messageElement = this.page.locator(`[data-testid="message-${messageCode}"]`);
    await expect(messageElement).toBeVisible();
  }

  /**
   * Wait for resolution state change
   */
  async waitForResolutionStateChange(
    resolutionCode: string, 
    expectedStatus: string
  ): Promise<void> {
    await this.page.waitForFunction(
      ({ code, status }) => {
        const card = document.querySelector(`[data-testid="resolution-card"]:has-text("${code}")`);
        if (!card) return false;
        
        const statusElement = card.querySelector('[data-testid="resolution-status"]');
        return statusElement?.textContent?.includes(status) || false;
      },
      { resolutionCode, expectedStatus },
      { timeout: 30000 }
    );
  }

  /**
   * Verify voting suspension workflow (Alternative 1)
   */
  async verifyVotingSuspensionWorkflow(resolutionCode: string): Promise<void> {
    // Verify resolution is in voting progress
    await this.verifyResolutionStatus(resolutionCode, 'Voting in Progress');
    
    // Edit resolution to trigger suspension
    await this.editResolution(resolutionCode);
    
    // Verify MSG006 confirmation message
    await this.verifySystemMessage('MSG006');
    
    // Confirm suspension
    await this.confirmAction();
    
    // Verify MSG007 notification
    await this.verifySystemMessage('MSG007');
    
    // Verify status changed to waiting for confirmation
    await this.waitForResolutionStateChange(resolutionCode, 'Waiting for Confirmation');
  }

  /**
   * Verify new resolution from approved workflow (Alternative 2)
   */
  async verifyNewResolutionFromApprovedWorkflow(resolutionCode: string): Promise<void> {
    // Verify resolution is approved/not approved
    const status = await this.getResolutionStatus(resolutionCode);
    expect(['Approved', 'Not Approved']).toContain(status);
    
    // Edit resolution to create new one
    await this.editResolution(resolutionCode);
    
    // Verify MSG008 confirmation message
    await this.verifySystemMessage('MSG008');
    
    // Confirm creation of new resolution
    await this.confirmAction();
    
    // Verify MSG009 notification
    await this.verifySystemMessage('MSG009');
  }

  /**
   * Get resolution cards count
   */
  async getResolutionCardsCount(): Promise<number> {
    return await this.resolutionCards.count();
  }

  /**
   * Wait for resolutions to load
   */
  async waitForResolutionsToLoad(): Promise<void> {
    await this.waitForLoadingToComplete();
    
    // Wait for either resolutions to appear or no resolutions message
    await Promise.race([
      this.resolutionCards.first().waitFor({ state: 'visible' }),
      this.noResolutionsMessage.waitFor({ state: 'visible' })
    ]);
  }
}
