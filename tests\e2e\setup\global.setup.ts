/**
 * Global Setup for Jadwa Fund Management System E2E Tests
 * 
 * This file handles global test setup including:
 * - Environment validation
 * - Database seeding
 * - Authentication state preparation
 * - Test data initialization
 */

import { test as setup, expect } from '@playwright/test';
import { getCurrentEnvironment, getCredentials } from '../config/environments';
import { LoginPage } from '../page-objects/login.page';
import { DashboardPage } from '../page-objects/dashboard.page';

const authFile = 'tests/e2e/.auth/user.json';

setup('authenticate users and prepare test environment', async ({ page }) => {
  const environment = getCurrentEnvironment();
  console.log(`Setting up tests for environment: ${environment.name}`);

  // Validate environment configuration
  console.log('Validating environment configuration...');
  expect(environment.baseUrl).toBeTruthy();
  expect(environment.apiUrl).toBeTruthy();
  expect(environment.credentials.fundManager.username).toBeTruthy();
  expect(environment.credentials.legalCouncil.username).toBeTruthy();
  expect(environment.credentials.boardSecretary.username).toBeTruthy();
  expect(environment.credentials.boardMember.username).toBeTruthy();

  // Test application availability
  console.log('Testing application availability...');
  const response = await page.request.get(environment.baseUrl);
  expect(response.status()).toBeLessThan(400);

  // Test API availability
  console.log('Testing API availability...');
  try {
    const apiResponse = await page.request.get(`${environment.apiUrl}/health`);
    if (apiResponse.status() !== 404) { // Health endpoint might not exist
      expect(apiResponse.status()).toBeLessThan(500);
    }
  } catch (error) {
    console.warn('API health check failed, continuing with setup...');
  }

  // Authenticate each user role and save auth states
  const roles = ['fundManager', 'legalCouncil', 'boardSecretary', 'boardMember'] as const;
  
  for (const role of roles) {
    console.log(`Authenticating ${role}...`);
    
    const credentials = getCredentials(role);
    const loginPage = new LoginPage(page);
    const dashboardPage = new DashboardPage(page);

    try {
      // Navigate to login page
      await loginPage.navigateToLogin();
      await loginPage.verifyLoginPageLoaded();

      // Perform login
      await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);
      
      // Verify successful login
      await dashboardPage.verifyDashboardLoaded();

      // Save authentication state for this role
      await page.context().storageState({ 
        path: `tests/e2e/.auth/${role}.json` 
      });

      console.log(`✓ ${role} authentication successful`);

      // Logout to prepare for next role
      await dashboardPage.logout();
      
    } catch (error) {
      console.error(`✗ Failed to authenticate ${role}:`, error);
      throw error;
    }
  }

  console.log('Global setup completed successfully');
});

setup('seed test database', async ({ page }) => {
  const environment = getCurrentEnvironment();
  
  if (environment.name === 'production') {
    console.log('Skipping database seeding in production environment');
    return;
  }

  console.log('Seeding test database...');

  try {
    // This would typically call a backend API endpoint to seed test data
    // For now, we'll create a basic setup through the UI

    const credentials = getCredentials('legalCouncil');
    const loginPage = new LoginPage(page);
    const dashboardPage = new DashboardPage(page);

    // Login as legal council (has full permissions)
    await loginPage.navigateToLogin();
    await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);

    // Create a test fund if none exists
    await page.goto('/admin/investment-funds');
    
    const existingFunds = await page.locator('[data-testid="fund-card"]').count();
    
    if (existingFunds === 0) {
      console.log('Creating initial test fund...');
      
      await page.click('[data-testid="create-fund-button"]');
      
      // Fill fund form with test data
      await page.fill('[data-testid="fund-name-ar"]', 'صندوق اختبار النظام');
      await page.fill('[data-testid="fund-name-en"]', 'System Test Fund');
      await page.fill('[data-testid="fund-description-ar"]', 'صندوق للاختبار الآلي للنظام');
      await page.fill('[data-testid="fund-description-en"]', 'Fund for automated system testing');
      await page.selectOption('[data-testid="fund-strategy"]', 'Test Strategy');
      await page.fill('[data-testid="fund-exit-date"]', '2025-12-31');
      
      await page.click('[data-testid="save-fund-button"]');
      
      // Wait for success message
      await page.waitForSelector('[data-testid="success-message"]');
      
      console.log('✓ Test fund created successfully');
    } else {
      console.log(`✓ Found ${existingFunds} existing funds, skipping creation`);
    }

    await dashboardPage.logout();
    
  } catch (error) {
    console.error('Database seeding failed:', error);
    // Don't throw error as tests might still work with existing data
  }

  console.log('Database seeding completed');
});

setup('validate test data integrity', async ({ page }) => {
  console.log('Validating test data integrity...');

  const credentials = getCredentials('fundManager');
  const loginPage = new LoginPage(page);
  const dashboardPage = new DashboardPage(page);

  try {
    // Login and check basic data availability
    await loginPage.navigateToLogin();
    await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);

    // Verify dashboard loads with data
    await dashboardPage.verifyDashboardLoaded();
    
    // Check if funds are available
    await page.goto('/admin/investment-funds');
    await page.waitForLoadState('networkidle');
    
    const fundsAvailable = await page.locator('[data-testid="fund-card"], [data-testid="no-funds-message"]').count();
    expect(fundsAvailable).toBeGreaterThan(0);

    // Verify user roles and permissions
    const userRoles = await page.evaluate(() => {
      const token = localStorage.getItem('auth_token');
      if (!token) return [];
      
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        return payload['http://schemas.microsoft.com/ws/2008/06/identity/claims/role'] || [];
      } catch {
        return [];
      }
    });

    expect(userRoles).toContain('fundmanager');

    await dashboardPage.logout();
    
    console.log('✓ Test data integrity validation passed');
    
  } catch (error) {
    console.error('Test data integrity validation failed:', error);
    throw error;
  }
});

setup('performance baseline measurement', async ({ page }) => {
  console.log('Measuring performance baselines...');

  const credentials = getCredentials('fundManager');
  const loginPage = new LoginPage(page);
  const dashboardPage = new DashboardPage(page);

  try {
    // Measure login performance
    const loginStart = Date.now();
    await loginPage.navigateToLogin();
    await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);
    const loginTime = Date.now() - loginStart;

    // Measure dashboard load performance
    const dashboardStart = Date.now();
    await dashboardPage.verifyDashboardLoaded();
    const dashboardTime = Date.now() - dashboardStart;

    // Measure funds page load performance
    const fundsStart = Date.now();
    await page.goto('/admin/investment-funds');
    await page.waitForLoadState('networkidle');
    const fundsTime = Date.now() - fundsStart;

    // Log performance metrics
    console.log('Performance Baselines:');
    console.log(`  Login: ${loginTime}ms`);
    console.log(`  Dashboard: ${dashboardTime}ms`);
    console.log(`  Funds Page: ${fundsTime}ms`);

    // Set reasonable performance expectations
    expect(loginTime).toBeLessThan(10000); // 10 seconds max for login
    expect(dashboardTime).toBeLessThan(5000); // 5 seconds max for dashboard
    expect(fundsTime).toBeLessThan(8000); // 8 seconds max for funds page

    await dashboardPage.logout();
    
    console.log('✓ Performance baseline measurement completed');
    
  } catch (error) {
    console.error('Performance baseline measurement failed:', error);
    // Don't throw error as this is not critical for test execution
  }
});

setup('browser compatibility check', async ({ page, browserName }) => {
  console.log(`Checking browser compatibility for ${browserName}...`);

  const credentials = getCredentials('fundManager');
  const loginPage = new LoginPage(page);

  try {
    // Test basic functionality in current browser
    await loginPage.navigateToLogin();
    await loginPage.verifyLoginPageLoaded();

    // Test Arabic text rendering
    await loginPage.switchLanguage();
    await loginPage.verifyArabicLanguage();

    // Test form interactions
    await loginPage.enterUsername('<EMAIL>');
    await loginPage.enterPassword('password');

    // Verify input values are preserved
    const usernameValue = await loginPage.getUsernameValue();
    const passwordValue = await loginPage.getPasswordValue();
    
    expect(usernameValue).toBe('<EMAIL>');
    expect(passwordValue).toBe('password');

    // Test English text rendering
    await loginPage.switchLanguage();
    await loginPage.verifyEnglishLanguage();

    console.log(`✓ Browser compatibility check passed for ${browserName}`);
    
  } catch (error) {
    console.error(`Browser compatibility check failed for ${browserName}:`, error);
    throw error;
  }
});

// Export setup configuration
export const setupConfig = {
  timeout: 60000, // 1 minute timeout for setup
  retries: 2, // Retry setup steps twice on failure
  parallel: false // Run setup steps sequentially
};
