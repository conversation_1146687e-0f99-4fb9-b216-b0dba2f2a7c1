/**
 * Authentication and Role-Based Access Control Tests for Jadwa Fund Management System
 * 
 * This test suite covers:
 * - Authentication flow with JWT token management
 * - Role-based access control for all 4 user roles
 * - Fund-specific permissions and access restrictions
 * - Session management and security features
 * - Permission validation across different features
 */

import { test, expect } from '@playwright/test';
import { LoginPage } from '../page-objects/login.page';
import { DashboardPage } from '../page-objects/dashboard.page';
import { FundsPage } from '../page-objects/funds.page';
import { ResolutionsPage } from '../page-objects/resolutions.page';
import { loginAs, logout, hasPermission, hasRole } from '../utils/auth';
import { getCurrentEnvironment, getCredentials } from '../config/environments';
import { setupTest, cleanupTest, TestContext } from '../utils/test-setup';

test.describe('Authentication and Role-Based Access Control', () => {
  let loginPage: LoginPage;
  let dashboardPage: DashboardPage;
  let fundsPage: FundsPage;
  let resolutionsPage: ResolutionsPage;
  let testContext: TestContext;

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    dashboardPage = new DashboardPage(page);
    fundsPage = new FundsPage(page);
    resolutionsPage = new ResolutionsPage(page);
  });

  test.afterEach(async () => {
    if (testContext) {
      await cleanupTest(testContext);
    }
  });

  test.describe('Authentication Flow', () => {
    test('Successful Login with Valid Credentials', async ({ page }) => {
      const credentials = getCredentials('fundManager');
      
      await loginPage.navigateToLogin();
      await loginPage.verifyLoginPageLoaded();
      
      await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);
      
      // Verify successful login
      await dashboardPage.verifyDashboardLoaded();
      await dashboardPage.verifyWelcomeMessage();
      
      // Verify JWT token is stored
      const token = await page.evaluate(() => localStorage.getItem('auth_token'));
      expect(token).toBeTruthy();
      
      // Verify token structure (JWT format)
      expect(token).toMatch(/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/);
    });

    test('Failed Login with Invalid Credentials', async ({ page }) => {
      await loginPage.navigateToLogin();
      
      await loginPage.login('<EMAIL>', 'wrongpassword');
      
      // Verify error message is displayed
      await loginPage.verifyInvalidCredentialsError();
      
      // Verify user remains on login page
      await expect(page).toHaveURL(/\/auth\/login/);
      
      // Verify no token is stored
      const token = await page.evaluate(() => localStorage.getItem('auth_token'));
      expect(token).toBeFalsy();
    });

    test('Login Form Validation', async ({ page }) => {
      await loginPage.navigateToLogin();
      
      // Test empty username
      await loginPage.verifyUsernameValidation();
      
      // Test empty password
      await loginPage.verifyPasswordValidation();
      
      // Test both empty
      await loginPage.clearForm();
      await loginPage.clickLogin();
      
      // Verify validation messages
      await expect(page.locator('[data-testid="username-validation"]')).toBeVisible();
      await expect(page.locator('[data-testid="password-validation"]')).toBeVisible();
    });

    test('Logout Functionality', async ({ page }) => {
      testContext = await setupTest(page, 'fundManager');
      
      // Verify user is logged in
      await dashboardPage.verifyDashboardLoaded();
      
      // Logout
      await dashboardPage.logout();
      
      // Verify redirect to login page
      await expect(page).toHaveURL(/\/auth\/login/);
      
      // Verify token is cleared
      const token = await page.evaluate(() => localStorage.getItem('auth_token'));
      expect(token).toBeFalsy();
    });

    test('Session Timeout Handling', async ({ page }) => {
      testContext = await setupTest(page, 'fundManager');
      
      // Simulate expired token
      await page.evaluate(() => {
        const expiredToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.invalid';
        localStorage.setItem('auth_token', expiredToken);
      });
      
      // Try to access protected page
      await page.goto('/admin/dashboard');
      
      // Verify redirect to login page
      await expect(page).toHaveURL(/\/auth\/login/);
      
      // Verify session timeout message
      await expect(page.locator('[data-testid="session-timeout-message"]')).toBeVisible();
    });

    test('Remember Me Functionality', async ({ page }) => {
      await loginPage.navigateToLogin();
      
      const credentials = getCredentials('fundManager');
      
      // Enable remember me
      await loginPage.toggleRememberMe();
      await expect(loginPage.isRememberMeChecked()).resolves.toBe(true);
      
      await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);
      
      // Close browser and reopen (simulate)
      await page.context().close();
      const newContext = await page.context().browser()!.newContext();
      const newPage = await newContext.newPage();
      
      // Navigate to protected page
      await newPage.goto('/admin/dashboard');
      
      // Verify user is still logged in (if remember me is implemented)
      // This test depends on the actual implementation of remember me functionality
    });
  });

  test.describe('Fund Manager Role Access Control', () => {
    test('Fund Manager: Dashboard and Navigation Access', async ({ page }) => {
      testContext = await setupTest(page, 'fundManager');
      
      await dashboardPage.verifyDashboardLoaded();
      
      // Verify fund manager can access dashboard
      await dashboardPage.verifyDashboardStatistics();
      
      // Verify navigation menu items
      await dashboardPage.verifySideMenuForRole('fundmanager');
      
      // Verify fund manager can create funds
      await dashboardPage.verifyCreateFundAccess();
    });

    test('Fund Manager: Fund Management Permissions', async ({ page }) => {
      testContext = await setupTest(page, 'fundManager', {
        createFund: true
      });
      
      await fundsPage.navigateToFunds();
      
      // Verify fund manager can view funds
      await fundsPage.verifyFundsDisplayed();
      
      // Verify fund manager can create funds
      await fundsPage.verifyCreateFundButtonVisibility(true);
      
      // Verify fund manager can edit funds
      const fundName = testContext.testData.funds[0].name.ar;
      await fundsPage.verifyFundCardActions(fundName, true);
      
      // Verify permissions in token
      const canCreateFund = await hasPermission(page, 'Fund.Create');
      const canEditFund = await hasPermission(page, 'Fund.Edit');
      expect(canCreateFund).toBe(true);
      expect(canEditFund).toBe(true);
    });

    test('Fund Manager: Resolution Management Permissions', async ({ page }) => {
      testContext = await setupTest(page, 'fundManager', {
        createFund: true,
        createResolution: true
      });
      
      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;
      
      await resolutionsPage.navigateToResolutions(fundId);
      
      // Verify fund manager can create resolutions
      await expect(page.locator('[data-testid="create-resolution-button"]')).toBeVisible();
      
      // Verify fund manager can edit draft resolutions
      await resolutionsPage.verifyResolutionActions(resolutionCode, ['edit', 'send']);
      
      // Verify fund manager cannot complete resolutions (legal council only)
      await expect(page.locator(`[data-testid="resolution-card"]:has-text("${resolutionCode}") [data-testid="complete-button"]`))
        .toBeHidden();
    });

    test('Fund Manager: Board Member View-Only Access', async ({ page }) => {
      testContext = await setupTest(page, 'fundManager', {
        createFund: true,
        createBoardMembers: true
      });
      
      const fundId = testContext.testData.funds[0].id!;
      
      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');
      
      // Verify fund manager can view members
      await expect(page.locator('[data-testid="member-card"]')).toBeVisible();
      
      // Verify fund manager cannot add/edit/delete members
      await expect(page.locator('[data-testid="add-member-button"]')).toBeHidden();
      await expect(page.locator('[data-testid="edit-member-button"]')).toBeHidden();
      await expect(page.locator('[data-testid="delete-member-button"]')).toBeHidden();
    });
  });

  test.describe('Legal Council Role Access Control', () => {
    test('Legal Council: Full Fund and Resolution Access', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true,
        createResolution: true
      });
      
      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;
      
      // Verify legal council can manage funds
      await fundsPage.navigateToFunds();
      await fundsPage.verifyCreateFundButtonVisibility(true);
      
      // Verify legal council can complete resolutions
      await resolutionsPage.navigateToResolutions(fundId);
      await resolutionsPage.verifyResolutionActions(resolutionCode, ['edit', 'complete']);
      
      // Verify permissions
      const canCompleteFund = await hasPermission(page, 'Fund.Complete');
      const canCompleteResolution = await hasPermission(page, 'Resolution.Complete');
      expect(canCompleteFund).toBe(true);
      expect(canCompleteResolution).toBe(true);
    });

    test('Legal Council: Board Member Management Access', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true
      });
      
      const fundId = testContext.testData.funds[0].id!;
      
      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');
      
      // Verify legal council can manage board members
      await expect(page.locator('[data-testid="add-member-button"]')).toBeVisible();
      
      // Verify CRUD permissions
      const canCreateMember = await hasPermission(page, 'BoardMember.Create');
      const canEditMember = await hasPermission(page, 'BoardMember.Edit');
      expect(canCreateMember).toBe(true);
      expect(canEditMember).toBe(true);
    });
  });

  test.describe('Board Secretary Role Access Control', () => {
    test('Board Secretary: Resolution and Member Management', async ({ page }) => {
      testContext = await setupTest(page, 'boardSecretary', {
        createFund: true,
        createResolution: true
      });
      
      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;
      
      // Verify board secretary can complete resolutions
      await resolutionsPage.navigateToResolutions(fundId);
      await resolutionsPage.verifyResolutionActions(resolutionCode, ['edit', 'complete']);
      
      // Verify board secretary can manage members
      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');
      await expect(page.locator('[data-testid="add-member-button"]')).toBeVisible();
      
      // Verify board secretary cannot create funds
      await fundsPage.navigateToFunds();
      await fundsPage.verifyCreateFundButtonVisibility(false);
    });

    test('Board Secretary: Fund View-Only Access', async ({ page }) => {
      testContext = await setupTest(page, 'boardSecretary', {
        createFund: true
      });
      
      await fundsPage.navigateToFunds();
      
      // Verify board secretary can view funds
      await fundsPage.verifyFundsDisplayed();
      
      // Verify board secretary cannot create funds
      await fundsPage.verifyCreateFundButtonVisibility(false);
      
      // Verify fund permissions
      const canCreateFund = await hasPermission(page, 'Fund.Create');
      const canViewFund = await hasPermission(page, 'Fund.View');
      expect(canCreateFund).toBe(false);
      expect(canViewFund).toBe(true);
    });
  });

  test.describe('Board Member Role Access Control', () => {
    test('Board Member: Limited Dashboard Access', async ({ page }) => {
      testContext = await setupTest(page, 'boardMember', {
        createFund: true
      });
      
      await dashboardPage.verifyDashboardLoaded();
      
      // Verify board member cannot create funds
      await dashboardPage.verifyNoCreateFundAccess();
      
      // Verify limited navigation menu
      await dashboardPage.verifySideMenuForRole('boardmember');
    });

    test('Board Member: Resolution Voting Access Only', async ({ page }) => {
      testContext = await setupTest(page, 'boardMember', {
        createFund: true,
        createResolution: true,
        createBoardMembers: true
      });
      
      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;
      
      await resolutionsPage.navigateToResolutions(fundId);
      
      // Verify board member can only vote on resolutions
      await resolutionsPage.verifyResolutionActions(resolutionCode, ['vote', 'view']);
      
      // Verify board member cannot create/edit resolutions
      await expect(page.locator('[data-testid="create-resolution-button"]')).toBeHidden();
      await expect(page.locator(`[data-testid="resolution-card"]:has-text("${resolutionCode}") [data-testid="edit-button"]`))
        .toBeHidden();
      
      // Verify voting permission
      const canVote = await hasPermission(page, 'Resolution.Vote');
      const canEdit = await hasPermission(page, 'Resolution.Edit');
      expect(canVote).toBe(true);
      expect(canEdit).toBe(false);
    });

    test('Board Member: View-Only Fund and Member Access', async ({ page }) => {
      testContext = await setupTest(page, 'boardMember', {
        createFund: true,
        createBoardMembers: true
      });
      
      const fundId = testContext.testData.funds[0].id!;
      
      // Verify board member can view funds
      await fundsPage.navigateToFunds();
      await fundsPage.verifyFundsDisplayed();
      await fundsPage.verifyCreateFundButtonVisibility(false);
      
      // Verify board member can view members
      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');
      await expect(page.locator('[data-testid="member-card"]')).toBeVisible();
      
      // Verify no member management access
      await expect(page.locator('[data-testid="add-member-button"]')).toBeHidden();
      await expect(page.locator('[data-testid="edit-member-button"]')).toBeHidden();
    });
  });

  test.describe('Fund-Specific Permissions', () => {
    test('User Access Limited to Assigned Funds Only', async ({ page }) => {
      // This test would require setting up multiple funds with different user assignments
      testContext = await setupTest(page, 'fundManager', {
        createFund: true
      });
      
      await fundsPage.navigateToFunds();
      
      // Verify user can only see funds they are assigned to
      const fundCards = await page.locator('[data-testid="fund-card"]').count();
      expect(fundCards).toBeGreaterThan(0);
      
      // Verify fund-specific permissions in token
      const userRoles = await page.evaluate(() => {
        const token = localStorage.getItem('auth_token');
        if (!token) return [];
        
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          return payload['http://schemas.microsoft.com/ws/2008/06/identity/claims/role'] || [];
        } catch {
          return [];
        }
      });
      
      expect(userRoles).toContain('fundmanager');
    });

    test('Cross-Fund Access Prevention', async ({ page }) => {
      testContext = await setupTest(page, 'boardMember', {
        createFund: true,
        createBoardMembers: true
      });
      
      const fundId = testContext.testData.funds[0].id!;
      
      // Try to access a different fund (simulate unauthorized access)
      const unauthorizedFundId = fundId + 999;
      
      await page.goto(`/admin/investment-funds/${unauthorizedFundId}/details`);
      
      // Verify access denied or redirect
      await expect(page.locator('[data-testid="access-denied-message"]')).toBeVisible();
      // OR verify redirect to authorized funds
      // await expect(page).toHaveURL(/\/admin\/investment-funds$/);
    });
  });

  test.describe('Security Features', () => {
    test('JWT Token Validation and Refresh', async ({ page }) => {
      testContext = await setupTest(page, 'fundManager');
      
      // Get initial token
      const initialToken = await page.evaluate(() => localStorage.getItem('auth_token'));
      expect(initialToken).toBeTruthy();
      
      // Simulate token near expiry (this would depend on backend implementation)
      // Make API call that triggers token refresh
      await page.goto('/admin/dashboard');
      await page.waitForTimeout(1000);
      
      // Verify token is still valid or refreshed
      const currentToken = await page.evaluate(() => localStorage.getItem('auth_token'));
      expect(currentToken).toBeTruthy();
    });

    test('Unauthorized API Access Prevention', async ({ page }) => {
      await loginPage.navigateToLogin();
      
      // Try to access API without authentication
      const response = await page.request.get('/api/funds');
      expect(response.status()).toBe(401);
      
      // Login and try again
      testContext = await setupTest(page, 'fundManager');
      
      const authenticatedResponse = await page.request.get('/api/funds', {
        headers: {
          'Authorization': `Bearer ${await page.evaluate(() => localStorage.getItem('auth_token'))}`
        }
      });
      expect(authenticatedResponse.status()).toBe(200);
    });

    test('CSRF Protection Validation', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true
      });
      
      const fundId = testContext.testData.funds[0].id!;
      
      // Navigate to form that should have CSRF protection
      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');
      await page.click('[data-testid="add-member-button"]');
      
      // Check for CSRF token in form
      const csrfToken = await page.locator('[name="_token"]').getAttribute('value');
      if (csrfToken) {
        expect(csrfToken).toBeTruthy();
        expect(csrfToken.length).toBeGreaterThan(10);
      }
    });
  });
});
