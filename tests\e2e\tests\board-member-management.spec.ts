/**
 * Board Member Management Tests for Jadwa Fund Management System
 * 
 * This test suite covers comprehensive CRUD operations for board members:
 * - Adding board members with validation against business rules
 * - Role-based access control testing
 * - Fund activation scenarios (2 independent members requirement)
 * - Chairman assignment and validation
 * - Member type validation (independent/dependent)
 * - Maximum member limits and business rule enforcement
 */

import { test, expect } from '@playwright/test';
import { LoginPage } from '../page-objects/login.page';
import { DashboardPage } from '../page-objects/dashboard.page';
import { FundsPage } from '../page-objects/funds.page';
import { setupTest, cleanupTest, TestContext } from '../utils/test-setup';
import { TestDataGenerator } from '../fixtures/test-data';

test.describe('Board Member Management', () => {
  let loginPage: LoginPage;
  let dashboardPage: DashboardPage;
  let fundsPage: FundsPage;
  let testContext: TestContext;

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    dashboardPage = new DashboardPage(page);
    fundsPage = new FundsPage(page);
  });

  test.afterEach(async () => {
    if (testContext) {
      await cleanupTest(testContext);
    }
  });

  test.describe('Add Board Members - Legal Council/Board Secretary', () => {
    test('Legal Council: Add First Independent Board Member as Chairman', async ({ page }) => {
      // Setup test with legal council role and fund without members
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const fundName = testContext.testData.funds[0].name.ar;

      // Navigate to fund members
      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');

      // Verify no members message
      await expect(page.locator('[data-testid="no-members-message"]')).toBeVisible();

      // Click add member button
      await page.click('[data-testid="add-member-button"]');

      // Fill member form
      const memberData = TestDataGenerator.generateBoardMember(fundId, {
        memberType: 'independent',
        isChairman: true
      });

      await page.selectOption('[data-testid="member-user"]', memberData.userId);
      await page.check('[data-testid="member-type-independent"]');
      await page.check('[data-testid="is-chairman"]');

      // Submit form
      await page.click('[data-testid="save-member-button"]');

      // Verify success message (MSG003)
      await expect(page.locator('[data-testid="message-MSG003"]')).toBeVisible();
      expect(await page.locator('[data-testid="message-MSG003"]').textContent())
        .toContain('تم حفظ البيانات بنجاح');

      // Verify member notification (MSG002)
      await expect(page.locator('[data-testid="message-MSG002"]')).toBeVisible();
      expect(await page.locator('[data-testid="message-MSG002"]').textContent())
        .toContain('تم إضافتك كعضو');

      // Verify fund manager notification (MSG007)
      await expect(page.locator('[data-testid="message-MSG007"]')).toBeVisible();

      // Verify member is added and displayed
      await expect(page.locator('[data-testid="member-card"]')).toBeVisible();
      await expect(page.locator('[data-testid="member-name"]')).toContainText(memberData.memberName.ar);
      await expect(page.locator('[data-testid="member-type"]')).toContainText('مستقل');
      await expect(page.locator('[data-testid="chairman-badge"]')).toBeVisible();
    });

    test('Board Secretary: Add Second Independent Member to Activate Fund', async ({ page }) => {
      // Setup test with fund having one independent member
      testContext = await setupTest(page, 'boardSecretary', {
        createFund: true
      });

      const fundId = testContext.testData.funds[0].id!;

      // First, add one independent member
      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');
      await page.click('[data-testid="add-member-button"]');

      const firstMember = TestDataGenerator.generateBoardMember(fundId, {
        memberType: 'independent',
        isChairman: true
      });

      await page.selectOption('[data-testid="member-user"]', firstMember.userId);
      await page.check('[data-testid="member-type-independent"]');
      await page.check('[data-testid="is-chairman"]');
      await page.click('[data-testid="save-member-button"]');

      // Wait for success message
      await expect(page.locator('[data-testid="message-MSG003"]')).toBeVisible();

      // Add second independent member
      await page.click('[data-testid="add-member-button"]');

      const secondMember = TestDataGenerator.generateBoardMember(fundId, {
        memberType: 'independent',
        isChairman: false
      });

      await page.selectOption('[data-testid="member-user"]', secondMember.userId);
      await page.check('[data-testid="member-type-independent"]');
      await page.click('[data-testid="save-member-button"]');

      // Verify success message
      await expect(page.locator('[data-testid="message-MSG003"]')).toBeVisible();

      // Verify fund activation notification (MSG008)
      await expect(page.locator('[data-testid="message-MSG008"]')).toBeVisible();
      expect(await page.locator('[data-testid="message-MSG008"]').textContent())
        .toContain('تم تفعيل الصندوق');

      // Verify fund status changed to Active
      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await expect(page.locator('[data-testid="fund-status"]')).toContainText('نشط');

      // Verify all fund activities are now enabled
      await expect(page.locator('[data-testid="resolutions-tab"]')).toBeEnabled();
      await expect(page.locator('[data-testid="meetings-tab"]')).toBeEnabled();
      await expect(page.locator('[data-testid="documents-tab"]')).toBeEnabled();
    });

    test('Add Dependent Board Member', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true,
        createBoardMembers: true // This creates 2 independent members
      });

      const fundId = testContext.testData.funds[0].id!;

      // Navigate to members and add dependent member
      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');
      await page.click('[data-testid="add-member-button"]');

      const dependentMember = TestDataGenerator.generateBoardMember(fundId, {
        memberType: 'dependent',
        isChairman: false
      });

      await page.selectOption('[data-testid="member-user"]', dependentMember.userId);
      await page.check('[data-testid="member-type-dependent"]');
      await page.click('[data-testid="save-member-button"]');

      // Verify member is added successfully
      await expect(page.locator('[data-testid="message-MSG003"]')).toBeVisible();

      // Verify member type is displayed correctly
      const memberCards = await page.locator('[data-testid="member-card"]').all();
      const dependentCard = memberCards.find(async card => 
        (await card.locator('[data-testid="member-name"]').textContent())?.includes(dependentMember.memberName.ar)
      );
      
      expect(dependentCard).toBeDefined();
      await expect(dependentCard!.locator('[data-testid="member-type"]')).toContainText('غير مستقل');
    });

    test('Verify Maximum Independent Members Limit (14 members)', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true
      });

      const fundId = testContext.testData.funds[0].id!;

      // Add 14 independent members (maximum allowed)
      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');

      for (let i = 0; i < 14; i++) {
        await page.click('[data-testid="add-member-button"]');

        const member = TestDataGenerator.generateBoardMember(fundId, {
          memberType: 'independent',
          isChairman: i === 0 // First member is chairman
        });

        await page.selectOption('[data-testid="member-user"]', `user${i + 1}`);
        await page.check('[data-testid="member-type-independent"]');
        
        if (i === 0) {
          await page.check('[data-testid="is-chairman"]');
        }

        await page.click('[data-testid="save-member-button"]');
        await expect(page.locator('[data-testid="message-MSG003"]')).toBeVisible();
      }

      // Try to add 15th independent member
      await page.click('[data-testid="add-member-button"]');
      await page.selectOption('[data-testid="member-user"]', 'user15');
      await page.check('[data-testid="member-type-independent"]');
      await page.click('[data-testid="save-member-button"]');

      // Verify error message (MSG006)
      await expect(page.locator('[data-testid="message-MSG006"]')).toBeVisible();
      expect(await page.locator('[data-testid="message-MSG006"]').textContent())
        .toContain('لقد وصلت للحد الأقصى لعدد الأعضاء المستقلين');
    });

    test('Verify Chairman Uniqueness Constraint', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true
      });

      const fundId = testContext.testData.funds[0].id!;

      // Add first member as chairman
      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');
      await page.click('[data-testid="add-member-button"]');

      await page.selectOption('[data-testid="member-user"]', 'user1');
      await page.check('[data-testid="member-type-independent"]');
      await page.check('[data-testid="is-chairman"]');
      await page.click('[data-testid="save-member-button"]');

      await expect(page.locator('[data-testid="message-MSG003"]')).toBeVisible();

      // Try to add second member as chairman
      await page.click('[data-testid="add-member-button"]');
      await page.selectOption('[data-testid="member-user"]', 'user2');
      await page.check('[data-testid="member-type-independent"]');

      // Verify chairman checkbox is disabled
      await expect(page.locator('[data-testid="is-chairman"]')).toBeDisabled();

      // Verify tooltip or message explaining why it's disabled
      const chairmanField = page.locator('[data-testid="chairman-field"]');
      await expect(chairmanField).toContainText('Each board has max 1 chairman');
    });
  });

  test.describe('View Board Members', () => {
    test('Fund Manager: View All Board Members', async ({ page }) => {
      testContext = await setupTest(page, 'fundManager', {
        createFund: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;

      // Navigate to members tab
      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');

      // Verify members are displayed
      await expect(page.locator('[data-testid="member-card"]')).toHaveCount(2); // 2 independent members

      // Verify member information is displayed correctly
      const memberCards = await page.locator('[data-testid="member-card"]').all();
      
      for (const card of memberCards) {
        await expect(card.locator('[data-testid="member-name"]')).toBeVisible();
        await expect(card.locator('[data-testid="member-type"]')).toBeVisible();
        await expect(card.locator('[data-testid="member-status"]')).toBeVisible();
        await expect(card.locator('[data-testid="last-update-date"]')).toBeVisible();
      }

      // Verify chairman is properly identified
      const chairmanCard = page.locator('[data-testid="member-card"]:has([data-testid="chairman-badge"])');
      await expect(chairmanCard).toHaveCount(1);
    });

    test('Board Member: View Limited Member Information', async ({ page }) => {
      testContext = await setupTest(page, 'boardMember', {
        createFund: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;

      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');

      // Verify board member can view members but not edit/delete
      await expect(page.locator('[data-testid="member-card"]')).toBeVisible();
      
      // Verify no edit/delete buttons for board member role
      await expect(page.locator('[data-testid="edit-member-button"]')).toBeHidden();
      await expect(page.locator('[data-testid="delete-member-button"]')).toBeHidden();
      await expect(page.locator('[data-testid="add-member-button"]')).toBeHidden();
    });

    test('Display No Members Message When Fund Has No Members', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true
      });

      const fundId = testContext.testData.funds[0].id!;

      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');

      // Verify no members message is displayed
      await expect(page.locator('[data-testid="no-members-message"]')).toBeVisible();
      await expect(page.locator('[data-testid="no-members-message"]')).toContainText('MSG001');
    });
  });

  test.describe('Role-Based Access Control', () => {
    test('Legal Council: Full CRUD Access', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;

      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');

      // Verify legal council has all permissions
      await expect(page.locator('[data-testid="add-member-button"]')).toBeVisible();
      await expect(page.locator('[data-testid="edit-member-button"]')).toBeVisible();
      await expect(page.locator('[data-testid="delete-member-button"]')).toBeVisible();
    });

    test('Board Secretary: Full CRUD Access', async ({ page }) => {
      testContext = await setupTest(page, 'boardSecretary', {
        createFund: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;

      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');

      // Verify board secretary has all permissions
      await expect(page.locator('[data-testid="add-member-button"]')).toBeVisible();
      await expect(page.locator('[data-testid="edit-member-button"]')).toBeVisible();
      await expect(page.locator('[data-testid="delete-member-button"]')).toBeVisible();
    });

    test('Fund Manager: View Only Access', async ({ page }) => {
      testContext = await setupTest(page, 'fundManager', {
        createFund: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;

      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');

      // Verify fund manager has view-only access
      await expect(page.locator('[data-testid="member-card"]')).toBeVisible();
      await expect(page.locator('[data-testid="add-member-button"]')).toBeHidden();
      await expect(page.locator('[data-testid="edit-member-button"]')).toBeHidden();
      await expect(page.locator('[data-testid="delete-member-button"]')).toBeHidden();
    });

    test('Board Member: View Only Access', async ({ page }) => {
      testContext = await setupTest(page, 'boardMember', {
        createFund: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;

      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');

      // Verify board member has view-only access
      await expect(page.locator('[data-testid="member-card"]')).toBeVisible();
      await expect(page.locator('[data-testid="add-member-button"]')).toBeHidden();
      await expect(page.locator('[data-testid="edit-member-button"]')).toBeHidden();
      await expect(page.locator('[data-testid="delete-member-button"]')).toBeHidden();
    });
  });

  test.describe('Business Rule Validation', () => {
    test('Verify Fund Activities Disabled Without Minimum Members', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true
      });

      const fundId = testContext.testData.funds[0].id!;

      await page.goto(`/admin/investment-funds/${fundId}/details`);

      // Verify fund status shows "لا يوجد أعضاء"
      await expect(page.locator('[data-testid="fund-status"]')).toContainText('لا يوجد أعضاء');

      // Verify MSG001 notification is displayed
      await expect(page.locator('[data-testid="message-MSG001"]')).toBeVisible();
      expect(await page.locator('[data-testid="message-MSG001"]').textContent())
        .toContain('يجب إضافة أعضاء الصندوق حتى تتمكن من التعامل مع أنشطة الصندوق');

      // Verify all activities except members are disabled
      await expect(page.locator('[data-testid="resolutions-tab"]')).toBeDisabled();
      await expect(page.locator('[data-testid="meetings-tab"]')).toBeDisabled();
      await expect(page.locator('[data-testid="assessments-tab"]')).toBeDisabled();
      await expect(page.locator('[data-testid="documents-tab"]')).toBeDisabled();
      
      // Only members tab should be enabled
      await expect(page.locator('[data-testid="members-tab"]')).toBeEnabled();
    });

    test('Verify Required Field Validation', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true
      });

      const fundId = testContext.testData.funds[0].id!;

      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');
      await page.click('[data-testid="add-member-button"]');

      // Try to save without selecting member
      await page.click('[data-testid="save-member-button"]');

      // Verify validation message (MSG001)
      await expect(page.locator('[data-testid="validation-message"]')).toBeVisible();
      await expect(page.locator('[data-testid="validation-message"]')).toContainText('حقل إلزامي');
    });

    test('Verify Member Cannot Be Added Twice to Same Fund', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true
      });

      const fundId = testContext.testData.funds[0].id!;

      // Add member first time
      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');
      await page.click('[data-testid="add-member-button"]');

      await page.selectOption('[data-testid="member-user"]', 'user1');
      await page.check('[data-testid="member-type-independent"]');
      await page.click('[data-testid="save-member-button"]');

      await expect(page.locator('[data-testid="message-MSG003"]')).toBeVisible();

      // Try to add same member again
      await page.click('[data-testid="add-member-button"]');
      await page.selectOption('[data-testid="member-user"]', 'user1');
      await page.check('[data-testid="member-type-independent"]');
      await page.click('[data-testid="save-member-button"]');

      // Verify error message
      await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
      await expect(page.locator('[data-testid="error-message"]')).toContainText('Member already exists in this fund');
    });
  });
});
