/**
 * Localization and Error Handling Tests for Jadwa Fund Management System
 * 
 * This test suite covers:
 * - Dual-language testing (Arabic/English) with RTL/LTR layout validation
 * - Comprehensive MSG code validation with proper localization
 * - Cross-browser compatibility testing
 * - Unicode character support and font rendering verification
 * - Error message display and user experience testing
 */

import { test, expect } from '@playwright/test';
import { LoginPage } from '../page-objects/login.page';
import { DashboardPage } from '../page-objects/dashboard.page';
import { FundsPage } from '../page-objects/funds.page';
import { ResolutionsPage } from '../page-objects/resolutions.page';
import { setupTest, cleanupTest, TestContext } from '../utils/test-setup';
import { systemMessages } from '../fixtures/test-data';

test.describe('Localization and Error Handling', () => {
  let loginPage: LoginPage;
  let dashboardPage: DashboardPage;
  let fundsPage: FundsPage;
  let resolutionsPage: ResolutionsPage;
  let testContext: TestContext;

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    dashboardPage = new DashboardPage(page);
    fundsPage = new FundsPage(page);
    resolutionsPage = new ResolutionsPage(page);
  });

  test.afterEach(async () => {
    if (testContext) {
      await cleanupTest(testContext);
    }
  });

  test.describe('Arabic Language and RTL Layout', () => {
    test('Verify Arabic Login Page Layout and Functionality', async ({ page }) => {
      // Navigate to login page
      await loginPage.navigateToLogin();

      // Switch to Arabic if not already
      await loginPage.switchLanguage();

      // Verify Arabic layout
      await loginPage.verifyArabicLanguage();

      // Verify RTL direction
      const bodyDir = await page.getAttribute('body', 'dir');
      expect(bodyDir).toBe('rtl');

      // Verify Arabic text rendering
      await expect(page.locator('[data-testid="login-title"]')).toContainText('تسجيل الدخول');
      await expect(page.locator('[data-testid="username-label"]')).toContainText('اسم المستخدم');
      await expect(page.locator('[data-testid="password-label"]')).toContainText('كلمة المرور');

      // Test Arabic input
      await loginPage.enterUsername('مستخدم@test.com');
      await loginPage.enterPassword('كلمة_مرور123');

      // Verify Arabic text is properly displayed in input fields
      const usernameValue = await loginPage.getUsernameValue();
      expect(usernameValue).toBe('مستخدم@test.com');
    });

    test('Verify Arabic Dashboard Layout and Navigation', async ({ page }) => {
      testContext = await setupTest(page, 'fundManager', {
        createFund: true
      });

      // Switch to Arabic
      await dashboardPage.switchLanguage();
      await dashboardPage.verifyArabicLayout();

      // Verify Arabic navigation menu
      await expect(page.locator('[data-testid="funds-menu-item"]')).toContainText('الصناديق');
      await expect(page.locator('[data-testid="strategies-menu-item"]')).toContainText('الاستراتيجيات');

      // Verify Arabic dashboard statistics
      await expect(page.locator('[data-testid="total-funds-title"]')).toContainText('إجمالي الصناديق');
      await expect(page.locator('[data-testid="active-funds-title"]')).toContainText('الصناديق النشطة');

      // Verify Arabic welcome message
      await dashboardPage.verifyWelcomeMessage();
      const welcomeText = await page.locator('[data-testid="welcome-message"]').textContent();
      expect(welcomeText).toMatch(/مرحباً/);
    });

    test('Verify Arabic Fund Management Interface', async ({ page }) => {
      testContext = await setupTest(page, 'fundManager', {
        createFund: true
      });

      // Navigate to funds page in Arabic
      await fundsPage.navigateToFunds();
      await fundsPage.switchLanguage();
      await fundsPage.verifyArabicLayout();

      // Verify Arabic fund card content
      const fundName = testContext.testData.funds[0].name.ar;
      await fundsPage.verifyFundCardExists(fundName);

      // Verify Arabic status and strategy labels
      await expect(page.locator('[data-testid="fund-status-label"]')).toContainText('الحالة');
      await expect(page.locator('[data-testid="fund-strategy-label"]')).toContainText('الاستراتيجية');

      // Test Arabic search functionality
      await fundsPage.searchFunds(fundName);
      await fundsPage.verifySearchResults(fundName);
    });

    test('Verify Arabic Resolution Management Interface', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true,
        createResolution: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;

      // Navigate to resolutions in Arabic
      await resolutionsPage.navigateToResolutions(fundId);
      await resolutionsPage.switchLanguage();

      // Verify Arabic resolution interface
      await expect(page.locator('[data-testid="page-title"]')).toContainText('القرارات');
      await expect(page.locator('[data-testid="resolution-type-label"]')).toContainText('نوع القرار');
      await expect(page.locator('[data-testid="resolution-status-label"]')).toContainText('حالة القرار');

      // Verify Arabic resolution content
      const resolutionDescription = testContext.testData.resolutions[0].description.ar;
      await expect(page.locator(`[data-testid="resolution-card"]:has-text("${resolutionCode}")`))
        .toContainText(resolutionDescription);
    });

    test('Verify Arabic Font Rendering and Unicode Support', async ({ page }) => {
      testContext = await setupTest(page, 'fundManager');

      await dashboardPage.navigateToDashboard();
      await dashboardPage.switchLanguage();

      // Test various Arabic characters and diacritics
      const arabicTexts = [
        'الصناديق الاستثمارية', // Basic Arabic
        'مجلس الإدارة', // Arabic with hamza
        'القرارات المعتمدة', // Arabic with various forms
        'التصويت الإلكتروني', // Arabic with alif maqsura
        'الأعضاء المستقلون' // Arabic with tanween
      ];

      for (const text of arabicTexts) {
        // Create a test element with the Arabic text
        await page.evaluate((testText) => {
          const testDiv = document.createElement('div');
          testDiv.textContent = testText;
          testDiv.setAttribute('data-testid', 'arabic-test');
          document.body.appendChild(testDiv);
        }, text);

        // Verify text is rendered correctly
        const renderedText = await page.locator('[data-testid="arabic-test"]').textContent();
        expect(renderedText).toBe(text);

        // Clean up
        await page.evaluate(() => {
          const testDiv = document.querySelector('[data-testid="arabic-test"]');
          if (testDiv) testDiv.remove();
        });
      }
    });
  });

  test.describe('English Language and LTR Layout', () => {
    test('Verify English Login Page Layout', async ({ page }) => {
      await loginPage.navigateToLogin();
      await loginPage.verifyEnglishLanguage();

      // Verify LTR direction
      const bodyDir = await page.getAttribute('body', 'dir');
      expect(bodyDir).toBe('ltr');

      // Verify English text
      await expect(page.locator('[data-testid="login-title"]')).toContainText('Login');
      await expect(page.locator('[data-testid="username-label"]')).toContainText('Username');
      await expect(page.locator('[data-testid="password-label"]')).toContainText('Password');
    });

    test('Verify English Dashboard and Navigation', async ({ page }) => {
      testContext = await setupTest(page, 'fundManager', {
        createFund: true
      });

      // Ensure English language
      await dashboardPage.verifyEnglishLayout();

      // Verify English navigation
      await expect(page.locator('[data-testid="funds-menu-item"]')).toContainText('Funds');
      await expect(page.locator('[data-testid="strategies-menu-item"]')).toContainText('Strategies');

      // Verify English dashboard content
      await expect(page.locator('[data-testid="total-funds-title"]')).toContainText('Total Funds');
      await expect(page.locator('[data-testid="active-funds-title"]')).toContainText('Active Funds');
    });

    test('Verify English Fund and Resolution Management', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true,
        createResolution: true
      });

      const fundId = testContext.testData.funds[0].id!;

      // Test English fund interface
      await fundsPage.navigateToFunds();
      await fundsPage.verifyEnglishLayout();

      const fundNameEn = testContext.testData.funds[0].name.en;
      await fundsPage.verifyFundCardExists(fundNameEn);

      // Test English resolution interface
      await resolutionsPage.navigateToResolutions(fundId);
      await expect(page.locator('[data-testid="page-title"]')).toContainText('Resolutions');

      const resolutionDescriptionEn = testContext.testData.resolutions[0].description.en;
      await expect(page.locator('[data-testid="resolution-card"]').first())
        .toContainText(resolutionDescriptionEn);
    });
  });

  test.describe('Language Switching and Persistence', () => {
    test('Verify Language Switch Functionality', async ({ page }) => {
      testContext = await setupTest(page, 'fundManager');

      await dashboardPage.navigateToDashboard();

      // Start with Arabic
      await dashboardPage.switchLanguage();
      await dashboardPage.verifyArabicLayout();

      // Switch to English
      await dashboardPage.switchLanguage();
      await dashboardPage.verifyEnglishLayout();

      // Switch back to Arabic
      await dashboardPage.switchLanguage();
      await dashboardPage.verifyArabicLayout();
    });

    test('Verify Language Preference Persistence', async ({ page }) => {
      testContext = await setupTest(page, 'fundManager');

      // Set language to Arabic
      await dashboardPage.navigateToDashboard();
      await dashboardPage.switchLanguage();
      await dashboardPage.verifyArabicLayout();

      // Navigate to different page
      await fundsPage.navigateToFunds();

      // Verify Arabic is maintained
      await fundsPage.verifyArabicLayout();

      // Refresh page
      await page.reload();

      // Verify Arabic is still maintained
      await fundsPage.verifyArabicLayout();
    });
  });

  test.describe('System Message Localization (MSG Codes)', () => {
    test('Verify MSG001 (Required Field) in Both Languages', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true
      });

      const fundId = testContext.testData.funds[0].id!;

      // Test Arabic MSG001
      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');
      await page.click('[data-testid="add-member-button"]');
      await page.click('[data-testid="save-member-button"]');

      await expect(page.locator('[data-testid="message-MSG001"]')).toBeVisible();
      expect(await page.locator('[data-testid="message-MSG001"]').textContent())
        .toBe(systemMessages.MSG001.ar);

      // Switch to English and test
      await dashboardPage.switchLanguage();
      await page.click('[data-testid="save-member-button"]');

      await expect(page.locator('[data-testid="message-MSG001"]')).toBeVisible();
      expect(await page.locator('[data-testid="message-MSG001"]').textContent())
        .toBe(systemMessages.MSG001.en);
    });

    test('Verify MSG002 (Record Saved Successfully) Localization', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true
      });

      const fundId = testContext.testData.funds[0].id!;

      // Test Arabic MSG002
      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');
      await page.click('[data-testid="add-member-button"]');

      await page.selectOption('[data-testid="member-user"]', 'user1');
      await page.check('[data-testid="member-type-independent"]');
      await page.click('[data-testid="save-member-button"]');

      await expect(page.locator('[data-testid="message-MSG002"]')).toBeVisible();
      expect(await page.locator('[data-testid="message-MSG002"]').textContent())
        .toBe(systemMessages.MSG002.ar);

      // Test English version
      await dashboardPage.switchLanguage();
      // Repeat action to trigger message in English
      await page.click('[data-testid="add-member-button"]');
      await page.selectOption('[data-testid="member-user"]', 'user2');
      await page.check('[data-testid="member-type-independent"]');
      await page.click('[data-testid="save-member-button"]');

      await expect(page.locator('[data-testid="message-MSG002"]')).toBeVisible();
      expect(await page.locator('[data-testid="message-MSG002"]').textContent())
        .toBe(systemMessages.MSG002.en);
    });

    test('Verify MSG006/MSG007 (Voting Suspension) Localization', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true,
        createResolution: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;

      // Set up voting in progress
      await resolutionsPage.navigateToResolutions(fundId);
      await resolutionsPage.confirmResolution(resolutionCode);

      // Test Arabic MSG006
      await resolutionsPage.editResolution(resolutionCode);
      await expect(page.locator('[data-testid="message-MSG006"]')).toBeVisible();
      expect(await page.locator('[data-testid="message-MSG006"]').textContent())
        .toBe(systemMessages.MSG006.ar);

      await resolutionsPage.confirmAction();
      await expect(page.locator('[data-testid="message-MSG007"]')).toBeVisible();
      expect(await page.locator('[data-testid="message-MSG007"]').textContent())
        .toBe(systemMessages.MSG007.ar);

      // Test English versions
      await dashboardPage.switchLanguage();
      // Repeat the workflow to test English messages
      await resolutionsPage.confirmResolution(resolutionCode);
      await resolutionsPage.editResolution(resolutionCode);

      await expect(page.locator('[data-testid="message-MSG006"]')).toBeVisible();
      expect(await page.locator('[data-testid="message-MSG006"]').textContent())
        .toBe(systemMessages.MSG006.en);
    });

    test('Verify MSG008/MSG009 (New Resolution Creation) Localization', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true,
        createResolution: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;

      // Test Arabic MSG008/MSG009
      await resolutionsPage.navigateToResolutions(fundId);
      await resolutionsPage.editResolution(resolutionCode);

      await expect(page.locator('[data-testid="message-MSG008"]')).toBeVisible();
      expect(await page.locator('[data-testid="message-MSG008"]').textContent())
        .toBe(systemMessages.MSG008.ar);

      await resolutionsPage.confirmAction();
      await page.click('[data-testid="send-for-confirmation-button"]');

      await expect(page.locator('[data-testid="message-MSG009"]')).toBeVisible();
      expect(await page.locator('[data-testid="message-MSG009"]').textContent())
        .toBe(systemMessages.MSG009.ar);
    });
  });

  test.describe('Cross-Browser Compatibility', () => {
    test('Verify Arabic Text Rendering Across Browsers', async ({ page, browserName }) => {
      testContext = await setupTest(page, 'fundManager');

      await dashboardPage.navigateToDashboard();
      await dashboardPage.switchLanguage();

      // Test Arabic text rendering
      const arabicText = 'الصناديق الاستثمارية';
      await expect(page.locator('[data-testid="funds-menu-item"]')).toContainText(arabicText);

      // Verify RTL layout works correctly
      const bodyDir = await page.getAttribute('body', 'dir');
      expect(bodyDir).toBe('rtl');

      // Browser-specific checks
      if (browserName === 'webkit') {
        // Safari-specific Arabic font rendering checks
        const computedStyle = await page.evaluate(() => {
          const element = document.querySelector('[data-testid="funds-menu-item"]');
          return window.getComputedStyle(element!).fontFamily;
        });
        expect(computedStyle).toMatch(/arabic|noto|tahoma/i);
      }
    });

    test('Verify Form Input Handling Across Browsers', async ({ page, browserName }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true
      });

      const fundId = testContext.testData.funds[0].id!;

      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');
      await page.click('[data-testid="add-member-button"]');

      // Test Arabic input in different browsers
      const arabicName = 'أحمد محمد الأحمد';
      await page.fill('[data-testid="member-name-ar"]', arabicName);

      const inputValue = await page.inputValue('[data-testid="member-name-ar"]');
      expect(inputValue).toBe(arabicName);

      // Browser-specific input handling
      if (browserName === 'firefox') {
        // Firefox-specific Arabic input validation
        await page.keyboard.press('Tab');
        const focusedElement = await page.evaluate(() => document.activeElement?.getAttribute('data-testid'));
        expect(focusedElement).toBeTruthy();
      }
    });
  });

  test.describe('Error Message Display and UX', () => {
    test('Verify Error Message Positioning and Styling', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true
      });

      const fundId = testContext.testData.funds[0].id!;

      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');
      await page.click('[data-testid="add-member-button"]');
      await page.click('[data-testid="save-member-button"]');

      // Verify error message styling
      const errorMessage = page.locator('[data-testid="message-MSG001"]');
      await expect(errorMessage).toBeVisible();

      // Check error message styling
      const errorStyles = await errorMessage.evaluate(el => {
        const styles = window.getComputedStyle(el);
        return {
          color: styles.color,
          backgroundColor: styles.backgroundColor,
          border: styles.border
        };
      });

      // Verify error styling (red color, appropriate background)
      expect(errorStyles.color).toMatch(/rgb\(220, 38, 38\)|red|#dc2626/i);
    });

    test('Verify Error Message Accessibility', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true
      });

      const fundId = testContext.testData.funds[0].id!;

      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');
      await page.click('[data-testid="add-member-button"]');
      await page.click('[data-testid="save-member-button"]');

      // Verify error message has proper ARIA attributes
      const errorMessage = page.locator('[data-testid="message-MSG001"]');
      await expect(errorMessage).toHaveAttribute('role', 'alert');
      await expect(errorMessage).toHaveAttribute('aria-live', 'polite');

      // Verify error is associated with form field
      const memberUserField = page.locator('[data-testid="member-user"]');
      const ariaDescribedBy = await memberUserField.getAttribute('aria-describedby');
      expect(ariaDescribedBy).toContain('MSG001');
    });

    test('Verify Error Message Auto-Dismiss', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true
      });

      const fundId = testContext.testData.funds[0].id!;

      await page.goto(`/admin/investment-funds/${fundId}/details`);
      await page.click('[data-testid="members-tab"]');
      await page.click('[data-testid="add-member-button"]');
      await page.click('[data-testid="save-member-button"]');

      // Verify error message appears
      await expect(page.locator('[data-testid="message-MSG001"]')).toBeVisible();

      // Fix the error by selecting a user
      await page.selectOption('[data-testid="member-user"]', 'user1');

      // Verify error message disappears
      await expect(page.locator('[data-testid="message-MSG001"]')).toBeHidden();
    });
  });
});
