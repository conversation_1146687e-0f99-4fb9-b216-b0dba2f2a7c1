/**
 * Resolution Alternative Workflows Tests for Jadwa Fund Management System
 * 
 * This test suite covers the 17 alternative scenarios for Resolution lifecycle:
 * - Alternative 1: Voting suspension workflow with MSG006/MSG007 validation
 * - Alternative 2: New resolution creation from Approved/NotApproved with MSG008/MSG009
 * - Alternative 3: Standard workflow without alternatives (covered in main lifecycle tests)
 * 
 * Tests include proper state rollback, business rule enforcement, and MSG code validation.
 */

import { test, expect } from '@playwright/test';
import { LoginPage } from '../page-objects/login.page';
import { ResolutionsPage } from '../page-objects/resolutions.page';
import { setupTest, cleanupTest, TestContext } from '../utils/test-setup';
import { TestDataGenerator } from '../fixtures/test-data';

test.describe('Resolution Alternative Workflows', () => {
  let loginPage: LoginPage;
  let resolutionsPage: ResolutionsPage;
  let testContext: TestContext;

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    resolutionsPage = new ResolutionsPage(page);
  });

  test.afterEach(async () => {
    if (testContext) {
      await cleanupTest(testContext);
    }
  });

  test.describe('Alternative 1: Voting Suspension Workflow', () => {
    test('Legal Council: Suspend Voting by Editing Resolution Data', async ({ page }) => {
      // Setup test with resolution in voting progress
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true,
        createResolution: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;

      // Set resolution to voting in progress status
      await resolutionsPage.navigateToResolutions(fundId);
      await resolutionsPage.confirmResolution(resolutionCode);
      await resolutionsPage.waitForResolutionStateChange(resolutionCode, 'Voting in Progress');

      // Attempt to edit resolution while voting is in progress
      await resolutionsPage.editResolution(resolutionCode);

      // Verify MSG006 confirmation message appears
      await resolutionsPage.verifySystemMessage('MSG006');
      expect(await page.locator('[data-testid="message-MSG006"]').textContent())
        .toContain('هذا القرار في مرحلة التصويت, في حالة التعديل على البيانات سوف يتم إلغاء التصويت');

      // Confirm the suspension
      await resolutionsPage.confirmAction();

      // Verify MSG007 notification is sent
      await resolutionsPage.verifySystemMessage('MSG007');
      expect(await page.locator('[data-testid="message-MSG007"]').textContent())
        .toContain('تم تحديث بيانات القرار مما يترتب عليه إلغاء التصويت الجارى');

      // Verify resolution status rolled back to "Waiting for Confirmation"
      await resolutionsPage.waitForResolutionStateChange(resolutionCode, 'Waiting for Confirmation');

      // Verify voting is suspended and members are notified
      await resolutionsPage.verifyResolutionActions(resolutionCode, ['confirm', 'reject']);
    });

    test('Board Secretary: Suspend Voting by Adding Resolution Items', async ({ page }) => {
      // Setup test with board secretary role
      testContext = await setupTest(page, 'boardSecretary', {
        createFund: true,
        createResolution: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;

      // Set resolution to voting in progress
      await resolutionsPage.navigateToResolutions(fundId);
      await resolutionsPage.confirmResolution(resolutionCode);
      await resolutionsPage.waitForResolutionStateChange(resolutionCode, 'Voting in Progress');

      // Edit resolution to add new items
      await resolutionsPage.editResolution(resolutionCode);

      // Add new resolution item
      await page.click('[data-testid="add-item-button"]');
      await page.fill('[data-testid="item-title"]', 'New Item During Voting');
      await page.fill('[data-testid="item-description"]', 'Additional item added during voting process');
      await page.click('[data-testid="save-item-button"]');

      // Attempt to save changes
      await page.click('[data-testid="send-for-confirmation-button"]');

      // Verify MSG006 confirmation appears
      await resolutionsPage.verifySystemMessage('MSG006');

      // Confirm the changes
      await resolutionsPage.confirmAction();

      // Verify voting suspension and state rollback
      await resolutionsPage.waitForResolutionStateChange(resolutionCode, 'Waiting for Confirmation');
      await resolutionsPage.verifySystemMessage('MSG007');
    });

    test('Verify Voting Suspension Notifications to All Stakeholders', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true,
        createResolution: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;

      // Execute voting suspension workflow
      await resolutionsPage.navigateToResolutions(fundId);
      await resolutionsPage.confirmResolution(resolutionCode);
      await resolutionsPage.waitForResolutionStateChange(resolutionCode, 'Voting in Progress');
      
      await resolutionsPage.editResolution(resolutionCode);
      await resolutionsPage.verifySystemMessage('MSG006');
      await resolutionsPage.confirmAction();

      // Verify notifications sent to:
      // 1. Fund managers
      // 2. Legal council
      // 3. Board secretary
      // 4. Board members
      await resolutionsPage.verifySystemMessage('MSG007');

      // Verify notification content includes resolution code and user details
      const notificationText = await page.locator('[data-testid="message-MSG007"]').textContent();
      expect(notificationText).toContain(resolutionCode);
      expect(notificationText).toContain('Legal Council'); // Role of the user who made the change
    });

    test('Verify Voting Data Preservation After Suspension', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true,
        createResolution: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;

      // Simulate some votes before suspension
      await resolutionsPage.navigateToResolutions(fundId);
      await resolutionsPage.confirmResolution(resolutionCode);
      
      // Record existing votes (if any)
      const votesBefore = await page.locator('[data-testid="vote-count"]').textContent();

      // Suspend voting
      await resolutionsPage.editResolution(resolutionCode);
      await resolutionsPage.confirmAction();

      // Verify votes are cleared/reset after suspension
      await resolutionsPage.waitForResolutionStateChange(resolutionCode, 'Waiting for Confirmation');
      
      // When voting resumes, it should start fresh
      await resolutionsPage.confirmResolution(resolutionCode);
      const votesAfter = await page.locator('[data-testid="vote-count"]').textContent();
      expect(votesAfter).toBe('0'); // Votes should be reset
    });
  });

  test.describe('Alternative 2: New Resolution from Approved/NotApproved', () => {
    test('Legal Council: Create New Resolution from Approved Resolution', async ({ page }) => {
      // Setup test with approved resolution
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true,
        createResolution: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const originalResolutionCode = testContext.testData.resolutions[0].code!;

      // Set resolution to approved status (simulate completed voting)
      await resolutionsPage.navigateToResolutions(fundId);
      // Simulate approval process completion
      await page.evaluate(() => {
        // This would typically be done through the complete voting process
        // For testing, we'll simulate the final approved state
      });

      // Attempt to edit approved resolution
      await resolutionsPage.editResolution(originalResolutionCode);

      // Verify MSG008 confirmation message
      await resolutionsPage.verifySystemMessage('MSG008');
      expect(await page.locator('[data-testid="message-MSG008"]').textContent())
        .toContain('تحديث بيانات القرار المعتمد يترتب عليه إنشاء قرار جديد');

      // Confirm creation of new resolution
      await resolutionsPage.confirmAction();

      // Modify resolution data
      await page.fill('[data-testid="resolution-description"]', 'Updated resolution based on approved resolution');
      await page.click('[data-testid="send-for-confirmation-button"]');

      // Verify MSG009 notification
      await resolutionsPage.verifySystemMessage('MSG009');
      expect(await page.locator('[data-testid="message-MSG009"]').textContent())
        .toContain('تم إضافة قرار جديد كتحديث على القرار السابق');

      // Verify new resolution is created with new code
      await resolutionsPage.navigateToResolutions(fundId);
      const resolutionCount = await resolutionsPage.getResolutionCardsCount();
      expect(resolutionCount).toBeGreaterThan(1); // Original + new resolution

      // Verify relationship between old and new resolution
      const newResolutionCode = await page.locator('[data-testid="resolution-card"]:last-child [data-testid="resolution-code"]').textContent();
      expect(newResolutionCode).not.toBe(originalResolutionCode);
    });

    test('Board Secretary: Create New Resolution from Not Approved Resolution', async ({ page }) => {
      testContext = await setupTest(page, 'boardSecretary', {
        createFund: true,
        createResolution: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const originalResolutionCode = testContext.testData.resolutions[0].code!;

      // Set resolution to not approved status
      await resolutionsPage.navigateToResolutions(fundId);
      // Simulate rejection process completion

      // Edit not approved resolution
      await resolutionsPage.editResolution(originalResolutionCode);

      // Verify MSG008 confirmation
      await resolutionsPage.verifySystemMessage('MSG008');
      await resolutionsPage.confirmAction();

      // Make significant changes to create new resolution
      await page.fill('[data-testid="resolution-description"]', 'Revised resolution addressing previous concerns');
      await page.selectOption('[data-testid="resolution-type"]', 'Different Type');
      await page.click('[data-testid="send-for-confirmation-button"]');

      // Verify new resolution creation
      await resolutionsPage.verifySystemMessage('MSG009');
      await resolutionsPage.navigateToResolutions(fundId);

      // Verify both resolutions exist with proper relationship
      const resolutions = await page.locator('[data-testid="resolution-card"]').all();
      expect(resolutions.length).toBe(2);
    });

    test('Verify New Resolution Code Generation and Relationship', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true,
        createResolution: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const fundCode = testContext.testData.funds[0].code!;
      const originalResolutionCode = testContext.testData.resolutions[0].code!;

      // Create new resolution from approved one
      await resolutionsPage.navigateToResolutions(fundId);
      await resolutionsPage.editResolution(originalResolutionCode);
      await resolutionsPage.confirmAction();
      await page.click('[data-testid="send-for-confirmation-button"]');

      // Verify new resolution code follows proper format
      await resolutionsPage.navigateToResolutions(fundId);
      const newResolutionCode = await page.locator('[data-testid="resolution-card"]:last-child [data-testid="resolution-code"]').textContent();
      
      const currentYear = new Date().getFullYear();
      const expectedPattern = new RegExp(`${fundCode}/${currentYear}/\\d{3}`);
      expect(newResolutionCode).toMatch(expectedPattern);

      // Verify relationship is maintained
      await page.click(`[data-testid="resolution-card"]:has-text("${newResolutionCode}") [data-testid="view-details-button"]`);
      const relatedResolution = await page.locator('[data-testid="related-resolution"]').textContent();
      expect(relatedResolution).toContain(originalResolutionCode);
    });

    test('Verify Notifications for New Resolution Creation', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true,
        createResolution: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const originalResolutionCode = testContext.testData.resolutions[0].code!;

      // Execute new resolution creation workflow
      await resolutionsPage.navigateToResolutions(fundId);
      await resolutionsPage.editResolution(originalResolutionCode);
      await resolutionsPage.confirmAction();
      await page.click('[data-testid="send-for-confirmation-button"]');

      // Verify MSG009 notification content
      await resolutionsPage.verifySystemMessage('MSG009');
      const notificationText = await page.locator('[data-testid="message-MSG009"]').textContent();
      
      // Verify notification includes:
      // - Fund name
      // - User role and name
      // - Original resolution number
      expect(notificationText).toContain(testContext.testData.funds[0].name.ar);
      expect(notificationText).toContain('Legal Council');
      expect(notificationText).toContain(originalResolutionCode);
    });
  });

  test.describe('Alternative Workflow Business Rules', () => {
    test('Verify Only Authorized Roles Can Trigger Alternative Workflows', async ({ page }) => {
      // Test with board member (should not have edit access)
      testContext = await setupTest(page, 'boardMember', {
        createFund: true,
        createResolution: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;

      await resolutionsPage.navigateToResolutions(fundId);

      // Verify board member cannot edit resolution
      await resolutionsPage.verifyResolutionActions(resolutionCode, ['vote', 'view']);
      
      // Edit button should not be visible
      const editButton = page.locator(`[data-testid="resolution-card"]:has-text("${resolutionCode}") [data-testid="edit-button"]`);
      await expect(editButton).toBeHidden();
    });

    test('Verify Alternative Workflows Maintain Audit Trail', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true,
        createResolution: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;

      // Execute voting suspension
      await resolutionsPage.navigateToResolutions(fundId);
      await resolutionsPage.confirmResolution(resolutionCode);
      await resolutionsPage.editResolution(resolutionCode);
      await resolutionsPage.confirmAction();

      // Verify audit trail entries
      await page.click(`[data-testid="resolution-card"]:has-text("${resolutionCode}") [data-testid="view-details-button"]`);
      await page.click('[data-testid="audit-trail-tab"]');

      // Verify audit entries include:
      // 1. Resolution vote suspend action
      // 2. Resolution data update action
      // 3. User and timestamp information
      const auditEntries = await page.locator('[data-testid="audit-entry"]').all();
      expect(auditEntries.length).toBeGreaterThanOrEqual(2);

      const suspendEntry = auditEntries.find(async entry => 
        (await entry.textContent())?.includes('resolution vote suspend')
      );
      expect(suspendEntry).toBeDefined();
    });

    test('Verify State Consistency After Alternative Workflows', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true,
        createResolution: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;

      // Execute complete alternative workflow cycle
      await resolutionsPage.navigateToResolutions(fundId);
      
      // 1. Start voting
      await resolutionsPage.confirmResolution(resolutionCode);
      await resolutionsPage.waitForResolutionStateChange(resolutionCode, 'Voting in Progress');
      
      // 2. Suspend voting
      await resolutionsPage.editResolution(resolutionCode);
      await resolutionsPage.confirmAction();
      await resolutionsPage.waitForResolutionStateChange(resolutionCode, 'Waiting for Confirmation');
      
      // 3. Resume voting
      await resolutionsPage.confirmResolution(resolutionCode);
      await resolutionsPage.waitForResolutionStateChange(resolutionCode, 'Voting in Progress');

      // Verify resolution state is consistent and voting can proceed normally
      await resolutionsPage.verifyResolutionActions(resolutionCode, ['vote']);
      
      // Verify resolution data integrity
      const resolutionStatus = await resolutionsPage.getResolutionStatus(resolutionCode);
      expect(resolutionStatus).toBe('Voting in Progress');
    });
  });
});
