/**
 * Resolution Lifecycle Tests for Jadwa Fund Management System
 * 
 * This test suite covers the complete resolution state machine:
 * Draft → Pending → Waiting for Confirmation → Voting in Progress → Approved/NotApproved
 * 
 * Tests include business rule validation, role-based access control,
 * and proper state transitions with MSG code validation.
 */

import { test, expect } from '@playwright/test';
import { LoginPage } from '../page-objects/login.page';
import { DashboardPage } from '../page-objects/dashboard.page';
import { FundsPage } from '../page-objects/funds.page';
import { ResolutionsPage } from '../page-objects/resolutions.page';
import { setupTest, cleanupTest, TestContext } from '../utils/test-setup';
import { getCurrentEnvironment } from '../config/environments';
import { TestDataGenerator } from '../fixtures/test-data';

test.describe('Resolution Lifecycle Management', () => {
  let loginPage: LoginPage;
  let dashboardPage: DashboardPage;
  let fundsPage: FundsPage;
  let resolutionsPage: ResolutionsPage;
  let testContext: TestContext;

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    dashboardPage = new DashboardPage(page);
    fundsPage = new FundsPage(page);
    resolutionsPage = new ResolutionsPage(page);
  });

  test.afterEach(async () => {
    if (testContext) {
      await cleanupTest(testContext);
    }
  });

  test.describe('Standard Resolution Workflow (Alternative 3)', () => {
    test('Fund Manager: Create Draft Resolution', async ({ page }) => {
      // Setup test with fund manager role and create test fund
      testContext = await setupTest(page, 'fundManager', {
        createFund: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;
      
      // Navigate to resolutions page
      await resolutionsPage.navigateToResolutions(fundId);
      await resolutionsPage.verifyResolutionsPageLoaded();

      // Create new resolution
      await resolutionsPage.clickCreateResolution();

      // Fill resolution form
      const resolutionData = TestDataGenerator.generateResolution(fundId);
      await page.fill('[data-testid="resolution-date"]', resolutionData.date);
      await page.selectOption('[data-testid="resolution-type"]', resolutionData.type.en);
      await page.fill('[data-testid="resolution-description"]', resolutionData.description.ar);
      await page.selectOption('[data-testid="voting-methodology"]', resolutionData.votingMethodology);

      // Save as draft
      await page.click('[data-testid="save-draft-button"]');

      // Verify success message (MSG003)
      await resolutionsPage.verifySystemMessage('MSG003');

      // Verify resolution is created with draft status
      await resolutionsPage.navigateToResolutions(fundId);
      await resolutionsPage.verifyResolutionExists(resolutionData.code!);
      await resolutionsPage.verifyResolutionStatus(resolutionData.code!, 'Draft');
    });

    test('Fund Manager: Send Draft Resolution to Pending', async ({ page }) => {
      // Setup test with existing draft resolution
      testContext = await setupTest(page, 'fundManager', {
        createFund: true,
        createResolution: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;

      // Navigate to resolutions page
      await resolutionsPage.navigateToResolutions(fundId);

      // Send resolution for approval
      await resolutionsPage.sendResolution(resolutionCode);

      // Verify success message (MSG003)
      await resolutionsPage.verifySystemMessage('MSG003');

      // Verify status changed to pending
      await resolutionsPage.waitForResolutionStateChange(resolutionCode, 'Pending');

      // Verify notification sent to legal council and board secretary (MSG002)
      await resolutionsPage.verifySystemMessage('MSG002');
    });

    test('Legal Council: Complete Resolution Data', async ({ page }) => {
      // Setup test with pending resolution
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true,
        createResolution: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;

      // Set resolution to pending status
      await resolutionsPage.navigateToResolutions(fundId);
      await resolutionsPage.sendResolution(resolutionCode);

      // Complete resolution data
      await resolutionsPage.completeResolution(resolutionCode);

      // Add resolution items
      await page.click('[data-testid="add-item-button"]');
      await page.fill('[data-testid="item-title"]', 'Test Resolution Item');
      await page.fill('[data-testid="item-description"]', 'Test item description');
      await page.click('[data-testid="save-item-button"]');

      // Send for confirmation
      await page.click('[data-testid="send-for-confirmation-button"]');

      // Verify success message (MSG002)
      await resolutionsPage.verifySystemMessage('MSG002');

      // Verify status changed to waiting for confirmation
      await resolutionsPage.waitForResolutionStateChange(resolutionCode, 'Waiting for Confirmation');

      // Verify notification sent to fund manager (MSG003)
      await resolutionsPage.verifySystemMessage('MSG003');
    });

    test('Fund Manager: Confirm Resolution for Voting', async ({ page }) => {
      // Setup test with resolution waiting for confirmation
      testContext = await setupTest(page, 'fundManager', {
        createFund: true,
        createResolution: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;

      // Navigate to resolutions and confirm
      await resolutionsPage.navigateToResolutions(fundId);
      await resolutionsPage.confirmResolution(resolutionCode);

      // Verify status changed to voting in progress
      await resolutionsPage.waitForResolutionStateChange(resolutionCode, 'Voting in Progress');

      // Verify voting actions are available for board members
      await resolutionsPage.verifyResolutionActions(resolutionCode, ['vote']);
    });

    test('Board Member: Vote on Resolution', async ({ page }) => {
      // Setup test with resolution in voting progress
      testContext = await setupTest(page, 'boardMember', {
        createFund: true,
        createResolution: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;

      // Navigate to resolutions and vote
      await resolutionsPage.navigateToResolutions(fundId);
      await resolutionsPage.voteOnResolution(resolutionCode, 'yes');

      // Verify vote was recorded
      const voteStatus = await page.locator(`[data-testid="vote-status-${resolutionCode}"]`).textContent();
      expect(voteStatus).toContain('Voted: Yes');
    });

    test('Resolution Approval: All Members Vote Yes', async ({ page }) => {
      // Setup test with multiple board members
      testContext = await setupTest(page, 'fundManager', {
        createFund: true,
        createResolution: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;

      // Simulate all members voting yes
      // This would typically involve multiple user sessions
      // For this test, we'll simulate the final state

      await resolutionsPage.navigateToResolutions(fundId);

      // Wait for resolution to be approved (after all votes)
      await resolutionsPage.waitForResolutionStateChange(resolutionCode, 'Approved');

      // Verify final status
      await resolutionsPage.verifyResolutionStatus(resolutionCode, 'Approved');
    });

    test('Resolution Rejection: Majority Vote No', async ({ page }) => {
      // Setup test with resolution in voting
      testContext = await setupTest(page, 'fundManager', {
        createFund: true,
        createResolution: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;

      // Simulate majority voting no
      await resolutionsPage.navigateToResolutions(fundId);

      // Wait for resolution to be not approved (after majority no votes)
      await resolutionsPage.waitForResolutionStateChange(resolutionCode, 'Not Approved');

      // Verify final status
      await resolutionsPage.verifyResolutionStatus(resolutionCode, 'Not Approved');
    });
  });

  test.describe('Resolution State Validation', () => {
    test('Verify Draft Resolution Actions', async ({ page }) => {
      testContext = await setupTest(page, 'fundManager', {
        createFund: true,
        createResolution: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;

      await resolutionsPage.navigateToResolutions(fundId);

      // Verify available actions for draft resolution
      await resolutionsPage.verifyResolutionActions(resolutionCode, [
        'edit', 'delete', 'send'
      ]);
    });

    test('Verify Pending Resolution Actions', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true,
        createResolution: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;

      await resolutionsPage.navigateToResolutions(fundId);

      // Set to pending status
      await resolutionsPage.sendResolution(resolutionCode);

      // Verify available actions for pending resolution
      await resolutionsPage.verifyResolutionActions(resolutionCode, [
        'edit', 'complete', 'cancel'
      ]);
    });

    test('Verify Voting in Progress Resolution Actions', async ({ page }) => {
      testContext = await setupTest(page, 'boardMember', {
        createFund: true,
        createResolution: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;

      await resolutionsPage.navigateToResolutions(fundId);

      // Verify available actions for voting resolution
      await resolutionsPage.verifyResolutionActions(resolutionCode, ['vote']);
    });

    test('Verify Approved Resolution Actions', async ({ page }) => {
      testContext = await setupTest(page, 'legalCouncil', {
        createFund: true,
        createResolution: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;

      await resolutionsPage.navigateToResolutions(fundId);

      // Set to approved status (simulated)
      // In real scenario, this would be after voting completion

      // Verify available actions for approved resolution
      await resolutionsPage.verifyResolutionActions(resolutionCode, ['edit', 'view']);
    });
  });

  test.describe('Business Rule Validation', () => {
    test('Verify Fund Must Have 2 Independent Members for Voting', async ({ page }) => {
      // Setup test with fund having only 1 independent member
      testContext = await setupTest(page, 'fundManager', {
        createFund: true,
        createResolution: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;

      await resolutionsPage.navigateToResolutions(fundId);

      // Try to send resolution for voting
      await resolutionsPage.confirmResolution(resolutionCode);

      // Verify error message about insufficient independent members
      await resolutionsPage.verifySystemMessage('MSG001');
    });

    test('Verify Resolution Code Generation', async ({ page }) => {
      testContext = await setupTest(page, 'fundManager', {
        createFund: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const fundCode = testContext.testData.funds[0].code!;

      await resolutionsPage.navigateToResolutions(fundId);
      await resolutionsPage.clickCreateResolution();

      // Fill and save resolution
      const resolutionData = TestDataGenerator.generateResolution(fundId);
      await page.fill('[data-testid="resolution-date"]', resolutionData.date);
      await page.selectOption('[data-testid="resolution-type"]', resolutionData.type.en);
      await page.click('[data-testid="save-draft-button"]');

      // Verify resolution code format: fund code/year/sequence
      const currentYear = new Date().getFullYear();
      const expectedCodePattern = `${fundCode}/${currentYear}/001`;
      
      await resolutionsPage.navigateToResolutions(fundId);
      const resolutionCards = await page.locator('[data-testid="resolution-card"]').all();
      const firstCard = resolutionCards[0];
      const codeText = await firstCard.locator('[data-testid="resolution-code"]').textContent();
      
      expect(codeText).toContain(expectedCodePattern);
    });

    test('Verify Voting Methodology Enforcement', async ({ page }) => {
      testContext = await setupTest(page, 'fundManager', {
        createFund: true,
        createResolution: true,
        createBoardMembers: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;

      await resolutionsPage.navigateToResolutions(fundId);

      // Verify voting methodology is enforced based on fund settings
      // This would check if "All Members" or "Majority" voting is properly applied
      const votingMethod = await page.locator(`[data-testid="voting-methodology-${resolutionCode}"]`).textContent();
      expect(['All Members', 'Majority']).toContain(votingMethod);
    });
  });

  test.describe('Error Handling and Edge Cases', () => {
    test('Handle Network Errors During State Transitions', async ({ page }) => {
      testContext = await setupTest(page, 'fundManager', {
        createFund: true,
        createResolution: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;

      // Simulate network error
      await page.route('**/api/resolutions/**', route => route.abort());

      await resolutionsPage.navigateToResolutions(fundId);
      await resolutionsPage.sendResolution(resolutionCode);

      // Verify error handling
      await resolutionsPage.verifySystemMessage('MSG003'); // Error message
    });

    test('Verify Concurrent Editing Prevention', async ({ page, context }) => {
      testContext = await setupTest(page, 'fundManager', {
        createFund: true,
        createResolution: true
      });

      const fundId = testContext.testData.funds[0].id!;
      const resolutionCode = testContext.testData.resolutions[0].code!;

      // Open resolution in two tabs
      const page2 = await context.newPage();
      const resolutionsPage2 = new ResolutionsPage(page2);

      await resolutionsPage.navigateToResolutions(fundId);
      await resolutionsPage2.navigateToResolutions(fundId);

      // Edit in first tab
      await resolutionsPage.editResolution(resolutionCode);

      // Try to edit in second tab
      await resolutionsPage2.editResolution(resolutionCode);

      // Verify conflict detection
      await resolutionsPage2.verifySystemMessage('MSG003'); // Conflict error
    });
  });
});
