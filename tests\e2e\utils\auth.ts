/**
 * Authentication Utilities for Jadwa Fund Management System E2E Tests
 * 
 * This module provides utilities for handling JWT authentication,
 * role-based access, and session management in Playwright tests.
 */

import { Page, BrowserContext, expect } from '@playwright/test';
import { getCurrentEnvironment, getCredentials } from '../config/environments';

export type UserRole = 'fundManager' | 'legalCouncil' | 'boardSecretary' | 'boardMember';

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
}

export interface UserSession {
  role: UserRole;
  tokens: AuthTokens;
  userId: string;
  permissions: string[];
}

/**
 * Login with specific user role
 */
export async function loginAs(page: Page, role: UserRole): Promise<UserSession> {
  const environment = getCurrentEnvironment();
  const credentials = getCredentials(role);
  
  if (!credentials.username || !credentials.password) {
    throw new Error(`Credentials not configured for role: ${role} in environment: ${environment.name}`);
  }

  // Navigate to login page
  await page.goto('/auth/login');
  
  // Wait for login form to be visible
  await expect(page.locator('[data-testid="login-form"]')).toBeVisible();
  
  // Fill login credentials
  await page.fill('[data-testid="username-input"]', credentials.username);
  await page.fill('[data-testid="password-input"]', credentials.password);
  
  // Submit login form
  await page.click('[data-testid="login-button"]');
  
  // Wait for successful login (redirect to dashboard)
  await expect(page).toHaveURL(/\/admin\/dashboard/);
  
  // Extract tokens from localStorage
  const tokens = await extractTokensFromStorage(page);
  
  // Get user permissions from token
  const permissions = await getUserPermissions(page);
  
  // Get user ID from token
  const userId = await getUserId(page);
  
  return {
    role,
    tokens,
    userId,
    permissions
  };
}

/**
 * Login with username and password directly
 */
export async function loginWithCredentials(
  page: Page, 
  username: string, 
  password: string
): Promise<void> {
  await page.goto('/auth/login');
  
  await expect(page.locator('[data-testid="login-form"]')).toBeVisible();
  
  await page.fill('[data-testid="username-input"]', username);
  await page.fill('[data-testid="password-input"]', password);
  
  await page.click('[data-testid="login-button"]');
  
  // Wait for successful login
  await expect(page).toHaveURL(/\/admin\/dashboard/);
}

/**
 * Logout current user
 */
export async function logout(page: Page): Promise<void> {
  // Click user menu
  await page.click('[data-testid="user-menu"]');
  
  // Click logout option
  await page.click('[data-testid="logout-button"]');
  
  // Wait for redirect to login page
  await expect(page).toHaveURL(/\/auth\/login/);
}

/**
 * Check if user is authenticated
 */
export async function isAuthenticated(page: Page): Promise<boolean> {
  try {
    const token = await page.evaluate(() => localStorage.getItem('auth_token'));
    return !!token;
  } catch {
    return false;
  }
}

/**
 * Extract JWT tokens from localStorage
 */
export async function extractTokensFromStorage(page: Page): Promise<AuthTokens> {
  const accessToken = await page.evaluate(() => localStorage.getItem('auth_token'));
  const refreshToken = await page.evaluate(() => localStorage.getItem('refresh_token'));
  
  if (!accessToken) {
    throw new Error('Access token not found in localStorage');
  }
  
  // Decode JWT to get expiration time
  const tokenPayload = JSON.parse(atob(accessToken.split('.')[1]));
  const expiresAt = tokenPayload.exp * 1000; // Convert to milliseconds
  
  return {
    accessToken,
    refreshToken: refreshToken || '',
    expiresAt
  };
}

/**
 * Get user permissions from token
 */
export async function getUserPermissions(page: Page): Promise<string[]> {
  return await page.evaluate(() => {
    const token = localStorage.getItem('auth_token');
    if (!token) return [];
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.Permission || [];
    } catch {
      return [];
    }
  });
}

/**
 * Get user ID from token
 */
export async function getUserId(page: Page): Promise<string> {
  return await page.evaluate(() => {
    const token = localStorage.getItem('auth_token');
    if (!token) return '';
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.Id || '';
    } catch {
      return '';
    }
  });
}

/**
 * Get user roles from token
 */
export async function getUserRoles(page: Page): Promise<string[]> {
  return await page.evaluate(() => {
    const token = localStorage.getItem('auth_token');
    if (!token) return [];
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload['http://schemas.microsoft.com/ws/2008/06/identity/claims/role'] || [];
    } catch {
      return [];
    }
  });
}

/**
 * Check if user has specific permission
 */
export async function hasPermission(page: Page, permission: string): Promise<boolean> {
  const permissions = await getUserPermissions(page);
  return permissions.includes(permission);
}

/**
 * Check if user has specific role
 */
export async function hasRole(page: Page, role: string): Promise<boolean> {
  const roles = await getUserRoles(page);
  return roles.includes(role);
}

/**
 * Set authentication state in browser context
 */
export async function setAuthState(context: BrowserContext, tokens: AuthTokens): Promise<void> {
  await context.addInitScript((tokens) => {
    localStorage.setItem('auth_token', tokens.accessToken);
    if (tokens.refreshToken) {
      localStorage.setItem('refresh_token', tokens.refreshToken);
    }
  }, tokens);
}

/**
 * Clear authentication state
 */
export async function clearAuthState(page: Page): Promise<void> {
  await page.evaluate(() => {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('refresh_token');
  });
}

/**
 * Wait for token to be set in localStorage
 */
export async function waitForAuthToken(page: Page, timeout: number = 10000): Promise<string> {
  return await page.waitForFunction(
    () => localStorage.getItem('auth_token'),
    {},
    { timeout }
  );
}
