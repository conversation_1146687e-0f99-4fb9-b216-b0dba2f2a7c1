/**
 * Test Setup and Cleanup Utilities for Jadwa Fund Management System
 * 
 * This module provides utilities for setting up test data, cleaning up
 * after tests, and managing test isolation.
 */

import { Page, BrowserContext } from '@playwright/test';
import { getCurrentEnvironment } from '../config/environments';
import { loginAs, UserRole, UserSession } from './auth';
import { 
  FundTestData, 
  ResolutionTestData, 
  BoardMemberTestData,
  TestDataGenerator 
} from '../fixtures/test-data';

export interface TestContext {
  session: UserSession;
  testData: {
    funds: FundTestData[];
    resolutions: ResolutionTestData[];
    boardMembers: BoardMemberTestData[];
  };
  cleanup: (() => Promise<void>)[];
}

/**
 * Setup test environment with authentication and test data
 */
export async function setupTest(
  page: Page, 
  role: UserRole,
  options?: {
    createFund?: boolean;
    createResolution?: boolean;
    createBoardMembers?: boolean;
  }
): Promise<TestContext> {
  const environment = getCurrentEnvironment();
  const cleanup: (() => Promise<void>)[] = [];
  
  // Login with specified role
  const session = await loginAs(page, role);
  
  // Initialize test data
  const testData = {
    funds: [] as FundTestData[],
    resolutions: [] as ResolutionTestData[],
    boardMembers: [] as BoardMemberTestData[]
  };
  
  // Create test fund if requested
  if (options?.createFund) {
    const fund = await createTestFund(page);
    testData.funds.push(fund);
    cleanup.push(() => deleteTestFund(page, fund.id!));
  }
  
  // Create test resolution if requested
  if (options?.createResolution && testData.funds.length > 0) {
    const resolution = await createTestResolution(page, testData.funds[0].id!);
    testData.resolutions.push(resolution);
    cleanup.push(() => deleteTestResolution(page, resolution.id!));
  }
  
  // Create test board members if requested
  if (options?.createBoardMembers && testData.funds.length > 0) {
    const members = await createTestBoardMembers(page, testData.funds[0].id!);
    testData.boardMembers.push(...members);
    cleanup.push(() => deleteTestBoardMembers(page, members.map(m => m.id!)));
  }
  
  return {
    session,
    testData,
    cleanup
  };
}

/**
 * Cleanup test data and logout
 */
export async function cleanupTest(context: TestContext): Promise<void> {
  // Execute all cleanup functions in reverse order
  for (const cleanupFn of context.cleanup.reverse()) {
    try {
      await cleanupFn();
    } catch (error) {
      console.warn('Cleanup function failed:', error);
    }
  }
}

/**
 * Create a test fund
 */
export async function createTestFund(page: Page): Promise<FundTestData> {
  const fund = TestDataGenerator.generateFund();
  
  // Navigate to create fund page
  await page.goto('/admin/investment-funds/create');
  
  // Fill fund form
  await page.fill('[data-testid="fund-name-ar"]', fund.name.ar);
  await page.fill('[data-testid="fund-name-en"]', fund.name.en);
  await page.fill('[data-testid="fund-description-ar"]', fund.description.ar);
  await page.fill('[data-testid="fund-description-en"]', fund.description.en);
  await page.selectOption('[data-testid="fund-strategy"]', fund.strategy);
  await page.fill('[data-testid="fund-exit-date"]', fund.exitDate);
  
  // Submit form
  await page.click('[data-testid="save-fund-button"]');
  
  // Wait for success message
  await page.waitForSelector('[data-testid="success-message"]');
  
  // Extract fund ID from URL or response
  const fundId = await extractFundIdFromResponse(page);
  fund.id = fundId;
  
  return fund;
}

/**
 * Create a test resolution
 */
export async function createTestResolution(page: Page, fundId: number): Promise<ResolutionTestData> {
  const resolution = TestDataGenerator.generateResolution(fundId);
  
  // Navigate to fund details and then create resolution
  await page.goto(`/admin/investment-funds/${fundId}/details`);
  await page.click('[data-testid="resolutions-tab"]');
  await page.click('[data-testid="create-resolution-button"]');
  
  // Fill resolution form
  await page.fill('[data-testid="resolution-date"]', resolution.date);
  await page.selectOption('[data-testid="resolution-type"]', resolution.type.en);
  await page.fill('[data-testid="resolution-description"]', resolution.description.ar);
  await page.selectOption('[data-testid="voting-methodology"]', resolution.votingMethodology);
  await page.selectOption('[data-testid="member-voting-result"]', resolution.memberVotingResult);
  
  // Save as draft
  await page.click('[data-testid="save-draft-button"]');
  
  // Wait for success message
  await page.waitForSelector('[data-testid="success-message"]');
  
  // Extract resolution ID
  const resolutionId = await extractResolutionIdFromResponse(page);
  resolution.id = resolutionId;
  
  return resolution;
}

/**
 * Create test board members
 */
export async function createTestBoardMembers(page: Page, fundId: number): Promise<BoardMemberTestData[]> {
  const members: BoardMemberTestData[] = [];
  
  // Navigate to fund members
  await page.goto(`/admin/investment-funds/${fundId}/details`);
  await page.click('[data-testid="members-tab"]');
  
  // Create chairman (independent member)
  const chairman = TestDataGenerator.generateBoardMember(fundId, {
    isChairman: true,
    memberType: 'independent'
  });
  
  await page.click('[data-testid="add-member-button"]');
  await page.selectOption('[data-testid="member-user"]', chairman.userId);
  await page.check('[data-testid="member-type-independent"]');
  await page.check('[data-testid="is-chairman"]');
  await page.click('[data-testid="save-member-button"]');
  
  await page.waitForSelector('[data-testid="success-message"]');
  chairman.id = await extractMemberIdFromResponse(page);
  members.push(chairman);
  
  // Create second independent member
  const independentMember = TestDataGenerator.generateBoardMember(fundId, {
    isChairman: false,
    memberType: 'independent'
  });
  
  await page.click('[data-testid="add-member-button"]');
  await page.selectOption('[data-testid="member-user"]', independentMember.userId);
  await page.check('[data-testid="member-type-independent"]');
  await page.click('[data-testid="save-member-button"]');
  
  await page.waitForSelector('[data-testid="success-message"]');
  independentMember.id = await extractMemberIdFromResponse(page);
  members.push(independentMember);
  
  return members;
}

/**
 * Delete test fund
 */
export async function deleteTestFund(page: Page, fundId: number): Promise<void> {
  try {
    await page.goto(`/admin/investment-funds/${fundId}/details`);
    await page.click('[data-testid="delete-fund-button"]');
    await page.click('[data-testid="confirm-delete-button"]');
    await page.waitForSelector('[data-testid="success-message"]');
  } catch (error) {
    console.warn(`Failed to delete test fund ${fundId}:`, error);
  }
}

/**
 * Delete test resolution
 */
export async function deleteTestResolution(page: Page, resolutionId: number): Promise<void> {
  try {
    // Navigate to resolution and delete if it's in draft status
    await page.goto(`/admin/resolutions/${resolutionId}/details`);
    await page.click('[data-testid="delete-resolution-button"]');
    await page.click('[data-testid="confirm-delete-button"]');
    await page.waitForSelector('[data-testid="success-message"]');
  } catch (error) {
    console.warn(`Failed to delete test resolution ${resolutionId}:`, error);
  }
}

/**
 * Delete test board members
 */
export async function deleteTestBoardMembers(page: Page, memberIds: number[]): Promise<void> {
  for (const memberId of memberIds) {
    try {
      await page.goto(`/admin/board-members/${memberId}/details`);
      await page.click('[data-testid="delete-member-button"]');
      await page.click('[data-testid="confirm-delete-button"]');
      await page.waitForSelector('[data-testid="success-message"]');
    } catch (error) {
      console.warn(`Failed to delete test board member ${memberId}:`, error);
    }
  }
}

/**
 * Reset database to clean state (for test isolation)
 */
export async function resetTestDatabase(): Promise<void> {
  const environment = getCurrentEnvironment();
  
  if (environment.name === 'production') {
    throw new Error('Cannot reset database in production environment');
  }
  
  // This would typically call a test API endpoint to reset the database
  // Implementation depends on backend test utilities
  console.log('Database reset not implemented - using cleanup functions instead');
}

/**
 * Seed database with test data
 */
export async function seedTestDatabase(): Promise<void> {
  const environment = getCurrentEnvironment();
  
  if (environment.name === 'production') {
    throw new Error('Cannot seed database in production environment');
  }
  
  // This would typically call a test API endpoint to seed the database
  // Implementation depends on backend test utilities
  console.log('Database seeding not implemented - using create functions instead');
}

/**
 * Extract fund ID from API response or URL
 */
async function extractFundIdFromResponse(page: Page): Promise<number> {
  // Wait for navigation to fund details page
  await page.waitForURL(/\/admin\/investment-funds\/\d+\/details/);
  
  // Extract ID from URL
  const url = page.url();
  const match = url.match(/\/admin\/investment-funds\/(\d+)\/details/);
  
  if (!match) {
    throw new Error('Could not extract fund ID from URL');
  }
  
  return parseInt(match[1], 10);
}

/**
 * Extract resolution ID from API response or URL
 */
async function extractResolutionIdFromResponse(page: Page): Promise<number> {
  // This would typically extract from API response or URL
  // For now, return a mock ID
  return Math.floor(Math.random() * 1000) + 1;
}

/**
 * Extract member ID from API response
 */
async function extractMemberIdFromResponse(page: Page): Promise<number> {
  // This would typically extract from API response
  // For now, return a mock ID
  return Math.floor(Math.random() * 1000) + 1;
}

/**
 * Wait for API call to complete
 */
export async function waitForApiCall(page: Page, endpoint: string): Promise<void> {
  await page.waitForResponse(response => 
    response.url().includes(endpoint) && response.status() === 200
  );
}

/**
 * Verify system message is displayed
 */
export async function verifySystemMessage(
  page: Page, 
  messageCode: string, 
  language: 'ar' | 'en' = 'ar'
): Promise<void> {
  const messageSelector = `[data-testid="system-message-${messageCode}"]`;
  await page.waitForSelector(messageSelector);
  
  // Verify message content if needed
  const messageElement = page.locator(messageSelector);
  await messageElement.waitFor({ state: 'visible' });
}
