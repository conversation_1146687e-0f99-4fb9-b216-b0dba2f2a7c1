# Selenium WebDriver Test Suite for Jadwa Fund Management System

This directory contains a comprehensive Selenium WebDriver test suite that provides an alternative automation framework alongside the existing Playwright tests. The Selenium implementation maintains the same test coverage, Arabic/English localization support, and business rule validation as the Playwright suite.

## 🎯 Overview

The Selenium test suite provides:
- **Alternative Framework**: Complete Selenium WebDriver implementation alongside Playwright
- **Cross-Browser Testing**: Chrome, Firefox, Edge, and Safari support
- **Dual-Language Testing**: Arabic (RTL) and English (LTR) localization validation
- **Role-Based Testing**: All 4 user roles with proper permission validation
- **Business Rule Coverage**: Complete resolution lifecycle and alternative workflows
- **Shared Test Data**: Uses the same environment configuration and test fixtures as Playwright

## 📁 Directory Structure

```
tests/selenium/
├── config/
│   ├── selenium.config.ts          # WebDriver configuration and browser setup
│   └── mocha.config.js             # Mocha test runner configuration
├── page-objects/
│   ├── login.page.ts               # Login page interactions
│   ├── dashboard.page.ts           # Dashboard page interactions
│   ├── funds.page.ts               # Fund management page interactions
│   └── resolutions.page.ts         # Resolution lifecycle page interactions
├── tests/
│   ├── authentication-selenium.spec.ts     # Authentication and RBAC tests
│   ├── resolution-lifecycle-selenium.spec.ts # Resolution state machine tests
│   └── localization-selenium.spec.ts       # Arabic/English localization tests
├── utils/
│   ├── selenium-base.ts            # Base utilities and helper functions
│   ├── driver-manager.ts           # WebDriver lifecycle management
│   ├── test-data-manager.ts        # Test data management (shared with Playwright)
│   └── reporting.ts                # Custom reporting utilities
├── setup/
│   └── global-setup.ts             # Global setup and teardown
└── README.md                       # This file
```

## 🚀 Quick Start

### Prerequisites

1. **Node.js Dependencies**: All Selenium dependencies are already installed
2. **Browser Drivers**: WebDriver managers handle driver installation automatically
3. **Environment Setup**: Uses the same environment configuration as Playwright tests

### Running Tests

```bash
# Run all Selenium tests
npm run test:selenium

# Run tests in specific browser
npm run test:selenium:chrome
npm run test:selenium:firefox
npm run test:selenium:edge
npm run test:selenium:safari

# Run tests in specific environment
npm run test:selenium:local
npm run test:selenium:test
npm run test:selenium:production

# Run specific test suites
npm run test:selenium:auth
npm run test:selenium:resolution
npm run test:selenium:localization

# View test reports
npm run test:selenium:report
```

### Framework Comparison

```bash
# Run both Playwright and Selenium tests
npm run test:all

# Compare results between frameworks
npm run test:compare
```

## 🔧 Configuration

### Browser Configuration

The Selenium suite supports multiple browser configurations with Arabic/English locales:

```typescript
// Available browser configurations
const browsers = [
  'chrome-ar',    // Chrome with Arabic locale
  'chrome-en',    // Chrome with English locale
  'firefox-ar',   // Firefox with Arabic locale
  'firefox-en',   // Firefox with English locale
  'edge-ar',      // Edge with Arabic locale
  'edge-en',      // Edge with English locale
  'safari-ar',    // Safari with Arabic locale (macOS only)
  'safari-en'     // Safari with English locale (macOS only)
];
```

### Environment Configuration

Selenium tests use the same environment configuration as Playwright:

```typescript
// Environment selection
ENVIRONMENT=local    # Local development
ENVIRONMENT=test     # Test environment
ENVIRONMENT=production # Production environment (read-only tests)
```

### Test Data Management

Selenium tests share the same test data fixtures and environment configuration:

```typescript
import { seleniumTestDataManager } from '../utils/test-data-manager';

// Get test credentials
const credentials = seleniumTestDataManager.getCredentials('fundManager');

// Get test data
const fundData = seleniumTestDataManager.getTestFund('ar');
const resolutionData = seleniumTestDataManager.getTestResolution('en');
```

## 🧪 Test Categories

### 1. Authentication and Role-Based Access Control
- **File**: `authentication-selenium.spec.ts`
- **Coverage**: JWT authentication, 4 user roles, permissions validation
- **Tests**: 25+ test scenarios across multiple browsers

### 2. Resolution Lifecycle Management
- **File**: `resolution-lifecycle-selenium.spec.ts`
- **Coverage**: Complete state machine, alternative workflows, business rules
- **Tests**: 20+ test scenarios covering all resolution states

### 3. Localization and Cross-Browser Testing
- **File**: `localization-selenium.spec.ts`
- **Coverage**: Arabic/English layouts, RTL/LTR support, font rendering
- **Tests**: 15+ test scenarios across browsers and languages

## 🌐 Cross-Browser Support

### Desktop Browsers
- **Chrome**: Full Arabic/English support with proper font rendering
- **Firefox**: Arabic text rendering and form input validation
- **Edge**: Microsoft Edge compatibility with localization
- **Safari**: WebKit-specific Arabic text rendering (macOS only)

### Mobile Testing
- **Responsive Design**: Mobile viewport testing for Arabic/English layouts
- **Touch Interactions**: Mobile-specific interaction testing
- **Device Simulation**: iPhone, Samsung Galaxy, iPad viewport testing

## 🔍 Page Object Model

The Selenium implementation uses the same Page Object Model pattern as Playwright:

```typescript
// Example: Login page usage
const loginPage = new LoginPage(driver);
await loginPage.navigateToLogin();
await loginPage.loginAndWaitForDashboard(username, password);

// Example: Dashboard interactions
const dashboardPage = new DashboardPage(driver);
await dashboardPage.switchLanguage();
expect(await dashboardPage.verifyArabicLayout()).to.be.true;
```

### Key Features
- **Consistent Interface**: Same methods as Playwright page objects
- **Language Support**: Built-in Arabic/English switching
- **Error Handling**: Robust error handling and retry logic
- **Performance Monitoring**: Built-in performance metrics collection

## 📊 Reporting

### HTML Reports
Selenium generates comprehensive HTML reports with:
- Test execution summary
- Browser-specific results
- Language distribution
- Performance metrics
- Failure screenshots

### JSON Reports
Machine-readable JSON reports for CI/CD integration:
```json
{
  "summary": {
    "totalTests": 60,
    "passedTests": 58,
    "failedTests": 2,
    "passRate": 97,
    "browserStats": {...},
    "languageStats": {...}
  },
  "suites": [...],
  "results": [...]
}
```

### Framework Comparison
Automated comparison reports between Playwright and Selenium results:
- Side-by-side metrics comparison
- Performance analysis
- Coverage comparison
- Reliability assessment

## 🎭 Driver Management

### Automatic Driver Management
```typescript
// Get driver instance (automatically managed)
const driver = await DriverManager.getDriver('chrome-ar');

// Driver pool for parallel execution
const pooledDriver = await driverPool.getDriver('firefox-en');
```

### Features
- **Automatic Cleanup**: Drivers are automatically cleaned up after tests
- **Pool Management**: Driver pooling for efficient resource usage
- **Error Recovery**: Automatic driver restart on failures
- **Performance Monitoring**: Driver performance metrics collection

## 🔧 Utilities and Helpers

### Base Utilities
```typescript
// Selenium base class with common utilities
class SeleniumBase {
  async waitForElement(locator: By): Promise<WebElement>
  async clickElement(locator: By): Promise<void>
  async fillInput(locator: By, value: string): Promise<void>
  async switchLanguage(language: 'ar' | 'en'): Promise<void>
}
```

### Test Data Management
```typescript
// Shared test data with Playwright
const testData = seleniumTestDataManager.getTestScenarioData('fund-creation', 'ar');
```

## 🚀 CI/CD Integration

### GitHub Actions
```yaml
- name: Run Selenium Tests
  run: npm run test:selenium
  
- name: Upload Selenium Reports
  uses: actions/upload-artifact@v3
  with:
    name: selenium-reports
    path: test-results/selenium/
```

### Environment Variables
```bash
# Browser selection
BROWSER=chrome|firefox|edge|safari

# Environment selection
ENVIRONMENT=local|test|production

# Language selection
LANGUAGE=ar|en

# Headless mode
HEADLESS=true|false
```

## 🔍 Debugging

### Debug Mode
```bash
# Run tests with debug output
DEBUG=true npm run test:selenium

# Run specific test with debugging
npx mocha tests/selenium/tests/authentication-selenium.spec.ts --timeout 0
```

### Screenshots on Failure
Automatic screenshot capture on test failures:
```typescript
// Automatic screenshot on failure
afterEach(async function() {
  if (this.currentTest.state === 'failed') {
    const screenshot = await driver.takeScreenshot();
    // Screenshot saved to test-results/selenium/screenshots/
  }
});
```

## 📈 Performance Monitoring

### Metrics Collection
- **Page Load Times**: Automatic measurement of page load performance
- **Element Interaction Times**: Timing of user interactions
- **Browser-Specific Performance**: Performance comparison across browsers
- **Language-Specific Performance**: Arabic vs English performance analysis

### Performance Thresholds
```typescript
const performanceThresholds = {
  loginPage: 3000,      // 3 seconds
  dashboard: 5000,      // 5 seconds
  fundsList: 4000,      // 4 seconds
  resolutionsList: 4000 // 4 seconds
};
```

## 🔒 Security Testing

### Authentication Security
- JWT token validation
- Session timeout testing
- CSRF protection verification
- Unauthorized access prevention

### Data Security
- Input validation testing
- XSS prevention validation
- SQL injection prevention
- File upload security

## 🌍 Localization Testing

### Arabic Language Support
- **RTL Layout**: Right-to-left text direction validation
- **Font Rendering**: Arabic character and diacritics support
- **Unicode Handling**: Comprehensive Arabic text validation
- **Form Inputs**: Arabic text input and validation

### English Language Support
- **LTR Layout**: Left-to-right text direction validation
- **Cross-Browser**: Consistent rendering across browsers
- **Form Validation**: English error messages and validation

### Language Switching
- **Persistence**: Language preference maintained across sessions
- **Real-time**: Immediate interface updates on language change
- **Content**: Dynamic content switching validation

## 🤝 Comparison with Playwright

| Feature | Playwright | Selenium |
|---------|------------|----------|
| **Browser Support** | Chrome, Firefox, Safari, Edge | Chrome, Firefox, Safari, Edge |
| **Language Support** | Arabic/English RTL/LTR | Arabic/English RTL/LTR |
| **Test Framework** | Built-in test runner | Mocha + Chai |
| **Page Objects** | Custom implementation | Custom implementation |
| **Reporting** | HTML, JSON, Allure | HTML, JSON, Mochawesome |
| **Performance** | Fast execution | Standard execution |
| **Debugging** | Excellent debugging tools | Standard debugging |
| **CI/CD Integration** | Excellent | Good |
| **Learning Curve** | Moderate | Moderate |

## 🎯 Best Practices

### Test Organization
1. **Group Related Tests**: Use describe blocks for logical grouping
2. **Shared Setup**: Use beforeEach/afterEach for common setup
3. **Data Isolation**: Each test should create and clean up its own data
4. **Error Handling**: Proper error handling and cleanup in all tests

### Performance Optimization
1. **Driver Reuse**: Reuse drivers across tests in the same suite
2. **Parallel Execution**: Use driver pools for parallel test execution
3. **Smart Waits**: Use explicit waits instead of sleep statements
4. **Resource Cleanup**: Always clean up resources after tests

### Maintenance
1. **Regular Updates**: Keep WebDriver versions updated
2. **Browser Compatibility**: Test with latest browser versions
3. **Documentation**: Keep documentation current with changes
4. **Code Reviews**: Regular code reviews for test quality

## 🆘 Troubleshooting

### Common Issues

#### Driver Not Found
```bash
# Install specific driver
npx webdriver-manager update --versions.chrome=latest
```

#### Browser Compatibility
```bash
# Check browser availability
npm run test:selenium -- --grep "browser availability"
```

#### Arabic Text Rendering
```bash
# Test Arabic font rendering
npm run test:selenium:localization -- --grep "Arabic font"
```

#### Performance Issues
```bash
# Run performance-specific tests
npm run test:selenium -- --grep "performance"
```

### Getting Help
1. **Check Logs**: Review test execution logs for detailed error information
2. **Screenshots**: Check failure screenshots in test-results/selenium/screenshots/
3. **Reports**: Review HTML reports for detailed test results
4. **Documentation**: Refer to Selenium WebDriver documentation for advanced usage

## 📝 Contributing

### Adding New Tests
1. Follow existing page object patterns
2. Include Arabic/English language testing
3. Add appropriate test data fixtures
4. Update documentation

### Modifying Existing Tests
1. Ensure backward compatibility
2. Update related documentation
3. Test across all supported browsers
4. Verify localization support

This Selenium implementation provides a robust alternative to Playwright while maintaining the same comprehensive test coverage and quality standards for the Jadwa Fund Management System.
