/**
 * Mocha Configuration for Selenium Tests
 * 
 * This configuration file sets up Mocha test runner for Selenium WebDriver tests
 * with proper timeouts, reporters, and test organization.
 */

module.exports = {
  // Test file patterns
  spec: [
    'tests/selenium/tests/**/*.spec.ts'
  ],
  
  // Exclude patterns
  ignore: [
    'tests/selenium/tests/**/*.skip.ts',
    'tests/selenium/tests/**/*.disabled.ts'
  ],
  
  // Test execution settings
  timeout: 120000, // 2 minutes default timeout
  slow: 30000,     // 30 seconds slow threshold
  bail: false,     // Continue running tests after failures
  
  // Parallel execution
  parallel: false, // Disable parallel for Selenium to avoid driver conflicts
  jobs: 1,         // Single job for Selenium tests
  
  // Reporters
  reporter: 'mochawesome',
  'reporter-options': [
    'reportDir=test-results/selenium',
    'reportFilename=selenium-test-results',
    'html=true',
    'json=true',
    'overwrite=false',
    'timestamp=mmddyyyy_HHMMss'
  ],
  
  // TypeScript support
  require: [
    'ts-node/register',
    'tests/selenium/setup/global-setup.ts'
  ],
  
  // Test environment
  recursive: true,
  extension: ['ts'],
  
  // Grep patterns for selective test execution
  grep: process.env.TEST_GREP || '',
  
  // Retry configuration
  retries: process.env.CI ? 2 : 0,
  
  // Exit behavior
  exit: true,
  
  // Global setup and teardown
  globalSetup: 'tests/selenium/setup/global-setup.ts',
  globalTeardown: 'tests/selenium/setup/global-teardown.ts',
  
  // Watch mode settings
  watch: false,
  watchFiles: [
    'tests/selenium/**/*.ts',
    'tests/selenium/**/*.js'
  ],
  
  // UI settings
  ui: 'bdd',
  color: true,
  
  // Diff settings
  diff: true,
  'full-trace': true,
  
  // Node options
  'node-option': [
    'loader=ts-node/esm',
    'experimental-specifier-resolution=node'
  ]
};
