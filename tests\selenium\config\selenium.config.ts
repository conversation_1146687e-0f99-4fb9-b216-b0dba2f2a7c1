/**
 * Selenium WebDriver Configuration for Jadwa Fund Management System
 * 
 * This configuration provides browser setup, driver management, and
 * test execution settings for Selenium WebDriver tests.
 */

import { Builder, WebDriver, Capabilities } from 'selenium-webdriver';
import chrome from 'selenium-webdriver/chrome';
import firefox from 'selenium-webdriver/firefox';
import edge from 'selenium-webdriver/edge';
import safari from 'selenium-webdriver/safari';
import { getCurrentEnvironment } from '../../e2e/config/environments';

export interface SeleniumConfig {
  browser: 'chrome' | 'firefox' | 'edge' | 'safari';
  headless: boolean;
  timeout: {
    implicit: number;
    explicit: number;
    page: number;
  };
  window: {
    width: number;
    height: number;
  };
  locale: 'ar-EG' | 'en-US';
  enableLogging: boolean;
}

export const defaultSeleniumConfig: SeleniumConfig = {
  browser: 'chrome',
  headless: process.env.CI ? true : false,
  timeout: {
    implicit: 10000,
    explicit: 30000,
    page: 60000
  },
  window: {
    width: 1280,
    height: 720
  },
  locale: 'ar-EG',
  enableLogging: true
};

/**
 * Browser configurations for different browsers and locales
 */
export const browserConfigs = {
  'chrome-ar': {
    ...defaultSeleniumConfig,
    browser: 'chrome' as const,
    locale: 'ar-EG' as const
  },
  'chrome-en': {
    ...defaultSeleniumConfig,
    browser: 'chrome' as const,
    locale: 'en-US' as const
  },
  'firefox-ar': {
    ...defaultSeleniumConfig,
    browser: 'firefox' as const,
    locale: 'ar-EG' as const
  },
  'firefox-en': {
    ...defaultSeleniumConfig,
    browser: 'firefox' as const,
    locale: 'en-US' as const
  },
  'edge-ar': {
    ...defaultSeleniumConfig,
    browser: 'edge' as const,
    locale: 'ar-EG' as const
  },
  'edge-en': {
    ...defaultSeleniumConfig,
    browser: 'edge' as const,
    locale: 'en-US' as const
  },
  'safari-ar': {
    ...defaultSeleniumConfig,
    browser: 'safari' as const,
    locale: 'ar-EG' as const
  },
  'safari-en': {
    ...defaultSeleniumConfig,
    browser: 'safari' as const,
    locale: 'en-US' as const
  }
};

/**
 * Create WebDriver instance based on configuration
 */
export async function createWebDriver(config: SeleniumConfig = defaultSeleniumConfig): Promise<WebDriver> {
  const environment = getCurrentEnvironment();
  let driver: WebDriver;

  switch (config.browser) {
    case 'chrome':
      driver = await createChromeDriver(config);
      break;
    case 'firefox':
      driver = await createFirefoxDriver(config);
      break;
    case 'edge':
      driver = await createEdgeDriver(config);
      break;
    case 'safari':
      driver = await createSafariDriver(config);
      break;
    default:
      throw new Error(`Unsupported browser: ${config.browser}`);
  }

  // Set timeouts
  await driver.manage().setTimeouts({
    implicit: config.timeout.implicit,
    pageLoad: config.timeout.page,
    script: config.timeout.explicit
  });

  // Set window size
  await driver.manage().window().setRect({
    width: config.window.width,
    height: config.window.height
  });

  return driver;
}

/**
 * Create Chrome WebDriver with Arabic/English locale support
 */
async function createChromeDriver(config: SeleniumConfig): Promise<WebDriver> {
  const options = new chrome.Options();
  
  if (config.headless) {
    options.addArguments('--headless=new');
  }
  
  // Chrome arguments for better testing
  options.addArguments(
    '--no-sandbox',
    '--disable-dev-shm-usage',
    '--disable-gpu',
    '--disable-web-security',
    '--allow-running-insecure-content',
    '--ignore-certificate-errors',
    '--ignore-ssl-errors',
    '--ignore-certificate-errors-spki-list'
  );

  // Set locale for Arabic/English support
  if (config.locale === 'ar-EG') {
    options.addArguments('--lang=ar-EG');
    options.setUserPreferences({
      'intl.accept_languages': 'ar-EG,ar,en',
      'profile.default_content_setting_values.notifications': 2
    });
  } else {
    options.addArguments('--lang=en-US');
    options.setUserPreferences({
      'intl.accept_languages': 'en-US,en',
      'profile.default_content_setting_values.notifications': 2
    });
  }

  // Enable logging if required
  if (config.enableLogging) {
    options.setLoggingPrefs({
      browser: 'ALL',
      driver: 'ALL',
      performance: 'ALL'
    });
  }

  return new Builder()
    .forBrowser('chrome')
    .setChromeOptions(options)
    .build();
}

/**
 * Create Firefox WebDriver with Arabic/English locale support
 */
async function createFirefoxDriver(config: SeleniumConfig): Promise<WebDriver> {
  const options = new firefox.Options();
  
  if (config.headless) {
    options.addArguments('--headless');
  }

  // Firefox preferences for Arabic/English support
  const prefs = new Map();
  
  if (config.locale === 'ar-EG') {
    prefs.set('intl.accept_languages', 'ar-eg,ar,en');
    prefs.set('general.useragent.locale', 'ar-EG');
  } else {
    prefs.set('intl.accept_languages', 'en-us,en');
    prefs.set('general.useragent.locale', 'en-US');
  }

  // Additional Firefox preferences
  prefs.set('dom.webnotifications.enabled', false);
  prefs.set('media.navigator.permission.disabled', true);
  prefs.set('geo.enabled', false);

  options.setPreference('intl.accept_languages', prefs.get('intl.accept_languages'));
  options.setPreference('general.useragent.locale', prefs.get('general.useragent.locale'));
  options.setPreference('dom.webnotifications.enabled', false);

  return new Builder()
    .forBrowser('firefox')
    .setFirefoxOptions(options)
    .build();
}

/**
 * Create Edge WebDriver with Arabic/English locale support
 */
async function createEdgeDriver(config: SeleniumConfig): Promise<WebDriver> {
  const options = new edge.Options();
  
  if (config.headless) {
    options.addArguments('--headless=new');
  }

  // Edge arguments (similar to Chrome)
  options.addArguments(
    '--no-sandbox',
    '--disable-dev-shm-usage',
    '--disable-gpu',
    '--disable-web-security',
    '--ignore-certificate-errors'
  );

  // Set locale for Arabic/English support
  if (config.locale === 'ar-EG') {
    options.addArguments('--lang=ar-EG');
  } else {
    options.addArguments('--lang=en-US');
  }

  return new Builder()
    .forBrowser('MicrosoftEdge')
    .setEdgeOptions(options)
    .build();
}

/**
 * Create Safari WebDriver (macOS only)
 */
async function createSafariDriver(config: SeleniumConfig): Promise<WebDriver> {
  const options = new safari.Options();
  
  // Safari doesn't support headless mode
  if (config.headless) {
    console.warn('Safari does not support headless mode, running in headed mode');
  }

  return new Builder()
    .forBrowser('safari')
    .setSafariOptions(options)
    .build();
}

/**
 * Get browser configuration by name
 */
export function getBrowserConfig(browserName: keyof typeof browserConfigs): SeleniumConfig {
  const config = browserConfigs[browserName];
  if (!config) {
    throw new Error(`Browser configuration not found: ${browserName}`);
  }
  return config;
}

/**
 * Get all available browser configurations
 */
export function getAllBrowserConfigs(): Array<{ name: string; config: SeleniumConfig }> {
  return Object.entries(browserConfigs).map(([name, config]) => ({
    name,
    config
  }));
}

/**
 * Cleanup WebDriver instance
 */
export async function cleanupWebDriver(driver: WebDriver): Promise<void> {
  try {
    await driver.quit();
  } catch (error) {
    console.warn('Error during WebDriver cleanup:', error);
  }
}

/**
 * Check if browser is available on current platform
 */
export function isBrowserAvailable(browser: string): boolean {
  switch (browser) {
    case 'chrome':
    case 'firefox':
    case 'edge':
      return true;
    case 'safari':
      return process.platform === 'darwin'; // macOS only
    default:
      return false;
  }
}

/**
 * Get available browsers for current platform
 */
export function getAvailableBrowsers(): string[] {
  return Object.keys(browserConfigs).filter(browserName => {
    const browser = browserName.split('-')[0];
    return isBrowserAvailable(browser);
  });
}
