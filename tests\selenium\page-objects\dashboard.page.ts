/**
 * Dashboard Page Object for Jadwa Fund Management System (Selenium)
 * 
 * This page object handles all interactions with the main dashboard using Selenium WebDriver,
 * including navigation, fund overview, and notifications.
 */

import { WebDriver, By, until } from 'selenium-webdriver';
import { SeleniumBase } from '../utils/selenium-base';

export class DashboardPage extends SeleniumBase {
  // Navigation elements
  private readonly sideMenu = By.css('[data-testid="side-menu"]');
  private readonly fundsMenuItem = By.css('[data-testid="funds-menu-item"]');
  private readonly strategiesMenuItem = By.css('[data-testid="strategies-menu-item"]');
  private readonly userMenu = By.css('[data-testid="user-menu"]');
  private readonly logoutButton = By.css('[data-testid="logout-button"]');
  private readonly languageToggle = By.css('[data-testid="language-toggle"]');

  // Dashboard content
  private readonly pageTitle = By.css('[data-testid="page-title"]');
  private readonly welcomeMessage = By.css('[data-testid="welcome-message"]');
  private readonly fundCards = By.css('[data-testid="fund-card"]');
  private readonly notificationCounter = By.css('[data-testid="notification-counter"]');
  private readonly notificationPanel = By.css('[data-testid="notification-panel"]');

  // Fund overview cards
  private readonly totalFundsCard = By.css('[data-testid="total-funds-card"]');
  private readonly activeFundsCard = By.css('[data-testid="active-funds-card"]');
  private readonly pendingResolutionsCard = By.css('[data-testid="pending-resolutions-card"]');
  private readonly votingInProgressCard = By.css('[data-testid="voting-in-progress-card"]');

  // Quick actions
  private readonly createFundButton = By.css('[data-testid="create-fund-button"]');
  private readonly viewAllFundsButton = By.css('[data-testid="view-all-funds-button"]');
  private readonly viewNotificationsButton = By.css('[data-testid="view-notifications-button"]');

  constructor(driver: WebDriver) {
    super(driver);
  }

  /**
   * Navigate to dashboard
   */
  async navigateToDashboard(): Promise<void> {
    await this.goto('/admin/dashboard');
    await this.waitForPageLoad();
  }

  /**
   * Verify dashboard is loaded
   */
  async verifyDashboardLoaded(): Promise<boolean> {
    try {
      await this.waitForElement(this.pageTitle);
      await this.waitForElement(this.sideMenu);
      return await this.verifyUrlContains('/admin/dashboard');
    } catch {
      return false;
    }
  }

  /**
   * Verify welcome message for user
   */
  async verifyWelcomeMessage(userName?: string): Promise<boolean> {
    try {
      await this.waitForElement(this.welcomeMessage);
      
      if (userName) {
        const messageText = await this.getElementText(this.welcomeMessage);
        return messageText.includes(userName);
      }
      
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Navigate to funds page
   */
  async navigateToFunds(): Promise<void> {
    await this.clickElement(this.fundsMenuItem);
    await this.waitForPageLoad();
    await this.driver.wait(until.urlContains('/admin/investment-funds'), 10000);
  }

  /**
   * Navigate to fund strategies page
   */
  async navigateToStrategies(): Promise<void> {
    await this.clickElement(this.strategiesMenuItem);
    await this.waitForPageLoad();
    await this.driver.wait(until.urlContains('/admin/fund-strategies'), 10000);
  }

  /**
   * Open user menu
   */
  async openUserMenu(): Promise<void> {
    await this.clickElement(this.userMenu);
  }

  /**
   * Logout from application
   */
  async logout(): Promise<void> {
    await this.openUserMenu();
    await this.clickElement(this.logoutButton);
    await this.driver.wait(until.urlContains('/auth/login'), 10000);
  }

  /**
   * Switch language
   */
  async switchLanguage(): Promise<void> {
    await this.clickElement(this.languageToggle);
    await this.waitForPageLoad();
  }

  /**
   * Get total funds count
   */
  async getTotalFundsCount(): Promise<number> {
    try {
      const countElement = await this.driver.findElement(this.totalFundsCard).findElement(By.css('[data-testid="count"]'));
      const countText = await countElement.getText();
      return parseInt(countText || '0', 10);
    } catch {
      return 0;
    }
  }

  /**
   * Get active funds count
   */
  async getActiveFundsCount(): Promise<number> {
    try {
      const countElement = await this.driver.findElement(this.activeFundsCard).findElement(By.css('[data-testid="count"]'));
      const countText = await countElement.getText();
      return parseInt(countText || '0', 10);
    } catch {
      return 0;
    }
  }

  /**
   * Get pending resolutions count
   */
  async getPendingResolutionsCount(): Promise<number> {
    try {
      const countElement = await this.driver.findElement(this.pendingResolutionsCard).findElement(By.css('[data-testid="count"]'));
      const countText = await countElement.getText();
      return parseInt(countText || '0', 10);
    } catch {
      return 0;
    }
  }

  /**
   * Get voting in progress count
   */
  async getVotingInProgressCount(): Promise<number> {
    try {
      const countElement = await this.driver.findElement(this.votingInProgressCard).findElement(By.css('[data-testid="count"]'));
      const countText = await countElement.getText();
      return parseInt(countText || '0', 10);
    } catch {
      return 0;
    }
  }

  /**
   * Click create fund button
   */
  async clickCreateFund(): Promise<void> {
    await this.clickElement(this.createFundButton);
    await this.waitForPageLoad();
  }

  /**
   * Click view all funds button
   */
  async clickViewAllFunds(): Promise<void> {
    await this.clickElement(this.viewAllFundsButton);
    await this.waitForPageLoad();
  }

  /**
   * Get notification count
   */
  async getNotificationCount(): Promise<number> {
    try {
      const countText = await this.getElementText(this.notificationCounter);
      return parseInt(countText || '0', 10);
    } catch {
      return 0;
    }
  }

  /**
   * Open notifications panel
   */
  async openNotifications(): Promise<void> {
    await this.clickElement(this.viewNotificationsButton);
    await this.waitForElement(this.notificationPanel);
  }

  /**
   * Close notifications panel
   */
  async closeNotifications(): Promise<void> {
    const closeButton = By.css('[data-testid="close-notifications"]');
    await this.clickElement(closeButton);
    await this.waitForElementToHide(this.notificationPanel);
  }

  /**
   * Get fund cards count
   */
  async getFundCardsCount(): Promise<number> {
    try {
      const fundCardElements = await this.findElements(this.fundCards);
      return fundCardElements.length;
    } catch {
      return 0;
    }
  }

  /**
   * Click on specific fund card
   */
  async clickFundCard(fundName: string): Promise<void> {
    const fundCard = By.xpath(`//div[@data-testid="fund-card"][contains(text(), "${fundName}")]`);
    await this.clickElement(fundCard);
    await this.waitForPageLoad();
  }

  /**
   * Verify fund card exists
   */
  async verifyFundCardExists(fundName: string): Promise<boolean> {
    const fundCard = By.xpath(`//div[@data-testid="fund-card"][contains(text(), "${fundName}")]`);
    return await this.isElementVisible(fundCard);
  }

  /**
   * Verify dashboard statistics
   */
  async verifyDashboardStatistics(): Promise<boolean> {
    try {
      await this.waitForElement(this.totalFundsCard);
      await this.waitForElement(this.activeFundsCard);
      await this.waitForElement(this.pendingResolutionsCard);
      await this.waitForElement(this.votingInProgressCard);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Verify user has access to create fund
   */
  async verifyCreateFundAccess(): Promise<boolean> {
    try {
      await this.waitForElement(this.createFundButton);
      return await this.isElementEnabled(this.createFundButton);
    } catch {
      return false;
    }
  }

  /**
   * Verify user does not have access to create fund
   */
  async verifyNoCreateFundAccess(): Promise<boolean> {
    return !(await this.isElementVisible(this.createFundButton, 2000));
  }

  /**
   * Verify side menu items based on user role
   */
  async verifySideMenuForRole(role: string): Promise<boolean> {
    try {
      await this.waitForElement(this.sideMenu);
      await this.waitForElement(this.fundsMenuItem);
      
      if (role === 'admin' || role === 'fundmanager') {
        return await this.isElementVisible(this.strategiesMenuItem);
      }
      
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Verify Arabic layout
   */
  async verifyArabicLayout(): Promise<boolean> {
    try {
      const bodyDir = await this.getElementAttribute(By.tagName('body'), 'dir');
      if (bodyDir !== 'rtl') {
        return false;
      }
      
      // Verify Arabic text in page title
      const titleText = await this.getElementText(this.pageTitle);
      return titleText.includes('لوحة التحكم');
    } catch {
      return false;
    }
  }

  /**
   * Verify English layout
   */
  async verifyEnglishLayout(): Promise<boolean> {
    try {
      const bodyDir = await this.getElementAttribute(By.tagName('body'), 'dir');
      if (bodyDir !== 'ltr') {
        return false;
      }
      
      // Verify English text in page title
      const titleText = await this.getElementText(this.pageTitle);
      return titleText.includes('Dashboard');
    } catch {
      return false;
    }
  }

  /**
   * Wait for dashboard data to load
   */
  async waitForDashboardDataLoad(): Promise<void> {
    await this.waitForLoadingToComplete();
    
    // Wait for statistics to be populated
    await this.driver.wait(async () => {
      try {
        const totalFundsElement = await this.driver.findElement(this.totalFundsCard).findElement(By.css('[data-testid="count"]'));
        const count = await totalFundsElement.getText();
        return count !== '0';
      } catch {
        return false;
      }
    }, 10000);
  }

  /**
   * Verify dashboard performance
   */
  async verifyDashboardPerformance(): Promise<boolean> {
    try {
      const metrics = await this.getPerformanceMetrics();
      
      // Dashboard should load quickly
      return metrics.domContentLoaded < 2000 && metrics.loadComplete < 4000;
    } catch {
      return false;
    }
  }

  /**
   * Verify responsive design
   */
  async verifyResponsiveDesign(): Promise<boolean> {
    try {
      // Test mobile view
      await this.driver.manage().window().setRect({ width: 375, height: 667 });
      const mobileLoaded = await this.verifyDashboardLoaded();
      
      // Test tablet view
      await this.driver.manage().window().setRect({ width: 768, height: 1024 });
      const tabletLoaded = await this.verifyDashboardLoaded();
      
      // Test desktop view
      await this.driver.manage().window().setRect({ width: 1280, height: 720 });
      const desktopLoaded = await this.verifyDashboardLoaded();
      
      return mobileLoaded && tabletLoaded && desktopLoaded;
    } catch {
      return false;
    }
  }

  /**
   * Search for specific fund
   */
  async searchFund(fundName: string): Promise<void> {
    const searchInput = By.css('[data-testid="fund-search"]');
    await this.fillInput(searchInput, fundName);
    await this.waitForLoadingToComplete();
  }

  /**
   * Clear fund search
   */
  async clearFundSearch(): Promise<void> {
    const searchInput = By.css('[data-testid="fund-search"]');
    await this.fillInput(searchInput, '');
    await this.waitForLoadingToComplete();
  }

  /**
   * Get dashboard page title
   */
  async getPageTitle(): Promise<string> {
    return await this.getElementText(this.pageTitle);
  }

  /**
   * Check if notifications panel is open
   */
  async isNotificationsPanelOpen(): Promise<boolean> {
    return await this.isElementVisible(this.notificationPanel, 2000);
  }

  /**
   * Get welcome message text
   */
  async getWelcomeMessageText(): Promise<string> {
    return await this.getElementText(this.welcomeMessage);
  }

  /**
   * Verify all dashboard cards are loaded
   */
  async verifyAllCardsLoaded(): Promise<boolean> {
    try {
      const cards = [
        this.totalFundsCard,
        this.activeFundsCard,
        this.pendingResolutionsCard,
        this.votingInProgressCard
      ];
      
      for (const card of cards) {
        await this.waitForElement(card);
      }
      
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Wait for specific fund card to appear
   */
  async waitForFundCard(fundName: string, timeout: number = 10000): Promise<void> {
    const fundCard = By.xpath(`//div[@data-testid="fund-card"][contains(text(), "${fundName}")]`);
    await this.waitForElement(fundCard, timeout);
  }
}
