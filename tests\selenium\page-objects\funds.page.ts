/**
 * Funds Page Object for Jadwa Fund Management System (Selenium)
 * 
 * This page object handles all interactions with the funds list page using Selenium WebDriver,
 * including fund management, search, filtering, and navigation.
 */

import { WebDriver, By, until } from 'selenium-webdriver';
import { SeleniumBase } from '../utils/selenium-base';

export class FundsPage extends SeleniumBase {
  // Page elements
  private readonly pageTitle = By.css('[data-testid="page-title"]');
  private readonly breadcrumb = By.css('[data-testid="breadcrumb"]');
  private readonly createFundButton = By.css('[data-testid="create-fund-button"]');
  private readonly searchInput = By.css('[data-testid="fund-search-input"]');
  private readonly filterButton = By.css('[data-testid="filter-button"]');
  private readonly fundCards = By.css('[data-testid="fund-card"]');
  private readonly noFundsMessage = By.css('[data-testid="no-funds-message"]');
  private readonly loadingSpinner = By.css('[data-testid="loading-spinner"]');

  // Fund card elements
  private readonly fundCardTitle = By.css('[data-testid="fund-card-title"]');
  private readonly fundCardStatus = By.css('[data-testid="fund-card-status"]');
  private readonly fundCardStrategy = By.css('[data-testid="fund-card-strategy"]');
  private readonly fundCardActions = By.css('[data-testid="fund-card-actions"]');
  private readonly viewDetailsButton = By.css('[data-testid="view-details-button"]');
  private readonly editFundButton = By.css('[data-testid="edit-fund-button"]');

  // Filter panel
  private readonly filterPanel = By.css('[data-testid="filter-panel"]');
  private readonly statusFilter = By.css('[data-testid="status-filter"]');
  private readonly strategyFilter = By.css('[data-testid="strategy-filter"]');
  private readonly dateRangeFilter = By.css('[data-testid="date-range-filter"]');
  private readonly applyFiltersButton = By.css('[data-testid="apply-filters-button"]');
  private readonly clearFiltersButton = By.css('[data-testid="clear-filters-button"]');

  // Pagination
  private readonly paginationContainer = By.css('[data-testid="pagination"]');
  private readonly previousPageButton = By.css('[data-testid="previous-page"]');
  private readonly nextPageButton = By.css('[data-testid="next-page"]');
  private readonly pageNumbers = By.css('[data-testid="page-number"]');

  constructor(driver: WebDriver) {
    super(driver);
  }

  /**
   * Navigate to funds page
   */
  async navigateToFunds(): Promise<void> {
    await this.goto('/admin/investment-funds');
    await this.waitForPageLoad();
  }

  /**
   * Verify funds page is loaded
   */
  async verifyFundsPageLoaded(): Promise<boolean> {
    try {
      await this.waitForElement(this.pageTitle);
      return await this.verifyUrlContains('/admin/investment-funds');
    } catch {
      return false;
    }
  }

  /**
   * Click create fund button
   */
  async clickCreateFund(): Promise<void> {
    await this.clickElement(this.createFundButton);
    await this.waitForPageLoad();
  }

  /**
   * Search for funds
   */
  async searchFunds(searchTerm: string): Promise<void> {
    await this.fillInput(this.searchInput, searchTerm);
    await this.waitForLoadingToComplete();
  }

  /**
   * Clear search
   */
  async clearSearch(): Promise<void> {
    await this.fillInput(this.searchInput, '');
    await this.waitForLoadingToComplete();
  }

  /**
   * Open filter panel
   */
  async openFilterPanel(): Promise<void> {
    await this.clickElement(this.filterButton);
    await this.waitForElement(this.filterPanel);
  }

  /**
   * Close filter panel
   */
  async closeFilterPanel(): Promise<void> {
    const closeButton = By.css('[data-testid="close-filter"]');
    await this.clickElement(closeButton);
    await this.waitForElementToHide(this.filterPanel);
  }

  /**
   * Apply status filter
   */
  async filterByStatus(status: string): Promise<void> {
    await this.openFilterPanel();
    await this.selectOption(this.statusFilter, status);
    await this.clickElement(this.applyFiltersButton);
    await this.waitForLoadingToComplete();
  }

  /**
   * Apply strategy filter
   */
  async filterByStrategy(strategy: string): Promise<void> {
    await this.openFilterPanel();
    await this.selectOption(this.strategyFilter, strategy);
    await this.clickElement(this.applyFiltersButton);
    await this.waitForLoadingToComplete();
  }

  /**
   * Clear all filters
   */
  async clearAllFilters(): Promise<void> {
    await this.openFilterPanel();
    await this.clickElement(this.clearFiltersButton);
    await this.waitForLoadingToComplete();
  }

  /**
   * Get fund cards count
   */
  async getFundCardsCount(): Promise<number> {
    try {
      const fundCardElements = await this.findElements(this.fundCards);
      return fundCardElements.length;
    } catch {
      return 0;
    }
  }

  /**
   * Get fund card by name
   */
  private getFundCardByNameLocator(fundName: string): By {
    return By.xpath(`//div[@data-testid="fund-card"][contains(text(), "${fundName}")]`);
  }

  /**
   * Click fund card to view details
   */
  async clickFundCard(fundName: string): Promise<void> {
    const fundCard = this.getFundCardByNameLocator(fundName);
    await this.clickElement(fundCard);
    await this.waitForPageLoad();
  }

  /**
   * Click view details button for specific fund
   */
  async clickViewDetails(fundName: string): Promise<void> {
    const fundCard = this.getFundCardByNameLocator(fundName);
    const detailsButton = By.xpath(`//div[@data-testid="fund-card"][contains(text(), "${fundName}")]//button[@data-testid="view-details-button"]`);
    await this.clickElement(detailsButton);
    await this.waitForPageLoad();
  }

  /**
   * Click edit fund button for specific fund
   */
  async clickEditFund(fundName: string): Promise<void> {
    const fundCard = this.getFundCardByNameLocator(fundName);
    const editButton = By.xpath(`//div[@data-testid="fund-card"][contains(text(), "${fundName}")]//button[@data-testid="edit-fund-button"]`);
    await this.clickElement(editButton);
    await this.waitForPageLoad();
  }

  /**
   * Verify fund card exists
   */
  async verifyFundCardExists(fundName: string): Promise<boolean> {
    const fundCard = this.getFundCardByNameLocator(fundName);
    return await this.isElementVisible(fundCard);
  }

  /**
   * Verify fund card does not exist
   */
  async verifyFundCardNotExists(fundName: string): Promise<boolean> {
    const fundCard = this.getFundCardByNameLocator(fundName);
    return !(await this.isElementVisible(fundCard, 2000));
  }

  /**
   * Get fund status from card
   */
  async getFundStatus(fundName: string): Promise<string> {
    try {
      const statusElement = By.xpath(`//div[@data-testid="fund-card"][contains(text(), "${fundName}")]//span[@data-testid="fund-status"]`);
      return await this.getElementText(statusElement);
    } catch {
      return '';
    }
  }

  /**
   * Get fund strategy from card
   */
  async getFundStrategy(fundName: string): Promise<string> {
    try {
      const strategyElement = By.xpath(`//div[@data-testid="fund-card"][contains(text(), "${fundName}")]//span[@data-testid="fund-strategy"]`);
      return await this.getElementText(strategyElement);
    } catch {
      return '';
    }
  }

  /**
   * Verify no funds message is displayed
   */
  async verifyNoFundsMessage(): Promise<boolean> {
    return await this.isElementVisible(this.noFundsMessage);
  }

  /**
   * Verify funds are displayed
   */
  async verifyFundsDisplayed(): Promise<boolean> {
    return await this.isElementVisible(this.fundCards);
  }

  /**
   * Go to next page
   */
  async goToNextPage(): Promise<void> {
    await this.clickElement(this.nextPageButton);
    await this.waitForLoadingToComplete();
  }

  /**
   * Go to previous page
   */
  async goToPreviousPage(): Promise<void> {
    await this.clickElement(this.previousPageButton);
    await this.waitForLoadingToComplete();
  }

  /**
   * Go to specific page
   */
  async goToPage(pageNumber: number): Promise<void> {
    const pageButton = By.xpath(`//button[@data-testid="page-number"][text()="${pageNumber}"]`);
    await this.clickElement(pageButton);
    await this.waitForLoadingToComplete();
  }

  /**
   * Verify pagination is visible
   */
  async verifyPaginationVisible(): Promise<boolean> {
    return await this.isElementVisible(this.paginationContainer);
  }

  /**
   * Verify create fund button visibility based on permissions
   */
  async verifyCreateFundButtonVisibility(shouldBeVisible: boolean): Promise<boolean> {
    const isVisible = await this.isElementVisible(this.createFundButton, 2000);
    
    if (shouldBeVisible) {
      return isVisible && await this.isElementEnabled(this.createFundButton);
    } else {
      return !isVisible;
    }
  }

  /**
   * Verify fund card actions based on permissions
   */
  async verifyFundCardActions(fundName: string, canEdit: boolean): Promise<boolean> {
    const editButton = By.xpath(`//div[@data-testid="fund-card"][contains(text(), "${fundName}")]//button[@data-testid="edit-fund-button"]`);
    
    const isEditVisible = await this.isElementVisible(editButton, 2000);
    
    if (canEdit) {
      return isEditVisible;
    } else {
      return !isEditVisible;
    }
  }

  /**
   * Verify funds are grouped by strategy
   */
  async verifyFundsGroupedByStrategy(): Promise<boolean> {
    const strategyGroups = By.css('[data-testid="strategy-group"]');
    return await this.isElementVisible(strategyGroups);
  }

  /**
   * Expand strategy group
   */
  async expandStrategyGroup(strategyName: string): Promise<void> {
    const expandButton = By.xpath(`//div[@data-testid="strategy-group"][@data-strategy="${strategyName}"]//button[@data-testid="expand-button"]`);
    await this.clickElement(expandButton);
  }

  /**
   * Collapse strategy group
   */
  async collapseStrategyGroup(strategyName: string): Promise<void> {
    const collapseButton = By.xpath(`//div[@data-testid="strategy-group"][@data-strategy="${strategyName}"]//button[@data-testid="collapse-button"]`);
    await this.clickElement(collapseButton);
  }

  /**
   * Verify search results
   */
  async verifySearchResults(searchTerm: string): Promise<boolean> {
    try {
      const fundCardElements = await this.findElements(this.fundCards);
      
      for (const card of fundCardElements) {
        const cardText = await card.getText();
        if (!cardText.toLowerCase().includes(searchTerm.toLowerCase())) {
          return false;
        }
      }
      
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Verify filter results
   */
  async verifyFilterResults(filterType: 'status' | 'strategy', filterValue: string): Promise<boolean> {
    try {
      const fundCardElements = await this.findElements(this.fundCards);
      
      for (const card of fundCardElements) {
        const filterElement = await card.findElement(By.css(`[data-testid="fund-${filterType}"]`));
        const elementText = await filterElement.getText();
        
        if (!elementText.includes(filterValue)) {
          return false;
        }
      }
      
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Verify Arabic layout
   */
  async verifyArabicLayout(): Promise<boolean> {
    try {
      const bodyDir = await this.getElementAttribute(By.tagName('body'), 'dir');
      if (bodyDir !== 'rtl') {
        return false;
      }
      
      // Verify Arabic text in page title
      const titleText = await this.getElementText(this.pageTitle);
      return titleText.includes('الصناديق');
    } catch {
      return false;
    }
  }

  /**
   * Verify English layout
   */
  async verifyEnglishLayout(): Promise<boolean> {
    try {
      const bodyDir = await this.getElementAttribute(By.tagName('body'), 'dir');
      if (bodyDir !== 'ltr') {
        return false;
      }
      
      // Verify English text in page title
      const titleText = await this.getElementText(this.pageTitle);
      return titleText.includes('Funds');
    } catch {
      return false;
    }
  }

  /**
   * Wait for funds to load
   */
  async waitForFundsToLoad(): Promise<void> {
    await this.waitForLoadingToComplete();
    
    // Wait for either funds to appear or no funds message
    await this.driver.wait(async () => {
      const fundsVisible = await this.isElementVisible(this.fundCards, 1000);
      const noFundsVisible = await this.isElementVisible(this.noFundsMessage, 1000);
      return fundsVisible || noFundsVisible;
    }, 10000);
  }

  /**
   * Get page title text
   */
  async getPageTitle(): Promise<string> {
    return await this.getElementText(this.pageTitle);
  }

  /**
   * Check if filter panel is open
   */
  async isFilterPanelOpen(): Promise<boolean> {
    return await this.isElementVisible(this.filterPanel, 2000);
  }

  /**
   * Get search input value
   */
  async getSearchValue(): Promise<string> {
    return await this.getElementAttribute(this.searchInput, 'value');
  }

  /**
   * Verify fund card contains specific text
   */
  async verifyFundCardContainsText(fundName: string, expectedText: string): Promise<boolean> {
    try {
      const fundCard = this.getFundCardByNameLocator(fundName);
      const cardText = await this.getElementText(fundCard);
      return cardText.includes(expectedText);
    } catch {
      return false;
    }
  }

  /**
   * Wait for specific number of fund cards
   */
  async waitForFundCardsCount(expectedCount: number, timeout: number = 10000): Promise<void> {
    await this.waitForElementCount(this.fundCards, expectedCount, timeout);
  }

  /**
   * Check if pagination is enabled
   */
  async isPaginationEnabled(): Promise<boolean> {
    try {
      const nextButton = await this.driver.findElement(this.nextPageButton);
      const prevButton = await this.driver.findElement(this.previousPageButton);
      
      return (await nextButton.isEnabled()) || (await prevButton.isEnabled());
    } catch {
      return false;
    }
  }
}
