/**
 * Login Page Object for Jadwa Fund Management System (Selenium)
 * 
 * This page object handles all interactions with the login page using Selenium WebDriver,
 * including authentication, validation, and error handling.
 */

import { WebDriver, By, until } from 'selenium-webdriver';
import { SeleniumBase } from '../utils/selenium-base';

export class LoginPage extends SeleniumBase {
  // Page element locators
  private readonly usernameInput = By.css('[data-testid="username-input"]');
  private readonly passwordInput = By.css('[data-testid="password-input"]');
  private readonly loginButton = By.css('[data-testid="login-button"]');
  private readonly forgotPasswordLink = By.css('[data-testid="forgot-password-link"]');
  private readonly errorMessage = By.css('[data-testid="error-message"]');
  private readonly loadingSpinner = By.css('[data-testid="loading-spinner"]');
  private readonly languageToggle = By.css('[data-testid="language-toggle"]');
  private readonly rememberMeCheckbox = By.css('[data-testid="remember-me-checkbox"]');
  private readonly loginForm = By.css('[data-testid="login-form"]');

  constructor(driver: WebDriver) {
    super(driver);
  }

  /**
   * Navigate to login page
   */
  async navigateToLogin(): Promise<void> {
    await this.goto('/auth/login');
    await this.waitForPageLoad();
  }

  /**
   * Verify login page is loaded
   */
  async verifyLoginPageLoaded(): Promise<boolean> {
    try {
      await this.waitForElement(this.loginForm);
      await this.waitForElement(this.usernameInput);
      await this.waitForElement(this.passwordInput);
      await this.waitForElement(this.loginButton);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Enter username
   */
  async enterUsername(username: string): Promise<void> {
    await this.fillInput(this.usernameInput, username);
  }

  /**
   * Enter password
   */
  async enterPassword(password: string): Promise<void> {
    await this.fillInput(this.passwordInput, password);
  }

  /**
   * Click login button
   */
  async clickLogin(): Promise<void> {
    await this.clickElement(this.loginButton);
  }

  /**
   * Perform complete login process
   */
  async login(username: string, password: string): Promise<void> {
    await this.enterUsername(username);
    await this.enterPassword(password);
    await this.clickLogin();
  }

  /**
   * Login and wait for dashboard
   */
  async loginAndWaitForDashboard(username: string, password: string): Promise<void> {
    await this.login(username, password);
    await this.waitForSuccessfulLogin();
  }

  /**
   * Wait for successful login (redirect to dashboard)
   */
  async waitForSuccessfulLogin(): Promise<void> {
    await this.driver.wait(until.urlContains('/admin/dashboard'), 30000);
    await this.waitForPageLoad();
  }

  /**
   * Verify login error message
   */
  async verifyLoginError(expectedMessage?: string): Promise<boolean> {
    try {
      await this.waitForElement(this.errorMessage);
      
      if (expectedMessage) {
        const actualMessage = await this.getElementText(this.errorMessage);
        return actualMessage.includes(expectedMessage);
      }
      
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Check if login button is disabled
   */
  async isLoginButtonDisabled(): Promise<boolean> {
    try {
      return !(await this.isElementEnabled(this.loginButton));
    } catch {
      return true;
    }
  }

  /**
   * Check if loading spinner is visible
   */
  async isLoadingSpinnerVisible(): Promise<boolean> {
    return await this.isElementVisible(this.loadingSpinner, 2000);
  }

  /**
   * Toggle remember me checkbox
   */
  async toggleRememberMe(): Promise<void> {
    await this.clickElement(this.rememberMeCheckbox);
  }

  /**
   * Check if remember me is checked
   */
  async isRememberMeChecked(): Promise<boolean> {
    try {
      const element = await this.waitForElement(this.rememberMeCheckbox);
      const checked = await element.getAttribute('checked');
      return checked === 'true' || checked === 'checked';
    } catch {
      return false;
    }
  }

  /**
   * Click forgot password link
   */
  async clickForgotPassword(): Promise<void> {
    await this.clickElement(this.forgotPasswordLink);
  }

  /**
   * Switch language
   */
  async switchLanguage(): Promise<void> {
    await this.clickElement(this.languageToggle);
    await this.waitForPageLoad();
  }

  /**
   * Verify page is in Arabic
   */
  async verifyArabicLanguage(): Promise<boolean> {
    try {
      // Check for Arabic text or RTL direction
      const bodyDir = await this.getElementAttribute(By.tagName('body'), 'dir');
      if (bodyDir !== 'rtl') {
        return false;
      }
      
      // Check for Arabic login button text
      const buttonText = await this.getElementText(this.loginButton);
      return buttonText.includes('تسجيل الدخول');
    } catch {
      return false;
    }
  }

  /**
   * Verify page is in English
   */
  async verifyEnglishLanguage(): Promise<boolean> {
    try {
      // Check for LTR direction
      const bodyDir = await this.getElementAttribute(By.tagName('body'), 'dir');
      if (bodyDir !== 'ltr') {
        return false;
      }
      
      // Check for English login button text
      const buttonText = await this.getElementText(this.loginButton);
      return buttonText.includes('Login');
    } catch {
      return false;
    }
  }

  /**
   * Verify username field validation
   */
  async verifyUsernameValidation(): Promise<boolean> {
    try {
      // Clear username and try to login
      await this.fillInput(this.usernameInput, '');
      await this.clickLogin();
      
      // Check for validation message
      const validationMessage = By.css('[data-testid="username-validation"]');
      return await this.isElementVisible(validationMessage);
    } catch {
      return false;
    }
  }

  /**
   * Verify password field validation
   */
  async verifyPasswordValidation(): Promise<boolean> {
    try {
      // Clear password and try to login
      await this.fillInput(this.passwordInput, '');
      await this.clickLogin();
      
      // Check for validation message
      const validationMessage = By.css('[data-testid="password-validation"]');
      return await this.isElementVisible(validationMessage);
    } catch {
      return false;
    }
  }

  /**
   * Verify invalid credentials error
   */
  async verifyInvalidCredentialsError(): Promise<boolean> {
    return await this.verifyLoginError('Invalid username or password');
  }

  /**
   * Clear login form
   */
  async clearForm(): Promise<void> {
    await this.fillInput(this.usernameInput, '');
    await this.fillInput(this.passwordInput, '');
  }

  /**
   * Get username field value
   */
  async getUsernameValue(): Promise<string> {
    return await this.getElementAttribute(this.usernameInput, 'value');
  }

  /**
   * Get password field value
   */
  async getPasswordValue(): Promise<string> {
    return await this.getElementAttribute(this.passwordInput, 'value');
  }

  /**
   * Verify login form accessibility
   */
  async verifyAccessibility(): Promise<boolean> {
    try {
      // Check for proper labels
      const usernameLabel = await this.getElementAttribute(this.usernameInput, 'aria-label');
      const passwordLabel = await this.getElementAttribute(this.passwordInput, 'aria-label');
      
      if (!usernameLabel || !passwordLabel) {
        return false;
      }
      
      // Check for proper form structure
      const formRole = await this.getElementAttribute(this.loginForm, 'role');
      return formRole === 'form';
    } catch {
      return false;
    }
  }

  /**
   * Verify login page performance
   */
  async verifyPerformance(): Promise<boolean> {
    try {
      const metrics = await this.getPerformanceMetrics();
      
      // Verify page loads within acceptable time
      return metrics.domContentLoaded < 3000 && metrics.loadComplete < 5000;
    } catch {
      return false;
    }
  }

  /**
   * Test login with different browsers
   */
  async testCrossBrowserCompatibility(): Promise<boolean> {
    try {
      // Verify all elements are visible and functional
      const pageLoaded = await this.verifyLoginPageLoaded();
      if (!pageLoaded) {
        return false;
      }
      
      // Test form submission
      await this.enterUsername('<EMAIL>');
      await this.enterPassword('password');
      
      // Verify button is clickable
      return await this.isElementEnabled(this.loginButton);
    } catch {
      return false;
    }
  }

  /**
   * Test mobile responsiveness
   */
  async testMobileView(): Promise<boolean> {
    try {
      // Set mobile viewport
      await this.driver.manage().window().setRect({ width: 375, height: 667 });
      
      // Verify elements are still visible and accessible
      const pageLoaded = await this.verifyLoginPageLoaded();
      
      // Reset to desktop view
      await this.driver.manage().window().setRect({ width: 1280, height: 720 });
      
      return pageLoaded;
    } catch {
      return false;
    }
  }

  /**
   * Verify security features
   */
  async verifySecurityFeatures(): Promise<boolean> {
    try {
      // Check password field type
      const passwordType = await this.getElementAttribute(this.passwordInput, 'type');
      if (passwordType !== 'password') {
        return false;
      }
      
      // Verify form uses HTTPS
      const url = await this.getCurrentUrl();
      if (!url.startsWith('https:')) {
        return false;
      }
      
      // Check for CSRF protection (if implemented)
      const csrfToken = By.css('[name="_token"]');
      const hasCsrfToken = await this.isElementVisible(csrfToken, 1000);
      
      if (hasCsrfToken) {
        const tokenValue = await this.getElementAttribute(csrfToken, 'value');
        return !!tokenValue;
      }
      
      return true; // CSRF might not be implemented
    } catch {
      return false;
    }
  }

  /**
   * Wait for login form to be interactive
   */
  async waitForFormInteractive(): Promise<void> {
    await this.waitForElement(this.usernameInput);
    await this.waitForElement(this.passwordInput);
    await this.waitForElementClickable(this.loginButton);
  }

  /**
   * Verify login page title
   */
  async verifyPageTitle(expectedTitle: string): Promise<boolean> {
    return await this.verifyPageTitle(expectedTitle);
  }

  /**
   * Get current page language
   */
  async getCurrentLanguage(): Promise<'ar' | 'en' | 'unknown'> {
    try {
      const bodyDir = await this.getElementAttribute(By.tagName('body'), 'dir');
      
      if (bodyDir === 'rtl') {
        return 'ar';
      } else if (bodyDir === 'ltr') {
        return 'en';
      }
      
      return 'unknown';
    } catch {
      return 'unknown';
    }
  }

  /**
   * Check if login form is visible
   */
  async isLoginFormVisible(): Promise<boolean> {
    return await this.isElementVisible(this.loginForm);
  }

  /**
   * Wait for error message to appear
   */
  async waitForErrorMessage(timeout: number = 10000): Promise<string> {
    await this.waitForElement(this.errorMessage, timeout);
    return await this.getElementText(this.errorMessage);
  }

  /**
   * Check if forgot password link is visible
   */
  async isForgotPasswordVisible(): Promise<boolean> {
    return await this.isElementVisible(this.forgotPasswordLink);
  }
}
