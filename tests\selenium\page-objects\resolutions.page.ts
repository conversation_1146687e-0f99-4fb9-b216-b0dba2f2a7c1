/**
 * Resolutions Page Object for Jadwa Fund Management System (Selenium)
 * 
 * This page object handles all interactions with the resolutions page using Selenium WebDriver,
 * including resolution lifecycle management, voting, and state transitions.
 */

import { WebDriver, By, until } from 'selenium-webdriver';
import { SeleniumBase } from '../utils/selenium-base';

export class ResolutionsPage extends SeleniumBase {
  // Page elements
  private readonly pageTitle = By.css('[data-testid="page-title"]');
  private readonly breadcrumb = By.css('[data-testid="breadcrumb"]');
  private readonly createResolutionButton = By.css('[data-testid="create-resolution-button"]');
  private readonly resolutionCards = By.css('[data-testid="resolution-card"]');
  private readonly noResolutionsMessage = By.css('[data-testid="no-resolutions-message"]');
  private readonly loadingSpinner = By.css('[data-testid="loading-spinner"]');

  // Resolution card elements
  private readonly resolutionCode = By.css('[data-testid="resolution-code"]');
  private readonly resolutionStatus = By.css('[data-testid="resolution-status"]');
  private readonly resolutionType = By.css('[data-testid="resolution-type"]');
  private readonly resolutionDate = By.css('[data-testid="resolution-date"]');
  private readonly resolutionActions = By.css('[data-testid="resolution-actions"]');

  // Action buttons
  private readonly viewDetailsButton = By.css('[data-testid="view-details-button"]');
  private readonly editButton = By.css('[data-testid="edit-button"]');
  private readonly deleteButton = By.css('[data-testid="delete-button"]');
  private readonly cancelButton = By.css('[data-testid="cancel-button"]');
  private readonly sendButton = By.css('[data-testid="send-button"]');
  private readonly completeButton = By.css('[data-testid="complete-button"]');
  private readonly confirmButton = By.css('[data-testid="confirm-button"]');
  private readonly rejectButton = By.css('[data-testid="reject-button"]');
  private readonly voteButton = By.css('[data-testid="vote-button"]');

  // Filter and search
  private readonly statusFilter = By.css('[data-testid="status-filter"]');
  private readonly typeFilter = By.css('[data-testid="type-filter"]');
  private readonly searchInput = By.css('[data-testid="search-input"]');

  // Resolution details modal/page
  private readonly resolutionDetailsModal = By.css('[data-testid="resolution-details-modal"]');
  private readonly resolutionDescription = By.css('[data-testid="resolution-description"]');
  private readonly resolutionItems = By.css('[data-testid="resolution-items"]');
  private readonly votingMethodology = By.css('[data-testid="voting-methodology"]');
  private readonly memberVotingResult = By.css('[data-testid="member-voting-result"]');
  private readonly resolutionFile = By.css('[data-testid="resolution-file"]');

  // Voting elements
  private readonly voteModal = By.css('[data-testid="vote-modal"]');
  private readonly voteYesButton = By.css('[data-testid="vote-yes"]');
  private readonly voteNoButton = By.css('[data-testid="vote-no"]');
  private readonly voteAbstainButton = By.css('[data-testid="vote-abstain"]');
  private readonly submitVoteButton = By.css('[data-testid="submit-vote"]');

  // Confirmation dialogs
  private readonly confirmationDialog = By.css('[data-testid="confirmation-dialog"]');
  private readonly confirmYesButton = By.css('[data-testid="confirm-yes"]');
  private readonly confirmNoButton = By.css('[data-testid="confirm-no"]');

  constructor(driver: WebDriver) {
    super(driver);
  }

  /**
   * Navigate to resolutions page for specific fund
   */
  async navigateToResolutions(fundId: number): Promise<void> {
    await this.goto(`/admin/investment-funds/${fundId}/resolutions`);
    await this.waitForPageLoad();
  }

  /**
   * Verify resolutions page is loaded
   */
  async verifyResolutionsPageLoaded(): Promise<boolean> {
    try {
      await this.waitForElement(this.pageTitle);
      return await this.verifyUrlContains('/resolutions');
    } catch {
      return false;
    }
  }

  /**
   * Click create resolution button
   */
  async clickCreateResolution(): Promise<void> {
    await this.clickElement(this.createResolutionButton);
    await this.waitForPageLoad();
  }

  /**
   * Get resolution card by code
   */
  private getResolutionCardByCodeLocator(resolutionCode: string): By {
    return By.xpath(`//div[@data-testid="resolution-card"][contains(text(), "${resolutionCode}")]`);
  }

  /**
   * Click resolution card to view details
   */
  async clickResolutionCard(resolutionCode: string): Promise<void> {
    const resolutionCard = this.getResolutionCardByCodeLocator(resolutionCode);
    await this.clickElement(resolutionCard);
    await this.waitForPageLoad();
  }

  /**
   * Get resolution status
   */
  async getResolutionStatus(resolutionCode: string): Promise<string> {
    try {
      const statusElement = By.xpath(`//div[@data-testid="resolution-card"][contains(text(), "${resolutionCode}")]//span[@data-testid="resolution-status"]`);
      return await this.getElementText(statusElement);
    } catch {
      return '';
    }
  }

  /**
   * Edit resolution
   */
  async editResolution(resolutionCode: string): Promise<void> {
    const editButton = By.xpath(`//div[@data-testid="resolution-card"][contains(text(), "${resolutionCode}")]//button[@data-testid="edit-button"]`);
    await this.clickElement(editButton);
    await this.waitForPageLoad();
  }

  /**
   * Delete resolution (draft only)
   */
  async deleteResolution(resolutionCode: string): Promise<void> {
    const deleteButton = By.xpath(`//div[@data-testid="resolution-card"][contains(text(), "${resolutionCode}")]//button[@data-testid="delete-button"]`);
    await this.clickElement(deleteButton);
    
    // Confirm deletion
    await this.confirmAction();
  }

  /**
   * Cancel resolution (pending only)
   */
  async cancelResolution(resolutionCode: string): Promise<void> {
    const cancelButton = By.xpath(`//div[@data-testid="resolution-card"][contains(text(), "${resolutionCode}")]//button[@data-testid="cancel-button"]`);
    await this.clickElement(cancelButton);
    
    // Confirm cancellation
    await this.confirmAction();
  }

  /**
   * Send resolution for approval
   */
  async sendResolution(resolutionCode: string): Promise<void> {
    const sendButton = By.xpath(`//div[@data-testid="resolution-card"][contains(text(), "${resolutionCode}")]//button[@data-testid="send-button"]`);
    await this.clickElement(sendButton);
    await this.waitForLoadingToComplete();
  }

  /**
   * Complete resolution data
   */
  async completeResolution(resolutionCode: string): Promise<void> {
    const completeButton = By.xpath(`//div[@data-testid="resolution-card"][contains(text(), "${resolutionCode}")]//button[@data-testid="complete-button"]`);
    await this.clickElement(completeButton);
    await this.waitForPageLoad();
  }

  /**
   * Confirm resolution
   */
  async confirmResolution(resolutionCode: string): Promise<void> {
    const confirmButton = By.xpath(`//div[@data-testid="resolution-card"][contains(text(), "${resolutionCode}")]//button[@data-testid="confirm-button"]`);
    await this.clickElement(confirmButton);
    await this.waitForLoadingToComplete();
  }

  /**
   * Reject resolution
   */
  async rejectResolution(resolutionCode: string): Promise<void> {
    const rejectButton = By.xpath(`//div[@data-testid="resolution-card"][contains(text(), "${resolutionCode}")]//button[@data-testid="reject-button"]`);
    await this.clickElement(rejectButton);
    await this.waitForLoadingToComplete();
  }

  /**
   * Vote on resolution
   */
  async voteOnResolution(resolutionCode: string, vote: 'yes' | 'no' | 'abstain'): Promise<void> {
    const voteButton = By.xpath(`//div[@data-testid="resolution-card"][contains(text(), "${resolutionCode}")]//button[@data-testid="vote-button"]`);
    await this.clickElement(voteButton);
    
    // Wait for vote modal
    await this.waitForElement(this.voteModal);
    
    // Select vote
    switch (vote) {
      case 'yes':
        await this.clickElement(this.voteYesButton);
        break;
      case 'no':
        await this.clickElement(this.voteNoButton);
        break;
      case 'abstain':
        await this.clickElement(this.voteAbstainButton);
        break;
    }
    
    // Submit vote
    await this.clickElement(this.submitVoteButton);
    await this.waitForLoadingToComplete();
  }

  /**
   * Filter resolutions by status
   */
  async filterByStatus(status: string): Promise<void> {
    await this.selectOption(this.statusFilter, status);
    await this.waitForLoadingToComplete();
  }

  /**
   * Filter resolutions by type
   */
  async filterByType(type: string): Promise<void> {
    await this.selectOption(this.typeFilter, type);
    await this.waitForLoadingToComplete();
  }

  /**
   * Search resolutions
   */
  async searchResolutions(searchTerm: string): Promise<void> {
    await this.fillInput(this.searchInput, searchTerm);
    await this.waitForLoadingToComplete();
  }

  /**
   * Verify resolution exists
   */
  async verifyResolutionExists(resolutionCode: string): Promise<boolean> {
    const resolutionCard = this.getResolutionCardByCodeLocator(resolutionCode);
    return await this.isElementVisible(resolutionCard);
  }

  /**
   * Verify resolution status
   */
  async verifyResolutionStatus(resolutionCode: string, expectedStatus: string): Promise<boolean> {
    try {
      const status = await this.getResolutionStatus(resolutionCode);
      return status.includes(expectedStatus);
    } catch {
      return false;
    }
  }

  /**
   * Verify resolution actions based on status and role
   */
  async verifyResolutionActions(resolutionCode: string, expectedActions: string[]): Promise<boolean> {
    try {
      for (const action of expectedActions) {
        const actionButton = By.xpath(`//div[@data-testid="resolution-card"][contains(text(), "${resolutionCode}")]//button[@data-testid="${action}-button"]`);
        const isVisible = await this.isElementVisible(actionButton, 2000);
        if (!isVisible) {
          return false;
        }
      }
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Verify no resolutions message
   */
  async verifyNoResolutionsMessage(): Promise<boolean> {
    return await this.isElementVisible(this.noResolutionsMessage);
  }

  /**
   * Confirm action in dialog
   */
  async confirmAction(): Promise<void> {
    await this.waitForElement(this.confirmationDialog);
    await this.clickElement(this.confirmYesButton);
    await this.waitForLoadingToComplete();
  }

  /**
   * Cancel action in dialog
   */
  async cancelAction(): Promise<void> {
    await this.waitForElement(this.confirmationDialog);
    await this.clickElement(this.confirmNoButton);
  }

  /**
   * Verify system message
   */
  async verifySystemMessage(messageCode: string): Promise<boolean> {
    const messageElement = By.css(`[data-testid="message-${messageCode}"]`);
    return await this.isElementVisible(messageElement);
  }

  /**
   * Wait for resolution state change
   */
  async waitForResolutionStateChange(resolutionCode: string, expectedStatus: string): Promise<void> {
    await this.driver.wait(async () => {
      try {
        const status = await this.getResolutionStatus(resolutionCode);
        return status.includes(expectedStatus);
      } catch {
        return false;
      }
    }, 30000);
  }

  /**
   * Verify voting suspension workflow (Alternative 1)
   */
  async verifyVotingSuspensionWorkflow(resolutionCode: string): Promise<boolean> {
    try {
      // Verify resolution is in voting progress
      const isVotingInProgress = await this.verifyResolutionStatus(resolutionCode, 'Voting in Progress');
      if (!isVotingInProgress) {
        return false;
      }
      
      // Edit resolution to trigger suspension
      await this.editResolution(resolutionCode);
      
      // Verify MSG006 confirmation message
      const msg006Visible = await this.verifySystemMessage('MSG006');
      if (!msg006Visible) {
        return false;
      }
      
      // Confirm suspension
      await this.confirmAction();
      
      // Verify MSG007 notification
      const msg007Visible = await this.verifySystemMessage('MSG007');
      if (!msg007Visible) {
        return false;
      }
      
      // Verify status changed to waiting for confirmation
      await this.waitForResolutionStateChange(resolutionCode, 'Waiting for Confirmation');
      
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Verify new resolution from approved workflow (Alternative 2)
   */
  async verifyNewResolutionFromApprovedWorkflow(resolutionCode: string): Promise<boolean> {
    try {
      // Verify resolution is approved/not approved
      const status = await this.getResolutionStatus(resolutionCode);
      if (!['Approved', 'Not Approved'].includes(status)) {
        return false;
      }
      
      // Edit resolution to create new one
      await this.editResolution(resolutionCode);
      
      // Verify MSG008 confirmation message
      const msg008Visible = await this.verifySystemMessage('MSG008');
      if (!msg008Visible) {
        return false;
      }
      
      // Confirm creation of new resolution
      await this.confirmAction();
      
      // Verify MSG009 notification
      const msg009Visible = await this.verifySystemMessage('MSG009');
      
      return msg009Visible;
    } catch {
      return false;
    }
  }

  /**
   * Get resolution cards count
   */
  async getResolutionCardsCount(): Promise<number> {
    try {
      const resolutionCardElements = await this.findElements(this.resolutionCards);
      return resolutionCardElements.length;
    } catch {
      return 0;
    }
  }

  /**
   * Wait for resolutions to load
   */
  async waitForResolutionsToLoad(): Promise<void> {
    await this.waitForLoadingToComplete();
    
    // Wait for either resolutions to appear or no resolutions message
    await this.driver.wait(async () => {
      const resolutionsVisible = await this.isElementVisible(this.resolutionCards, 1000);
      const noResolutionsVisible = await this.isElementVisible(this.noResolutionsMessage, 1000);
      return resolutionsVisible || noResolutionsVisible;
    }, 10000);
  }

  /**
   * Get page title
   */
  async getPageTitle(): Promise<string> {
    return await this.getElementText(this.pageTitle);
  }

  /**
   * Check if vote modal is open
   */
  async isVoteModalOpen(): Promise<boolean> {
    return await this.isElementVisible(this.voteModal, 2000);
  }

  /**
   * Check if confirmation dialog is open
   */
  async isConfirmationDialogOpen(): Promise<boolean> {
    return await this.isElementVisible(this.confirmationDialog, 2000);
  }

  /**
   * Get resolution type
   */
  async getResolutionType(resolutionCode: string): Promise<string> {
    try {
      const typeElement = By.xpath(`//div[@data-testid="resolution-card"][contains(text(), "${resolutionCode}")]//span[@data-testid="resolution-type"]`);
      return await this.getElementText(typeElement);
    } catch {
      return '';
    }
  }

  /**
   * Get resolution date
   */
  async getResolutionDate(resolutionCode: string): Promise<string> {
    try {
      const dateElement = By.xpath(`//div[@data-testid="resolution-card"][contains(text(), "${resolutionCode}")]//span[@data-testid="resolution-date"]`);
      return await this.getElementText(dateElement);
    } catch {
      return '';
    }
  }

  /**
   * Wait for specific resolution to appear
   */
  async waitForResolution(resolutionCode: string, timeout: number = 10000): Promise<void> {
    const resolutionCard = this.getResolutionCardByCodeLocator(resolutionCode);
    await this.waitForElement(resolutionCard, timeout);
  }

  /**
   * Check if create resolution button is visible
   */
  async isCreateResolutionButtonVisible(): Promise<boolean> {
    return await this.isElementVisible(this.createResolutionButton, 2000);
  }
}
