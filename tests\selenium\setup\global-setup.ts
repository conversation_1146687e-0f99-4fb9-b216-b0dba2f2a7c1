/**
 * Global Setup for Selenium Tests
 * 
 * This module handles global setup and teardown for Selenium test execution,
 * including driver initialization, test data setup, and environment preparation.
 */

import { DriverManager } from '../utils/driver-manager';
import { seleniumTestDataManager } from '../utils/test-data-manager';
import { getCurrentEnvironment } from '../../e2e/config/environments';

/**
 * Global setup function executed before all tests
 */
export async function globalSetup(): Promise<void> {
  console.log('🚀 Starting Selenium Test Suite Global Setup...');
  
  try {
    // Setup environment
    await setupEnvironment();
    
    // Setup test data
    await setupTestData();
    
    // Verify browser availability
    await verifyBrowserAvailability();
    
    // Setup performance monitoring
    await setupPerformanceMonitoring();
    
    console.log('✅ Global setup completed successfully');
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  }
}

/**
 * Global teardown function executed after all tests
 */
export async function globalTeardown(): Promise<void> {
  console.log('🧹 Starting Selenium Test Suite Global Teardown...');
  
  try {
    // Cleanup all drivers
    await DriverManager.cleanupAllDrivers();
    
    // Cleanup test data
    await cleanupTestData();
    
    // Generate final reports
    await generateFinalReports();
    
    console.log('✅ Global teardown completed successfully');
  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw in teardown to avoid masking test failures
  }
}

/**
 * Setup test environment
 */
async function setupEnvironment(): Promise<void> {
  const environment = getCurrentEnvironment();
  
  console.log(`📋 Setting up environment: ${environment.name}`);
  console.log(`🌐 Base URL: ${environment.baseUrl}`);
  
  // Verify environment is accessible
  try {
    const response = await fetch(environment.baseUrl);
    if (!response.ok) {
      throw new Error(`Environment not accessible: ${response.status}`);
    }
    console.log('✅ Environment is accessible');
  } catch (error) {
    console.warn('⚠️ Environment accessibility check failed:', error);
    // Continue with tests as the environment might still work
  }
  
  // Set environment variables for tests
  process.env.SELENIUM_BASE_URL = environment.baseUrl;
  process.env.SELENIUM_ENVIRONMENT = environment.name;
}

/**
 * Setup test data
 */
async function setupTestData(): Promise<void> {
  console.log('📊 Setting up test data...');
  
  try {
    await seleniumTestDataManager.setupTestDatabase();
    console.log('✅ Test data setup completed');
  } catch (error) {
    console.warn('⚠️ Test data setup failed:', error);
    // Continue with tests using existing data
  }
}

/**
 * Verify browser availability
 */
async function verifyBrowserAvailability(): Promise<void> {
  console.log('🌐 Verifying browser availability...');
  
  const availableBrowsers = DriverManager.getAvailableBrowsers();
  
  if (availableBrowsers.length === 0) {
    throw new Error('No browsers available for testing');
  }
  
  console.log(`✅ Available browsers: ${availableBrowsers.join(', ')}`);
  
  // Test driver creation for primary browser
  try {
    const testDriver = await DriverManager.getDriver('chrome-ar');
    await testDriver.getTitle(); // Simple test to verify driver works
    console.log('✅ Primary browser driver test successful');
  } catch (error) {
    console.error('❌ Primary browser driver test failed:', error);
    throw error;
  }
}

/**
 * Setup performance monitoring
 */
async function setupPerformanceMonitoring(): Promise<void> {
  console.log('📈 Setting up performance monitoring...');
  
  // Initialize performance tracking
  global.performanceMetrics = {
    testStartTime: Date.now(),
    testResults: [],
    browserMetrics: new Map()
  };
  
  console.log('✅ Performance monitoring setup completed');
}

/**
 * Cleanup test data
 */
async function cleanupTestData(): Promise<void> {
  console.log('🧹 Cleaning up test data...');
  
  try {
    await seleniumTestDataManager.cleanupTestDatabase();
    console.log('✅ Test data cleanup completed');
  } catch (error) {
    console.warn('⚠️ Test data cleanup failed:', error);
  }
}

/**
 * Generate final reports
 */
async function generateFinalReports(): Promise<void> {
  console.log('📊 Generating final reports...');
  
  try {
    // Generate performance summary
    if (global.performanceMetrics) {
      const totalTime = Date.now() - global.performanceMetrics.testStartTime;
      const testCount = global.performanceMetrics.testResults.length;
      
      console.log(`📈 Test Execution Summary:`);
      console.log(`   Total execution time: ${totalTime}ms`);
      console.log(`   Total tests executed: ${testCount}`);
      console.log(`   Average test time: ${testCount > 0 ? Math.round(totalTime / testCount) : 0}ms`);
      
      // Browser-specific metrics
      for (const [browser, metrics] of global.performanceMetrics.browserMetrics) {
        console.log(`   ${browser}: ${metrics.testCount} tests, avg ${metrics.avgTime}ms`);
      }
    }
    
    console.log('✅ Final reports generated');
  } catch (error) {
    console.warn('⚠️ Report generation failed:', error);
  }
}

/**
 * Handle uncaught exceptions during setup
 */
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught exception during setup:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled rejection during setup:', reason);
  process.exit(1);
});

// Export for Mocha configuration
export { globalSetup as mochaGlobalSetup };
export { globalTeardown as mochaGlobalTeardown };

// Extend global types for TypeScript
declare global {
  var performanceMetrics: {
    testStartTime: number;
    testResults: Array<{
      name: string;
      duration: number;
      browser: string;
      status: 'passed' | 'failed' | 'skipped';
    }>;
    browserMetrics: Map<string, {
      testCount: number;
      totalTime: number;
      avgTime: number;
    }>;
  };
}

/**
 * Test-level setup hook
 */
export function setupTestHooks(): void {
  // Before each test
  beforeEach(function() {
    const testStartTime = Date.now();
    this.currentTest.startTime = testStartTime;
    
    console.log(`🧪 Starting test: ${this.currentTest.title}`);
  });
  
  // After each test
  afterEach(function() {
    const testEndTime = Date.now();
    const duration = testEndTime - this.currentTest.startTime;
    
    // Record test metrics
    if (global.performanceMetrics) {
      global.performanceMetrics.testResults.push({
        name: this.currentTest.title,
        duration,
        browser: process.env.CURRENT_BROWSER || 'unknown',
        status: this.currentTest.state || 'unknown'
      });
    }
    
    console.log(`✅ Test completed: ${this.currentTest.title} (${duration}ms)`);
  });
}

/**
 * Browser-specific setup
 */
export async function setupBrowserTest(browserName: string): Promise<void> {
  process.env.CURRENT_BROWSER = browserName;
  
  // Initialize browser metrics if not exists
  if (global.performanceMetrics && !global.performanceMetrics.browserMetrics.has(browserName)) {
    global.performanceMetrics.browserMetrics.set(browserName, {
      testCount: 0,
      totalTime: 0,
      avgTime: 0
    });
  }
}

/**
 * Browser-specific cleanup
 */
export async function cleanupBrowserTest(browserName: string, testDuration: number): Promise<void> {
  // Update browser metrics
  if (global.performanceMetrics) {
    const metrics = global.performanceMetrics.browserMetrics.get(browserName);
    if (metrics) {
      metrics.testCount++;
      metrics.totalTime += testDuration;
      metrics.avgTime = Math.round(metrics.totalTime / metrics.testCount);
    }
  }
}

// Auto-setup hooks when this module is imported
setupTestHooks();
