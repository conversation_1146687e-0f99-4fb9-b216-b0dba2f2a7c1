/**
 * Authentication Tests for Jadwa Fund Management System (Selenium)
 * 
 * This test suite covers authentication flow and role-based access control
 * using Selenium WebDriver, providing an alternative to Playwright tests.
 */

import { describe, it, before, after, beforeEach, afterEach } from 'mocha';
import { expect } from 'chai';
import { WebDriver } from 'selenium-webdriver';
import { DriverManager } from '../utils/driver-manager';
import { LoginPage } from '../page-objects/login.page';
import { DashboardPage } from '../page-objects/dashboard.page';
import { FundsPage } from '../page-objects/funds.page';
import { getCurrentEnvironment, getCredentials } from '../../e2e/config/environments';

describe('Authentication and Role-Based Access Control (Selenium)', function() {
  this.timeout(60000); // 1 minute timeout for Selenium tests
  
  let driver: WebDriver;
  let loginPage: LoginPage;
  let dashboardPage: DashboardPage;
  let fundsPage: FundsPage;
  
  const environment = getCurrentEnvironment();

  before(async function() {
    // Setup will be done in beforeEach for each test
  });

  after(async function() {
    await DriverManager.cleanupAllDrivers();
  });

  beforeEach(async function() {
    driver = await DriverManager.getDriver('chrome-ar');
    loginPage = new LoginPage(driver);
    dashboardPage = new DashboardPage(driver);
    fundsPage = new FundsPage(driver);
  });

  afterEach(async function() {
    // Clear session but keep driver for reuse
    try {
      await driver.manage().deleteAllCookies();
      await driver.executeScript('localStorage.clear(); sessionStorage.clear();');
    } catch (error) {
      console.warn('Error during cleanup:', error);
    }
  });

  describe('Authentication Flow', function() {
    it('should successfully login with valid credentials', async function() {
      const credentials = getCredentials('fundManager');
      
      await loginPage.navigateToLogin();
      expect(await loginPage.verifyLoginPageLoaded()).to.be.true;
      
      await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);
      
      // Verify successful login
      expect(await dashboardPage.verifyDashboardLoaded()).to.be.true;
      expect(await dashboardPage.verifyWelcomeMessage()).to.be.true;
      
      // Verify JWT token is stored
      const token = await driver.executeScript('return localStorage.getItem("auth_token");');
      expect(token).to.not.be.null;
      
      // Verify token structure (JWT format)
      expect(token).to.match(/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/);
    });

    it('should fail login with invalid credentials', async function() {
      await loginPage.navigateToLogin();
      
      await loginPage.login('<EMAIL>', 'wrongpassword');
      
      // Verify error message is displayed
      expect(await loginPage.verifyInvalidCredentialsError()).to.be.true;
      
      // Verify user remains on login page
      expect(await loginPage.getCurrentUrl()).to.include('/auth/login');
      
      // Verify no token is stored
      const token = await driver.executeScript('return localStorage.getItem("auth_token");');
      expect(token).to.be.null;
    });

    it('should validate required fields', async function() {
      await loginPage.navigateToLogin();
      
      // Test empty username
      expect(await loginPage.verifyUsernameValidation()).to.be.true;
      
      // Test empty password
      expect(await loginPage.verifyPasswordValidation()).to.be.true;
    });

    it('should handle logout functionality', async function() {
      const credentials = getCredentials('fundManager');
      
      // Login first
      await loginPage.navigateToLogin();
      await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);
      expect(await dashboardPage.verifyDashboardLoaded()).to.be.true;
      
      // Logout
      await dashboardPage.logout();
      
      // Verify redirect to login page
      expect(await loginPage.getCurrentUrl()).to.include('/auth/login');
      
      // Verify token is cleared
      const token = await driver.executeScript('return localStorage.getItem("auth_token");');
      expect(token).to.be.null;
    });

    it('should remember me functionality', async function() {
      const credentials = getCredentials('fundManager');
      
      await loginPage.navigateToLogin();
      
      // Enable remember me
      await loginPage.toggleRememberMe();
      expect(await loginPage.isRememberMeChecked()).to.be.true;
      
      await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);
      
      // Verify successful login
      expect(await dashboardPage.verifyDashboardLoaded()).to.be.true;
    });
  });

  describe('Fund Manager Role Access Control', function() {
    beforeEach(async function() {
      const credentials = getCredentials('fundManager');
      await loginPage.navigateToLogin();
      await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);
    });

    it('should have dashboard and navigation access', async function() {
      expect(await dashboardPage.verifyDashboardLoaded()).to.be.true;
      
      // Verify fund manager can access dashboard
      expect(await dashboardPage.verifyDashboardStatistics()).to.be.true;
      
      // Verify navigation menu items
      expect(await dashboardPage.verifySideMenuForRole('fundmanager')).to.be.true;
      
      // Verify fund manager can create funds
      expect(await dashboardPage.verifyCreateFundAccess()).to.be.true;
    });

    it('should have fund management permissions', async function() {
      await fundsPage.navigateToFunds();
      
      // Verify fund manager can view funds
      expect(await fundsPage.verifyFundsDisplayed()).to.be.true;
      
      // Verify fund manager can create funds
      expect(await fundsPage.verifyCreateFundButtonVisibility(true)).to.be.true;
      
      // Verify permissions in token
      const permissions = await driver.executeScript(`
        const token = localStorage.getItem('auth_token');
        if (!token) return [];
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          return payload.Permission || [];
        } catch {
          return [];
        }
      `);
      
      expect(permissions).to.include('Fund.Create');
      expect(permissions).to.include('Fund.Edit');
    });
  });

  describe('Legal Council Role Access Control', function() {
    beforeEach(async function() {
      const credentials = getCredentials('legalCouncil');
      await loginPage.navigateToLogin();
      await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);
    });

    it('should have full fund and resolution access', async function() {
      // Verify legal council can manage funds
      await fundsPage.navigateToFunds();
      expect(await fundsPage.verifyCreateFundButtonVisibility(true)).to.be.true;
      
      // Verify permissions
      const permissions = await driver.executeScript(`
        const token = localStorage.getItem('auth_token');
        if (!token) return [];
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          return payload.Permission || [];
        } catch {
          return [];
        }
      `);
      
      expect(permissions).to.include('Fund.Complete');
      expect(permissions).to.include('Resolution.Complete');
    });

    it('should have board member management access', async function() {
      // Navigate to a fund's member management (would need test data)
      // This test would verify CRUD permissions for board members
      
      const permissions = await driver.executeScript(`
        const token = localStorage.getItem('auth_token');
        if (!token) return [];
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          return payload.Permission || [];
        } catch {
          return [];
        }
      `);
      
      expect(permissions).to.include('BoardMember.Create');
      expect(permissions).to.include('BoardMember.Edit');
    });
  });

  describe('Board Secretary Role Access Control', function() {
    beforeEach(async function() {
      const credentials = getCredentials('boardSecretary');
      await loginPage.navigateToLogin();
      await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);
    });

    it('should have resolution and member management access', async function() {
      // Verify board secretary can manage members
      const permissions = await driver.executeScript(`
        const token = localStorage.getItem('auth_token');
        if (!token) return [];
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          return payload.Permission || [];
        } catch {
          return [];
        }
      `);
      
      expect(permissions).to.include('Resolution.Complete');
      expect(permissions).to.include('BoardMember.Create');
      expect(permissions).to.include('BoardMember.Edit');
    });

    it('should have fund view-only access', async function() {
      await fundsPage.navigateToFunds();
      
      // Verify board secretary can view funds
      expect(await fundsPage.verifyFundsDisplayed()).to.be.true;
      
      // Verify board secretary cannot create funds
      expect(await fundsPage.verifyCreateFundButtonVisibility(false)).to.be.true;
      
      // Verify fund permissions
      const permissions = await driver.executeScript(`
        const token = localStorage.getItem('auth_token');
        if (!token) return [];
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          return payload.Permission || [];
        } catch {
          return [];
        }
      `);
      
      expect(permissions).to.not.include('Fund.Create');
      expect(permissions).to.include('Fund.View');
    });
  });

  describe('Board Member Role Access Control', function() {
    beforeEach(async function() {
      const credentials = getCredentials('boardMember');
      await loginPage.navigateToLogin();
      await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);
    });

    it('should have limited dashboard access', async function() {
      expect(await dashboardPage.verifyDashboardLoaded()).to.be.true;
      
      // Verify board member cannot create funds
      expect(await dashboardPage.verifyNoCreateFundAccess()).to.be.true;
      
      // Verify limited navigation menu
      expect(await dashboardPage.verifySideMenuForRole('boardmember')).to.be.true;
    });

    it('should have view-only fund and member access', async function() {
      // Verify board member can view funds
      await fundsPage.navigateToFunds();
      expect(await fundsPage.verifyFundsDisplayed()).to.be.true;
      expect(await fundsPage.verifyCreateFundButtonVisibility(false)).to.be.true;
      
      // Verify voting permission
      const permissions = await driver.executeScript(`
        const token = localStorage.getItem('auth_token');
        if (!token) return [];
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          return payload.Permission || [];
        } catch {
          return [];
        }
      `);
      
      expect(permissions).to.include('Resolution.Vote');
      expect(permissions).to.not.include('Resolution.Edit');
    });
  });

  describe('Security Features', function() {
    it('should validate JWT token structure', async function() {
      const credentials = getCredentials('fundManager');
      
      await loginPage.navigateToLogin();
      await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);
      
      // Get initial token
      const initialToken = await driver.executeScript('return localStorage.getItem("auth_token");');
      expect(initialToken).to.not.be.null;
      
      // Verify token structure
      expect(initialToken).to.match(/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/);
      
      // Verify token payload can be decoded
      const payload = await driver.executeScript(`
        const token = localStorage.getItem('auth_token');
        try {
          return JSON.parse(atob(token.split('.')[1]));
        } catch {
          return null;
        }
      `);
      
      expect(payload).to.not.be.null;
      expect(payload).to.have.property('Id');
      expect(payload).to.have.property('Permission');
    });

    it('should verify security features on login page', async function() {
      await loginPage.navigateToLogin();
      
      expect(await loginPage.verifySecurityFeatures()).to.be.true;
    });

    it('should handle session timeout', async function() {
      const credentials = getCredentials('fundManager');
      
      await loginPage.navigateToLogin();
      await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);
      
      // Simulate expired token
      await driver.executeScript(`
        const expiredToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.invalid';
        localStorage.setItem('auth_token', expiredToken);
      `);
      
      // Try to access protected page
      await driver.get(`${environment.baseUrl}/admin/dashboard`);
      
      // Verify redirect to login page (this depends on implementation)
      const currentUrl = await driver.getCurrentUrl();
      expect(currentUrl).to.include('/auth/login');
    });
  });

  describe('Cross-Browser Authentication', function() {
    const browsers = ['chrome-ar', 'firefox-ar', 'edge-ar'];
    
    browsers.forEach(browserName => {
      it(`should authenticate successfully in ${browserName}`, async function() {
        // Skip if browser is not available
        if (!DriverManager.getAvailableBrowsers().includes(browserName)) {
          this.skip();
        }
        
        const browserDriver = await DriverManager.getDriver(browserName);
        const browserLoginPage = new LoginPage(browserDriver);
        const browserDashboardPage = new DashboardPage(browserDriver);
        
        try {
          const credentials = getCredentials('fundManager');
          
          await browserLoginPage.navigateToLogin();
          expect(await browserLoginPage.verifyLoginPageLoaded()).to.be.true;
          
          await browserLoginPage.loginAndWaitForDashboard(credentials.username, credentials.password);
          expect(await browserDashboardPage.verifyDashboardLoaded()).to.be.true;
          
        } finally {
          await browserDriver.manage().deleteAllCookies();
          await browserDriver.executeScript('localStorage.clear(); sessionStorage.clear();');
        }
      });
    });
  });
});
