/**
 * Localization Tests for Jadwa Fund Management System (Selenium)
 * 
 * This test suite covers Arabic/English localization validation using Selenium WebDriver,
 * including RTL/LTR layout testing, font rendering, and cross-browser compatibility.
 */

import { describe, it, before, after, beforeEach, afterEach } from 'mocha';
import { expect } from 'chai';
import { WebDriver } from 'selenium-webdriver';
import { DriverManager } from '../utils/driver-manager';
import { LoginPage } from '../page-objects/login.page';
import { DashboardPage } from '../page-objects/dashboard.page';
import { FundsPage } from '../page-objects/funds.page';
import { ResolutionsPage } from '../page-objects/resolutions.page';
import { getCurrentEnvironment, getCredentials } from '../../e2e/config/environments';

describe('Localization and Cross-Browser Testing (Selenium)', function() {
  this.timeout(90000); // 1.5 minutes timeout for localization tests
  
  let driver: WebDriver;
  let loginPage: LoginPage;
  let dashboardPage: DashboardPage;
  let fundsPage: FundsPage;
  let resolutionsPage: ResolutionsPage;
  
  const environment = getCurrentEnvironment();

  before(async function() {
    // Setup will be done in beforeEach for each test
  });

  after(async function() {
    await DriverManager.cleanupAllDrivers();
  });

  beforeEach(async function() {
    driver = await DriverManager.getDriver('chrome-ar');
    loginPage = new LoginPage(driver);
    dashboardPage = new DashboardPage(driver);
    fundsPage = new FundsPage(driver);
    resolutionsPage = new ResolutionsPage(driver);
  });

  afterEach(async function() {
    // Clear session but keep driver for reuse
    try {
      await driver.manage().deleteAllCookies();
      await driver.executeScript('localStorage.clear(); sessionStorage.clear();');
    } catch (error) {
      console.warn('Error during cleanup:', error);
    }
  });

  describe('Arabic Language and RTL Layout', function() {
    it('should verify Arabic login page layout and functionality', async function() {
      // Navigate to login page
      await loginPage.navigateToLogin();

      // Switch to Arabic if not already
      await loginPage.switchLanguage();

      // Verify Arabic layout
      expect(await loginPage.verifyArabicLanguage()).to.be.true;

      // Verify RTL direction
      const bodyDir = await driver.executeScript('return document.body.dir;');
      expect(bodyDir).to.equal('rtl');

      // Test Arabic input
      await loginPage.enterUsername('مستخدم@test.com');
      await loginPage.enterPassword('كلمة_مرور123');

      // Verify Arabic text is properly displayed in input fields
      const usernameValue = await loginPage.getUsernameValue();
      expect(usernameValue).to.equal('مستخدم@test.com');
    });

    it('should verify Arabic dashboard layout and navigation', async function() {
      const credentials = getCredentials('fundManager');
      
      await loginPage.navigateToLogin();
      await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);

      // Switch to Arabic
      await dashboardPage.switchLanguage();
      expect(await dashboardPage.verifyArabicLayout()).to.be.true;

      // Verify Arabic dashboard content
      const pageTitle = await dashboardPage.getPageTitle();
      expect(pageTitle).to.include('لوحة التحكم');

      // Verify Arabic welcome message
      const welcomeText = await dashboardPage.getWelcomeMessageText();
      expect(welcomeText).to.match(/مرحباً/);
    });

    it('should verify Arabic fund management interface', async function() {
      const credentials = getCredentials('fundManager');
      
      await loginPage.navigateToLogin();
      await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);

      // Navigate to funds page in Arabic
      await fundsPage.navigateToFunds();
      await fundsPage.switchLanguage();
      expect(await fundsPage.verifyArabicLayout()).to.be.true;

      // Verify Arabic page title
      const pageTitle = await fundsPage.getPageTitle();
      expect(pageTitle).to.include('الصناديق');

      // Test Arabic search functionality
      await fundsPage.searchFunds('صندوق');
      const searchValue = await fundsPage.getSearchValue();
      expect(searchValue).to.equal('صندوق');
    });

    it('should verify Arabic font rendering and Unicode support', async function() {
      const credentials = getCredentials('fundManager');
      
      await loginPage.navigateToLogin();
      await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);

      await dashboardPage.switchLanguage();

      // Test various Arabic characters and diacritics
      const arabicTexts = [
        'الصناديق الاستثمارية', // Basic Arabic
        'مجلس الإدارة', // Arabic with hamza
        'القرارات المعتمدة', // Arabic with various forms
        'التصويت الإلكتروني', // Arabic with alif maqsura
        'الأعضاء المستقلون' // Arabic with tanween
      ];

      for (const text of arabicTexts) {
        // Create a test element with the Arabic text
        await driver.executeScript(`
          const testDiv = document.createElement('div');
          testDiv.textContent = '${text}';
          testDiv.setAttribute('data-testid', 'arabic-test');
          testDiv.style.fontSize = '16px';
          testDiv.style.fontFamily = 'Arial, sans-serif';
          document.body.appendChild(testDiv);
        `);

        // Verify text is rendered correctly
        const renderedText = await driver.executeScript(`
          const testDiv = document.querySelector('[data-testid="arabic-test"]');
          return testDiv ? testDiv.textContent : '';
        `);
        expect(renderedText).to.equal(text);

        // Clean up
        await driver.executeScript(`
          const testDiv = document.querySelector('[data-testid="arabic-test"]');
          if (testDiv) testDiv.remove();
        `);
      }
    });
  });

  describe('English Language and LTR Layout', function() {
    it('should verify English login page layout', async function() {
      await loginPage.navigateToLogin();
      expect(await loginPage.verifyEnglishLanguage()).to.be.true;

      // Verify LTR direction
      const bodyDir = await driver.executeScript('return document.body.dir;');
      expect(bodyDir).to.equal('ltr');

      // Test English input
      await loginPage.enterUsername('<EMAIL>');
      await loginPage.enterPassword('password123');

      const usernameValue = await loginPage.getUsernameValue();
      expect(usernameValue).to.equal('<EMAIL>');
    });

    it('should verify English dashboard and navigation', async function() {
      const credentials = getCredentials('fundManager');
      
      await loginPage.navigateToLogin();
      await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);

      // Ensure English language
      expect(await dashboardPage.verifyEnglishLayout()).to.be.true;

      // Verify English dashboard content
      const pageTitle = await dashboardPage.getPageTitle();
      expect(pageTitle).to.include('Dashboard');
    });

    it('should verify English fund and resolution management', async function() {
      const credentials = getCredentials('legalCouncil');
      
      await loginPage.navigateToLogin();
      await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);

      // Test English fund interface
      await fundsPage.navigateToFunds();
      expect(await fundsPage.verifyEnglishLayout()).to.be.true;

      const pageTitle = await fundsPage.getPageTitle();
      expect(pageTitle).to.include('Funds');

      // Test English resolution interface
      const testFundId = 1; // Mock fund ID
      await resolutionsPage.navigateToResolutions(testFundId);
      
      const resolutionPageTitle = await resolutionsPage.getPageTitle();
      expect(resolutionPageTitle).to.include('Resolutions');
    });
  });

  describe('Language Switching and Persistence', function() {
    it('should verify language switch functionality', async function() {
      const credentials = getCredentials('fundManager');
      
      await loginPage.navigateToLogin();
      await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);

      // Start with Arabic
      await dashboardPage.switchLanguage();
      expect(await dashboardPage.verifyArabicLayout()).to.be.true;

      // Switch to English
      await dashboardPage.switchLanguage();
      expect(await dashboardPage.verifyEnglishLayout()).to.be.true;

      // Switch back to Arabic
      await dashboardPage.switchLanguage();
      expect(await dashboardPage.verifyArabicLayout()).to.be.true;
    });

    it('should verify language preference persistence', async function() {
      const credentials = getCredentials('fundManager');
      
      // Set language to Arabic
      await loginPage.navigateToLogin();
      await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);
      await dashboardPage.switchLanguage();
      expect(await dashboardPage.verifyArabicLayout()).to.be.true;

      // Navigate to different page
      await fundsPage.navigateToFunds();

      // Verify Arabic is maintained
      expect(await fundsPage.verifyArabicLayout()).to.be.true;

      // Refresh page
      await driver.navigate().refresh();
      await driver.sleep(2000); // Wait for page to load

      // Verify Arabic is still maintained
      expect(await fundsPage.verifyArabicLayout()).to.be.true;
    });
  });

  describe('System Message Localization', function() {
    it('should verify MSG001 (Required Field) in both languages', async function() {
      // Test Arabic MSG001
      await loginPage.navigateToLogin();
      await loginPage.switchLanguage(); // Switch to Arabic
      
      // Trigger validation error
      await loginPage.clearForm();
      await loginPage.clickLogin();

      // Check for Arabic error message
      const hasArabicError = await loginPage.verifyLoginError();
      expect(hasArabicError).to.be.true;

      // Switch to English and test
      await loginPage.switchLanguage();
      await loginPage.clickLogin();

      // Check for English error message
      const hasEnglishError = await loginPage.verifyLoginError();
      expect(hasEnglishError).to.be.true;
    });

    it('should verify MSG002 (Record Saved Successfully) localization', async function() {
      const credentials = getCredentials('legalCouncil');
      
      await loginPage.navigateToLogin();
      await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);

      // This test would require actual form submission to trigger MSG002
      // For now, we'll verify the message system is working
      
      // Navigate to a form page (mock)
      await fundsPage.navigateToFunds();
      
      // In real implementation, we would:
      // 1. Fill and submit a form
      // 2. Verify MSG002 appears in current language
      // 3. Switch language and repeat
      
      expect(await fundsPage.verifyFundsPageLoaded()).to.be.true;
    });
  });

  describe('Cross-Browser Localization', function() {
    const browsers = ['chrome-ar', 'firefox-ar', 'edge-ar'];
    
    browsers.forEach(browserName => {
      it(`should verify Arabic text rendering in ${browserName}`, async function() {
        // Skip if browser is not available
        if (!DriverManager.getAvailableBrowsers().includes(browserName)) {
          this.skip();
        }
        
        const browserDriver = await DriverManager.getDriver(browserName);
        const browserLoginPage = new LoginPage(browserDriver);
        const browserDashboardPage = new DashboardPage(browserDriver);
        
        try {
          const credentials = getCredentials('fundManager');
          
          await browserLoginPage.navigateToLogin();
          await browserLoginPage.loginAndWaitForDashboard(credentials.username, credentials.password);

          await browserDashboardPage.switchLanguage();

          // Test Arabic text rendering
          expect(await browserDashboardPage.verifyArabicLayout()).to.be.true;

          // Verify RTL layout works correctly
          const bodyDir = await browserDriver.executeScript('return document.body.dir;');
          expect(bodyDir).to.equal('rtl');

          // Browser-specific checks
          if (browserName.includes('webkit') || browserName.includes('safari')) {
            // Safari-specific Arabic font rendering checks
            const computedStyle = await browserDriver.executeScript(`
              const element = document.querySelector('[data-testid="page-title"]');
              return element ? window.getComputedStyle(element).fontFamily : '';
            `);
            expect(computedStyle).to.not.be.empty;
          }
          
        } finally {
          await browserDriver.manage().deleteAllCookies();
          await browserDriver.executeScript('localStorage.clear(); sessionStorage.clear();');
        }
      });
    });

    it('should verify form input handling across browsers', async function() {
      const browsers = ['chrome-ar', 'firefox-ar'];
      
      for (const browserName of browsers) {
        if (!DriverManager.getAvailableBrowsers().includes(browserName)) {
          continue;
        }
        
        const browserDriver = await DriverManager.getDriver(browserName);
        const browserLoginPage = new LoginPage(browserDriver);
        
        try {
          await browserLoginPage.navigateToLogin();
          await browserLoginPage.switchLanguage(); // Switch to Arabic

          // Test Arabic input in different browsers
          const arabicName = 'أحمد محمد الأحمد';
          await browserLoginPage.enterUsername(arabicName);

          const inputValue = await browserLoginPage.getUsernameValue();
          expect(inputValue).to.equal(arabicName);
          
        } finally {
          await browserDriver.manage().deleteAllCookies();
          await browserDriver.executeScript('localStorage.clear(); sessionStorage.clear();');
        }
      }
    });
  });

  describe('Responsive Design and Mobile Testing', function() {
    it('should verify Arabic layout on mobile devices', async function() {
      const credentials = getCredentials('fundManager');
      
      await loginPage.navigateToLogin();
      await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);

      // Switch to Arabic
      await dashboardPage.switchLanguage();

      // Test mobile view
      await driver.manage().window().setRect({ width: 375, height: 667 });
      expect(await dashboardPage.verifyArabicLayout()).to.be.true;

      // Test tablet view
      await driver.manage().window().setRect({ width: 768, height: 1024 });
      expect(await dashboardPage.verifyArabicLayout()).to.be.true;

      // Reset to desktop view
      await driver.manage().window().setRect({ width: 1280, height: 720 });
      expect(await dashboardPage.verifyArabicLayout()).to.be.true;
    });

    it('should verify touch interactions in mobile view', async function() {
      const credentials = getCredentials('fundManager');
      
      await loginPage.navigateToLogin();
      await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);

      // Set mobile viewport
      await driver.manage().window().setRect({ width: 375, height: 667 });

      // Test navigation in mobile view
      await fundsPage.navigateToFunds();
      expect(await fundsPage.verifyFundsPageLoaded()).to.be.true;

      // Reset viewport
      await driver.manage().window().setRect({ width: 1280, height: 720 });
    });
  });

  describe('Performance and Accessibility', function() {
    it('should verify Arabic page load performance', async function() {
      const credentials = getCredentials('fundManager');
      
      await loginPage.navigateToLogin();
      await loginPage.loginAndWaitForDashboard(credentials.username, credentials.password);

      // Switch to Arabic and measure performance
      const startTime = Date.now();
      await dashboardPage.switchLanguage();
      const loadTime = Date.now() - startTime;

      // Arabic page should load within reasonable time
      expect(loadTime).to.be.lessThan(5000); // 5 seconds

      // Verify performance metrics
      expect(await dashboardPage.verifyDashboardPerformance()).to.be.true;
    });

    it('should verify accessibility features in both languages', async function() {
      // Test Arabic accessibility
      await loginPage.navigateToLogin();
      await loginPage.switchLanguage();
      expect(await loginPage.verifyAccessibility()).to.be.true;

      // Test English accessibility
      await loginPage.switchLanguage();
      expect(await loginPage.verifyAccessibility()).to.be.true;
    });
  });

  describe('Error Message Display and UX', function() {
    it('should verify error message positioning and styling', async function() {
      await loginPage.navigateToLogin();
      
      // Trigger validation error
      await loginPage.clearForm();
      await loginPage.clickLogin();

      // Verify error message appears
      const hasError = await loginPage.verifyLoginError();
      expect(hasError).to.be.true;

      // Check error message styling (basic verification)
      const errorElement = await driver.findElement({ css: '[data-testid="error-message"]' });
      const isDisplayed = await errorElement.isDisplayed();
      expect(isDisplayed).to.be.true;
    });

    it('should verify error message auto-dismiss', async function() {
      await loginPage.navigateToLogin();
      
      // Trigger validation error
      await loginPage.clearForm();
      await loginPage.clickLogin();

      // Verify error message appears
      expect(await loginPage.verifyLoginError()).to.be.true;

      // Fix the error by entering valid data
      await loginPage.enterUsername('<EMAIL>');

      // Wait a moment for auto-dismiss (if implemented)
      await driver.sleep(1000);

      // In real implementation, we would verify the error disappears
      // For now, we'll just verify the form is functional
      const usernameValue = await loginPage.getUsernameValue();
      expect(usernameValue).to.equal('<EMAIL>');
    });
  });
});
