/**
 * WebDriver Manager for Jadwa Fund Management System
 * 
 * This module manages WebDriver instances, handles driver lifecycle,
 * and provides utilities for browser driver management.
 */

import { WebDriver } from 'selenium-webdriver';
import { createWebDriver, cleanupWebDriver, SeleniumConfig, getBrowserConfig } from '../config/selenium.config';

export class DriverManager {
  private static drivers: Map<string, WebDriver> = new Map();
  private static activeDrivers: Set<string> = new Set();

  /**
   * Get or create WebDriver instance for specific browser configuration
   */
  static async getDriver(browserName: string): Promise<WebDriver> {
    if (this.drivers.has(browserName)) {
      const existingDriver = this.drivers.get(browserName)!;
      
      // Check if driver is still active
      try {
        await existingDriver.getTitle();
        return existingDriver;
      } catch {
        // Driver is no longer active, remove it
        this.drivers.delete(browserName);
        this.activeDrivers.delete(browserName);
      }
    }

    // Create new driver
    const config = getBrowserConfig(browserName as any);
    const driver = await createWebDriver(config);
    
    this.drivers.set(browserName, driver);
    this.activeDrivers.add(browserName);
    
    return driver;
  }

  /**
   * Create WebDriver with custom configuration
   */
  static async createCustomDriver(config: SeleniumConfig): Promise<WebDriver> {
    return await createWebDriver(config);
  }

  /**
   * Cleanup specific driver
   */
  static async cleanupDriver(browserName: string): Promise<void> {
    if (this.drivers.has(browserName)) {
      const driver = this.drivers.get(browserName)!;
      await cleanupWebDriver(driver);
      this.drivers.delete(browserName);
      this.activeDrivers.delete(browserName);
    }
  }

  /**
   * Cleanup all drivers
   */
  static async cleanupAllDrivers(): Promise<void> {
    const cleanupPromises = Array.from(this.drivers.keys()).map(browserName =>
      this.cleanupDriver(browserName)
    );
    
    await Promise.all(cleanupPromises);
  }

  /**
   * Get all active driver names
   */
  static getActiveDrivers(): string[] {
    return Array.from(this.activeDrivers);
  }

  /**
   * Check if driver exists and is active
   */
  static async isDriverActive(browserName: string): Promise<boolean> {
    if (!this.drivers.has(browserName)) {
      return false;
    }

    try {
      const driver = this.drivers.get(browserName)!;
      await driver.getTitle();
      return true;
    } catch {
      this.drivers.delete(browserName);
      this.activeDrivers.delete(browserName);
      return false;
    }
  }

  /**
   * Restart driver
   */
  static async restartDriver(browserName: string): Promise<WebDriver> {
    await this.cleanupDriver(browserName);
    return await this.getDriver(browserName);
  }

  /**
   * Get driver count
   */
  static getDriverCount(): number {
    return this.drivers.size;
  }

  /**
   * Force cleanup (for emergency situations)
   */
  static forceCleanup(): void {
    this.drivers.clear();
    this.activeDrivers.clear();
  }
}

/**
 * Driver Pool for managing multiple concurrent drivers
 */
export class DriverPool {
  private pool: Map<string, WebDriver[]> = new Map();
  private maxPoolSize: number;

  constructor(maxPoolSize: number = 5) {
    this.maxPoolSize = maxPoolSize;
  }

  /**
   * Get driver from pool or create new one
   */
  async getDriver(browserName: string): Promise<WebDriver> {
    const browserPool = this.pool.get(browserName) || [];
    
    if (browserPool.length > 0) {
      const driver = browserPool.pop()!;
      
      // Verify driver is still active
      try {
        await driver.getTitle();
        return driver;
      } catch {
        // Driver is dead, create new one
        return await this.createNewDriver(browserName);
      }
    }

    return await this.createNewDriver(browserName);
  }

  /**
   * Return driver to pool
   */
  async returnDriver(browserName: string, driver: WebDriver): Promise<void> {
    const browserPool = this.pool.get(browserName) || [];
    
    if (browserPool.length < this.maxPoolSize) {
      // Reset driver state before returning to pool
      try {
        await driver.manage().deleteAllCookies();
        await driver.executeScript('localStorage.clear(); sessionStorage.clear();');
        browserPool.push(driver);
        this.pool.set(browserName, browserPool);
      } catch {
        // Driver is not usable, cleanup
        await cleanupWebDriver(driver);
      }
    } else {
      // Pool is full, cleanup driver
      await cleanupWebDriver(driver);
    }
  }

  /**
   * Cleanup entire pool
   */
  async cleanup(): Promise<void> {
    const cleanupPromises: Promise<void>[] = [];
    
    for (const [browserName, drivers] of this.pool.entries()) {
      for (const driver of drivers) {
        cleanupPromises.push(cleanupWebDriver(driver));
      }
    }
    
    await Promise.all(cleanupPromises);
    this.pool.clear();
  }

  /**
   * Get pool statistics
   */
  getPoolStats(): Record<string, number> {
    const stats: Record<string, number> = {};
    
    for (const [browserName, drivers] of this.pool.entries()) {
      stats[browserName] = drivers.length;
    }
    
    return stats;
  }

  private async createNewDriver(browserName: string): Promise<WebDriver> {
    const config = getBrowserConfig(browserName as any);
    return await createWebDriver(config);
  }
}

/**
 * Singleton driver pool instance
 */
export const driverPool = new DriverPool();

/**
 * Utility functions for driver management
 */
export class DriverUtils {
  /**
   * Setup driver for test
   */
  static async setupDriverForTest(browserName: string): Promise<WebDriver> {
    const driver = await DriverManager.getDriver(browserName);
    
    // Clear any existing state
    await driver.manage().deleteAllCookies();
    await driver.executeScript('localStorage.clear(); sessionStorage.clear();');
    
    return driver;
  }

  /**
   * Cleanup driver after test
   */
  static async cleanupDriverAfterTest(driver: WebDriver): Promise<void> {
    try {
      // Clear state but keep driver alive for reuse
      await driver.manage().deleteAllCookies();
      await driver.executeScript('localStorage.clear(); sessionStorage.clear();');
    } catch (error) {
      console.warn('Error during driver cleanup:', error);
    }
  }

  /**
   * Take screenshot on failure
   */
  static async takeFailureScreenshot(driver: WebDriver, testName: string): Promise<string | null> {
    try {
      const screenshot = await driver.takeScreenshot();
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `failure-${testName}-${timestamp}.png`;
      
      // Save screenshot (implementation depends on your file system setup)
      // For now, return the base64 data
      return screenshot;
    } catch (error) {
      console.warn('Failed to take screenshot:', error);
      return null;
    }
  }

  /**
   * Get browser information
   */
  static async getBrowserInfo(driver: WebDriver): Promise<any> {
    try {
      const capabilities = await driver.getCapabilities();
      return {
        browserName: capabilities.get('browserName'),
        browserVersion: capabilities.get('browserVersion') || capabilities.get('version'),
        platform: capabilities.get('platformName') || capabilities.get('platform')
      };
    } catch (error) {
      console.warn('Failed to get browser info:', error);
      return null;
    }
  }

  /**
   * Wait for all drivers to be ready
   */
  static async waitForDriversReady(drivers: WebDriver[], timeout: number = 30000): Promise<void> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      const readyChecks = drivers.map(async (driver) => {
        try {
          await driver.getTitle();
          return true;
        } catch {
          return false;
        }
      });
      
      const results = await Promise.all(readyChecks);
      
      if (results.every(ready => ready)) {
        return;
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    throw new Error('Timeout waiting for drivers to be ready');
  }

  /**
   * Parallel driver execution
   */
  static async executeInParallel<T>(
    browserNames: string[],
    operation: (driver: WebDriver, browserName: string) => Promise<T>
  ): Promise<T[]> {
    const drivers = await Promise.all(
      browserNames.map(name => DriverManager.getDriver(name))
    );
    
    try {
      const results = await Promise.all(
        drivers.map((driver, index) => operation(driver, browserNames[index]))
      );
      
      return results;
    } finally {
      // Cleanup drivers after parallel execution
      await Promise.all(
        drivers.map(driver => DriverUtils.cleanupDriverAfterTest(driver))
      );
    }
  }
}

/**
 * Global cleanup handler
 */
process.on('exit', () => {
  DriverManager.forceCleanup();
});

process.on('SIGINT', async () => {
  await DriverManager.cleanupAllDrivers();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await DriverManager.cleanupAllDrivers();
  process.exit(0);
});
