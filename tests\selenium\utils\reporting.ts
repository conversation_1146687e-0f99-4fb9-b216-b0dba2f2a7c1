/**
 * Selenium Test Reporting Utilities
 * 
 * This module provides reporting utilities for Selenium tests,
 * including HTML report generation, JSON output, and integration with existing Playwright reports.
 */

import * as fs from 'fs';
import * as path from 'path';

export interface SeleniumTestResult {
  testName: string;
  browser: string;
  language: 'ar' | 'en';
  status: 'passed' | 'failed' | 'skipped';
  duration: number;
  error?: string;
  screenshot?: string;
  startTime: Date;
  endTime: Date;
}

export interface SeleniumTestSuite {
  suiteName: string;
  tests: SeleniumTestResult[];
  totalTests: number;
  passedTests: number;
  failedTests: number;
  skippedTests: number;
  totalDuration: number;
  startTime: Date;
  endTime: Date;
}

export class SeleniumReporter {
  private results: SeleniumTestResult[] = [];
  private suites: Map<string, SeleniumTestSuite> = new Map();
  private reportDir: string;

  constructor(reportDir: string = 'test-results/selenium') {
    this.reportDir = reportDir;
    this.ensureReportDirectory();
  }

  /**
   * Add test result
   */
  addTestResult(result: SeleniumTestResult): void {
    this.results.push(result);
    
    // Add to suite
    const suiteName = this.extractSuiteName(result.testName);
    if (!this.suites.has(suiteName)) {
      this.suites.set(suiteName, {
        suiteName,
        tests: [],
        totalTests: 0,
        passedTests: 0,
        failedTests: 0,
        skippedTests: 0,
        totalDuration: 0,
        startTime: result.startTime,
        endTime: result.endTime
      });
    }
    
    const suite = this.suites.get(suiteName)!;
    suite.tests.push(result);
    suite.totalTests++;
    suite.totalDuration += result.duration;
    suite.endTime = result.endTime;
    
    switch (result.status) {
      case 'passed':
        suite.passedTests++;
        break;
      case 'failed':
        suite.failedTests++;
        break;
      case 'skipped':
        suite.skippedTests++;
        break;
    }
  }

  /**
   * Generate HTML report
   */
  async generateHtmlReport(): Promise<string> {
    const reportPath = path.join(this.reportDir, 'selenium-test-results.html');
    
    const html = this.generateHtmlContent();
    
    await fs.promises.writeFile(reportPath, html, 'utf8');
    
    console.log(`📊 HTML report generated: ${reportPath}`);
    return reportPath;
  }

  /**
   * Generate JSON report
   */
  async generateJsonReport(): Promise<string> {
    const reportPath = path.join(this.reportDir, 'selenium-test-results.json');
    
    const jsonData = {
      summary: this.generateSummary(),
      suites: Array.from(this.suites.values()),
      results: this.results,
      generatedAt: new Date().toISOString()
    };
    
    await fs.promises.writeFile(reportPath, JSON.stringify(jsonData, null, 2), 'utf8');
    
    console.log(`📊 JSON report generated: ${reportPath}`);
    return reportPath;
  }

  /**
   * Generate comparison report with Playwright results
   */
  async generateComparisonReport(playwrightResultsPath?: string): Promise<string> {
    const reportPath = path.join(this.reportDir, 'framework-comparison.html');
    
    let playwrightData = null;
    if (playwrightResultsPath && fs.existsSync(playwrightResultsPath)) {
      try {
        const playwrightContent = await fs.promises.readFile(playwrightResultsPath, 'utf8');
        playwrightData = JSON.parse(playwrightContent);
      } catch (error) {
        console.warn('Failed to load Playwright results:', error);
      }
    }
    
    const html = this.generateComparisonHtml(playwrightData);
    
    await fs.promises.writeFile(reportPath, html, 'utf8');
    
    console.log(`📊 Comparison report generated: ${reportPath}`);
    return reportPath;
  }

  /**
   * Generate summary statistics
   */
  generateSummary() {
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.status === 'passed').length;
    const failedTests = this.results.filter(r => r.status === 'failed').length;
    const skippedTests = this.results.filter(r => r.status === 'skipped').length;
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);
    
    const browserStats = this.results.reduce((stats, result) => {
      if (!stats[result.browser]) {
        stats[result.browser] = { total: 0, passed: 0, failed: 0, skipped: 0 };
      }
      stats[result.browser].total++;
      stats[result.browser][result.status]++;
      return stats;
    }, {} as Record<string, any>);
    
    const languageStats = this.results.reduce((stats, result) => {
      if (!stats[result.language]) {
        stats[result.language] = { total: 0, passed: 0, failed: 0, skipped: 0 };
      }
      stats[result.language].total++;
      stats[result.language][result.status]++;
      return stats;
    }, {} as Record<string, any>);

    return {
      totalTests,
      passedTests,
      failedTests,
      skippedTests,
      passRate: totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0,
      totalDuration,
      avgDuration: totalTests > 0 ? Math.round(totalDuration / totalTests) : 0,
      browserStats,
      languageStats,
      suiteCount: this.suites.size
    };
  }

  /**
   * Generate HTML content
   */
  private generateHtmlContent(): string {
    const summary = this.generateSummary();
    
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Selenium Test Results - Jadwa Fund Management System</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; color: #333; }
        .summary-card .value { font-size: 2em; font-weight: bold; }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .skipped { color: #ffc107; }
        .suite { margin-bottom: 30px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden; }
        .suite-header { background: #007bff; color: white; padding: 15px; }
        .test-list { padding: 0; margin: 0; list-style: none; }
        .test-item { padding: 15px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center; }
        .test-item:last-child { border-bottom: none; }
        .test-name { font-weight: bold; }
        .test-meta { font-size: 0.9em; color: #666; }
        .status-badge { padding: 4px 8px; border-radius: 4px; color: white; font-size: 0.8em; }
        .status-passed { background: #28a745; }
        .status-failed { background: #dc3545; }
        .status-skipped { background: #ffc107; color: #000; }
        .charts { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 30px; }
        .chart { background: #f8f9fa; padding: 20px; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Selenium Test Results</h1>
            <h2>Jadwa Fund Management System</h2>
            <p>Generated on ${new Date().toLocaleString()}</p>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <h3>Total Tests</h3>
                <div class="value">${summary.totalTests}</div>
            </div>
            <div class="summary-card">
                <h3>Passed</h3>
                <div class="value passed">${summary.passedTests}</div>
            </div>
            <div class="summary-card">
                <h3>Failed</h3>
                <div class="value failed">${summary.failedTests}</div>
            </div>
            <div class="summary-card">
                <h3>Skipped</h3>
                <div class="value skipped">${summary.skippedTests}</div>
            </div>
            <div class="summary-card">
                <h3>Pass Rate</h3>
                <div class="value">${summary.passRate}%</div>
            </div>
            <div class="summary-card">
                <h3>Total Duration</h3>
                <div class="value">${Math.round(summary.totalDuration / 1000)}s</div>
            </div>
        </div>
        
        ${Array.from(this.suites.values()).map(suite => `
            <div class="suite">
                <div class="suite-header">
                    <h3>${suite.suiteName}</h3>
                    <div>
                        ${suite.passedTests} passed, 
                        ${suite.failedTests} failed, 
                        ${suite.skippedTests} skipped
                        (${Math.round(suite.totalDuration / 1000)}s)
                    </div>
                </div>
                <ul class="test-list">
                    ${suite.tests.map(test => `
                        <li class="test-item">
                            <div>
                                <div class="test-name">${test.testName}</div>
                                <div class="test-meta">
                                    ${test.browser} | ${test.language} | ${test.duration}ms
                                </div>
                                ${test.error ? `<div class="test-error" style="color: #dc3545; font-size: 0.9em; margin-top: 5px;">${test.error}</div>` : ''}
                            </div>
                            <span class="status-badge status-${test.status}">${test.status.toUpperCase()}</span>
                        </li>
                    `).join('')}
                </ul>
            </div>
        `).join('')}
        
        <div class="charts">
            <div class="chart">
                <h3>Browser Distribution</h3>
                ${Object.entries(summary.browserStats).map(([browser, stats]) => `
                    <div style="margin-bottom: 10px;">
                        <strong>${browser}:</strong> 
                        ${stats.total} tests 
                        (${stats.passed} passed, ${stats.failed} failed, ${stats.skipped} skipped)
                    </div>
                `).join('')}
            </div>
            <div class="chart">
                <h3>Language Distribution</h3>
                ${Object.entries(summary.languageStats).map(([language, stats]) => `
                    <div style="margin-bottom: 10px;">
                        <strong>${language.toUpperCase()}:</strong> 
                        ${stats.total} tests 
                        (${stats.passed} passed, ${stats.failed} failed, ${stats.skipped} skipped)
                    </div>
                `).join('')}
            </div>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * Generate comparison HTML
   */
  private generateComparisonHtml(playwrightData: any): string {
    const seleniumSummary = this.generateSummary();
    
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Framework Comparison - Playwright vs Selenium</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .comparison { display: grid; grid-template-columns: 1fr 1fr; gap: 30px; }
        .framework { border: 1px solid #ddd; border-radius: 8px; overflow: hidden; }
        .framework-header { padding: 20px; color: white; text-align: center; }
        .playwright { background: #2d5a87; }
        .selenium { background: #43b02a; }
        .framework-content { padding: 20px; }
        .metric { display: flex; justify-content: space-between; margin-bottom: 10px; }
        .metric-label { font-weight: bold; }
        .metric-value { color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center;">Framework Comparison Report</h1>
        <p style="text-align: center;">Playwright vs Selenium - Jadwa Fund Management System</p>
        
        <div class="comparison">
            <div class="framework">
                <div class="framework-header playwright">
                    <h2>Playwright Results</h2>
                </div>
                <div class="framework-content">
                    ${playwrightData ? `
                        <div class="metric">
                            <span class="metric-label">Total Tests:</span>
                            <span class="metric-value">${playwrightData.totalTests || 'N/A'}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Pass Rate:</span>
                            <span class="metric-value">${playwrightData.passRate || 'N/A'}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Total Duration:</span>
                            <span class="metric-value">${playwrightData.totalDuration ? Math.round(playwrightData.totalDuration / 1000) + 's' : 'N/A'}</span>
                        </div>
                    ` : '<p>Playwright results not available</p>'}
                </div>
            </div>
            
            <div class="framework">
                <div class="framework-header selenium">
                    <h2>Selenium Results</h2>
                </div>
                <div class="framework-content">
                    <div class="metric">
                        <span class="metric-label">Total Tests:</span>
                        <span class="metric-value">${seleniumSummary.totalTests}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Pass Rate:</span>
                        <span class="metric-value">${seleniumSummary.passRate}%</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Total Duration:</span>
                        <span class="metric-value">${Math.round(seleniumSummary.totalDuration / 1000)}s</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * Extract suite name from test name
   */
  private extractSuiteName(testName: string): string {
    // Extract suite name from test name (e.g., "Authentication and Role-Based Access Control")
    const match = testName.match(/^([^:]+)/);
    return match ? match[1].trim() : 'Unknown Suite';
  }

  /**
   * Ensure report directory exists
   */
  private ensureReportDirectory(): void {
    if (!fs.existsSync(this.reportDir)) {
      fs.mkdirSync(this.reportDir, { recursive: true });
    }
  }
}

// Export singleton instance
export const seleniumReporter = new SeleniumReporter();
