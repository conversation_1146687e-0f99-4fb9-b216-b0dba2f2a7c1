/**
 * Base Selenium Utilities for Jadwa Fund Management System
 * 
 * This module provides base utilities and helper functions for Selenium WebDriver
 * tests, including element interactions, waits, and common operations.
 */

import { WebDriver, WebElement, By, until, Key } from 'selenium-webdriver';
import { getCurrentEnvironment } from '../../e2e/config/environments';

export class SeleniumBase {
  protected driver: WebDriver;
  protected environment = getCurrentEnvironment();

  constructor(driver: WebDriver) {
    this.driver = driver;
  }

  /**
   * Navigate to a specific URL
   */
  async goto(url: string): Promise<void> {
    const fullUrl = url.startsWith('http') ? url : `${this.environment.baseUrl}${url}`;
    await this.driver.get(fullUrl);
  }

  /**
   * Wait for element to be located and visible
   */
  async waitForElement(locator: By, timeout: number = 10000): Promise<WebElement> {
    await this.driver.wait(until.elementLocated(locator), timeout);
    const element = await this.driver.findElement(locator);
    await this.driver.wait(until.elementIsVisible(element), timeout);
    return element;
  }

  /**
   * Wait for element to be clickable
   */
  async waitForElementClickable(locator: By, timeout: number = 10000): Promise<WebElement> {
    const element = await this.waitForElement(locator, timeout);
    await this.driver.wait(until.elementIsEnabled(element), timeout);
    return element;
  }

  /**
   * Wait for element to be hidden
   */
  async waitForElementToHide(locator: By, timeout: number = 10000): Promise<void> {
    try {
      const element = await this.driver.findElement(locator);
      await this.driver.wait(until.elementIsNotVisible(element), timeout);
    } catch (error) {
      // Element not found, which means it's already hidden
    }
  }

  /**
   * Click element with retry logic
   */
  async clickElement(locator: By, timeout: number = 10000): Promise<void> {
    const element = await this.waitForElementClickable(locator, timeout);
    
    try {
      await element.click();
    } catch (error) {
      // Try JavaScript click if regular click fails
      await this.driver.executeScript('arguments[0].click();', element);
    }
  }

  /**
   * Fill input field
   */
  async fillInput(locator: By, value: string, timeout: number = 10000): Promise<void> {
    const element = await this.waitForElement(locator, timeout);
    await element.clear();
    await element.sendKeys(value);
  }

  /**
   * Select option from dropdown
   */
  async selectOption(locator: By, value: string, timeout: number = 10000): Promise<void> {
    const dropdown = await this.waitForElementClickable(locator, timeout);
    await dropdown.click();
    
    // Wait for options to appear and select by value
    const optionLocator = By.css(`option[value="${value}"]`);
    const option = await this.waitForElementClickable(optionLocator, timeout);
    await option.click();
  }

  /**
   * Get text content of element
   */
  async getElementText(locator: By, timeout: number = 10000): Promise<string> {
    const element = await this.waitForElement(locator, timeout);
    return await element.getText();
  }

  /**
   * Get attribute value of element
   */
  async getElementAttribute(locator: By, attribute: string, timeout: number = 10000): Promise<string> {
    const element = await this.waitForElement(locator, timeout);
    return await element.getAttribute(attribute) || '';
  }

  /**
   * Check if element is visible
   */
  async isElementVisible(locator: By, timeout: number = 5000): Promise<boolean> {
    try {
      await this.waitForElement(locator, timeout);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Check if element is enabled
   */
  async isElementEnabled(locator: By, timeout: number = 5000): Promise<boolean> {
    try {
      const element = await this.waitForElement(locator, timeout);
      return await element.isEnabled();
    } catch {
      return false;
    }
  }

  /**
   * Wait for page to load completely
   */
  async waitForPageLoad(timeout: number = 30000): Promise<void> {
    await this.driver.wait(async () => {
      const readyState = await this.driver.executeScript('return document.readyState');
      return readyState === 'complete';
    }, timeout);
  }

  /**
   * Wait for loading spinner to disappear
   */
  async waitForLoadingToComplete(timeout: number = 30000): Promise<void> {
    const spinnerLocator = By.css('[data-testid="loading-spinner"]');
    
    try {
      // Wait for spinner to appear first (optional)
      await this.waitForElement(spinnerLocator, 2000);
    } catch {
      // Spinner might not appear, continue
    }
    
    // Wait for spinner to disappear
    await this.waitForElementToHide(spinnerLocator, timeout);
  }

  /**
   * Handle alert/confirmation dialogs
   */
  async handleAlert(action: 'accept' | 'dismiss' = 'accept'): Promise<void> {
    try {
      const alert = await this.driver.wait(until.alertIsPresent(), 5000);
      if (action === 'accept') {
        await alert.accept();
      } else {
        await alert.dismiss();
      }
    } catch {
      // No alert present
    }
  }

  /**
   * Upload file
   */
  async uploadFile(locator: By, filePath: string, timeout: number = 10000): Promise<void> {
    const fileInput = await this.waitForElement(locator, timeout);
    await fileInput.sendKeys(filePath);
  }

  /**
   * Take screenshot
   */
  async takeScreenshot(): Promise<string> {
    return await this.driver.takeScreenshot();
  }

  /**
   * Scroll element into view
   */
  async scrollIntoView(locator: By, timeout: number = 10000): Promise<void> {
    const element = await this.waitForElement(locator, timeout);
    await this.driver.executeScript('arguments[0].scrollIntoView(true);', element);
  }

  /**
   * Get current URL
   */
  async getCurrentUrl(): Promise<string> {
    return await this.driver.getCurrentUrl();
  }

  /**
   * Refresh page
   */
  async refresh(): Promise<void> {
    await this.driver.navigate().refresh();
  }

  /**
   * Go back in browser history
   */
  async goBack(): Promise<void> {
    await this.driver.navigate().back();
  }

  /**
   * Switch language
   */
  async switchLanguage(language: 'ar' | 'en'): Promise<void> {
    // Click language switcher
    await this.clickElement(By.css('[data-testid="language-switcher"]'));
    
    // Select language
    await this.clickElement(By.css(`[data-testid="language-${language}"]`));
    
    // Wait for page to reload with new language
    await this.waitForPageLoad();
  }

  /**
   * Verify page title
   */
  async verifyPageTitle(expectedTitle: string): Promise<boolean> {
    const title = await this.driver.getTitle();
    return title.includes(expectedTitle);
  }

  /**
   * Verify URL contains path
   */
  async verifyUrlContains(path: string): Promise<boolean> {
    const currentUrl = await this.getCurrentUrl();
    return currentUrl.includes(path);
  }

  /**
   * Verify element contains text
   */
  async verifyElementText(locator: By, expectedText: string): Promise<boolean> {
    try {
      const actualText = await this.getElementText(locator);
      return actualText.includes(expectedText);
    } catch {
      return false;
    }
  }

  /**
   * Wait for network to be idle (approximate)
   */
  async waitForNetworkIdle(timeout: number = 10000): Promise<void> {
    // Wait for any pending requests to complete
    await this.driver.wait(async () => {
      const activeRequests = await this.driver.executeScript(`
        return window.performance.getEntriesByType('navigation').length > 0 &&
               window.performance.getEntriesByType('navigation')[0].loadEventEnd > 0;
      `);
      return activeRequests;
    }, timeout);
  }

  /**
   * Get page performance metrics
   */
  async getPerformanceMetrics(): Promise<any> {
    return await this.driver.executeScript(`
      const navigation = performance.getEntriesByType('navigation')[0];
      return {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
        firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0
      };
    `);
  }

  /**
   * Execute JavaScript
   */
  async executeScript(script: string, ...args: any[]): Promise<any> {
    return await this.driver.executeScript(script, ...args);
  }

  /**
   * Find element with retry
   */
  async findElementWithRetry(locator: By, maxRetries: number = 3, delay: number = 1000): Promise<WebElement> {
    let lastError: Error;
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await this.waitForElement(locator, 5000);
      } catch (error) {
        lastError = error as Error;
        if (i < maxRetries - 1) {
          await this.driver.sleep(delay);
        }
      }
    }
    
    throw lastError!;
  }

  /**
   * Find multiple elements
   */
  async findElements(locator: By, timeout: number = 10000): Promise<WebElement[]> {
    await this.driver.wait(until.elementsLocated(locator), timeout);
    return await this.driver.findElements(locator);
  }

  /**
   * Wait for element count
   */
  async waitForElementCount(locator: By, expectedCount: number, timeout: number = 10000): Promise<void> {
    await this.driver.wait(async () => {
      const elements = await this.driver.findElements(locator);
      return elements.length === expectedCount;
    }, timeout);
  }

  /**
   * Clear browser storage
   */
  async clearStorage(): Promise<void> {
    await this.driver.executeScript(`
      localStorage.clear();
      sessionStorage.clear();
    `);
  }

  /**
   * Set local storage item
   */
  async setLocalStorageItem(key: string, value: string): Promise<void> {
    await this.driver.executeScript(`localStorage.setItem('${key}', '${value}');`);
  }

  /**
   * Get local storage item
   */
  async getLocalStorageItem(key: string): Promise<string | null> {
    return await this.driver.executeScript(`return localStorage.getItem('${key}');`);
  }

  /**
   * Press keyboard key
   */
  async pressKey(key: string): Promise<void> {
    const body = await this.driver.findElement(By.tagName('body'));
    await body.sendKeys(Key[key as keyof typeof Key] || key);
  }

  /**
   * Hover over element
   */
  async hoverElement(locator: By, timeout: number = 10000): Promise<void> {
    const element = await this.waitForElement(locator, timeout);
    const actions = this.driver.actions();
    await actions.move({ origin: element }).perform();
  }

  /**
   * Double click element
   */
  async doubleClickElement(locator: By, timeout: number = 10000): Promise<void> {
    const element = await this.waitForElementClickable(locator, timeout);
    const actions = this.driver.actions();
    await actions.doubleClick(element).perform();
  }

  /**
   * Right click element
   */
  async rightClickElement(locator: By, timeout: number = 10000): Promise<void> {
    const element = await this.waitForElementClickable(locator, timeout);
    const actions = this.driver.actions();
    await actions.contextClick(element).perform();
  }
}
