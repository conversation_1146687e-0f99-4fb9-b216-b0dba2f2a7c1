/**
 * Test Data Manager for Selenium Tests
 * 
 * This module provides test data management utilities for Selenium tests,
 * reusing the existing Playwright test data fixtures and environment configuration.
 */

import { getCurrentEnvironment, getCredentials } from '../../e2e/config/environments';
import { testData } from '../../e2e/fixtures/test-data';

export class SeleniumTestDataManager {
  private environment = getCurrentEnvironment();

  /**
   * Get test credentials for specific role
   */
  getCredentials(role: 'fundManager' | 'legalCouncil' | 'boardSecretary' | 'boardMember') {
    return getCredentials(role);
  }

  /**
   * Get test fund data
   */
  getTestFund(language: 'ar' | 'en' = 'ar') {
    return testData.funds.find(fund => fund.language === language) || testData.funds[0];
  }

  /**
   * Get test resolution data
   */
  getTestResolution(language: 'ar' | 'en' = 'ar') {
    return testData.resolutions.find(resolution => resolution.language === language) || testData.resolutions[0];
  }

  /**
   * Get test board member data
   */
  getTestBoardMember(language: 'ar' | 'en' = 'ar') {
    return testData.boardMembers.find(member => member.language === language) || testData.boardMembers[0];
  }

  /**
   * Get test strategy data
   */
  getTestStrategy(language: 'ar' | 'en' = 'ar') {
    return testData.strategies.find(strategy => strategy.language === language) || testData.strategies[0];
  }

  /**
   * Get system messages for testing
   */
  getSystemMessages(language: 'ar' | 'en' = 'ar') {
    return testData.systemMessages[language];
  }

  /**
   * Get validation messages
   */
  getValidationMessages(language: 'ar' | 'en' = 'ar') {
    return testData.validationMessages[language];
  }

  /**
   * Get error messages
   */
  getErrorMessages(language: 'ar' | 'en' = 'ar') {
    return testData.errorMessages[language];
  }

  /**
   * Get current environment configuration
   */
  getEnvironment() {
    return this.environment;
  }

  /**
   * Generate unique test data
   */
  generateUniqueTestData(baseData: any, suffix?: string) {
    const timestamp = Date.now();
    const uniqueSuffix = suffix || timestamp.toString();
    
    return {
      ...baseData,
      name: `${baseData.name}_${uniqueSuffix}`,
      code: `${baseData.code}_${uniqueSuffix}`,
      email: baseData.email ? `test_${uniqueSuffix}_${baseData.email}` : undefined,
      timestamp
    };
  }

  /**
   * Create test fund data for Selenium tests
   */
  createTestFundData(language: 'ar' | 'en' = 'ar', unique: boolean = true) {
    const baseFund = this.getTestFund(language);
    
    if (unique) {
      return this.generateUniqueTestData(baseFund);
    }
    
    return baseFund;
  }

  /**
   * Create test resolution data for Selenium tests
   */
  createTestResolutionData(language: 'ar' | 'en' = 'ar', unique: boolean = true) {
    const baseResolution = this.getTestResolution(language);
    
    if (unique) {
      return this.generateUniqueTestData(baseResolution);
    }
    
    return baseResolution;
  }

  /**
   * Create test board member data for Selenium tests
   */
  createTestBoardMemberData(language: 'ar' | 'en' = 'ar', unique: boolean = true) {
    const baseMember = this.getTestBoardMember(language);
    
    if (unique) {
      return this.generateUniqueTestData(baseMember);
    }
    
    return baseMember;
  }

  /**
   * Get test data for specific test scenario
   */
  getTestScenarioData(scenario: string, language: 'ar' | 'en' = 'ar') {
    const scenarioData = {
      'fund-creation': {
        fund: this.createTestFundData(language),
        strategy: this.getTestStrategy(language),
        boardMembers: [
          this.createTestBoardMemberData(language),
          this.createTestBoardMemberData(language)
        ]
      },
      'resolution-lifecycle': {
        resolution: this.createTestResolutionData(language),
        fund: this.getTestFund(language),
        messages: this.getSystemMessages(language)
      },
      'voting-workflow': {
        resolution: this.getTestResolution(language),
        boardMembers: testData.boardMembers.filter(member => member.language === language),
        votingResults: testData.votingResults
      },
      'alternative-workflows': {
        resolutions: testData.resolutions.filter(resolution => resolution.language === language),
        messages: this.getSystemMessages(language),
        alternatives: testData.alternativeWorkflows
      }
    };

    return scenarioData[scenario as keyof typeof scenarioData];
  }

  /**
   * Validate test data structure
   */
  validateTestData(data: any, requiredFields: string[]): boolean {
    for (const field of requiredFields) {
      if (!data.hasOwnProperty(field) || data[field] === null || data[field] === undefined) {
        console.warn(`Missing required field: ${field}`);
        return false;
      }
    }
    return true;
  }

  /**
   * Get mock API responses for testing
   */
  getMockApiResponses() {
    return {
      login: {
        success: {
          token: 'mock.jwt.token',
          user: {
            id: 1,
            name: 'Test User',
            email: '<EMAIL>',
            roles: ['FundManager']
          }
        },
        error: {
          message: 'Invalid credentials',
          code: 'AUTH_FAILED'
        }
      },
      funds: {
        list: testData.funds,
        create: {
          id: 999,
          message: 'Fund created successfully'
        }
      },
      resolutions: {
        list: testData.resolutions,
        create: {
          id: 999,
          code: 'TEST001/2024/001',
          message: 'Resolution created successfully'
        }
      }
    };
  }

  /**
   * Setup test database state (mock)
   */
  async setupTestDatabase(): Promise<void> {
    // In a real implementation, this would:
    // 1. Connect to test database
    // 2. Clear existing test data
    // 3. Seed with fresh test data
    // 4. Return cleanup function
    
    console.log('Setting up test database state...');
    
    // Mock database setup
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  /**
   * Cleanup test database state (mock)
   */
  async cleanupTestDatabase(): Promise<void> {
    // In a real implementation, this would:
    // 1. Remove test data
    // 2. Reset database state
    // 3. Close connections
    
    console.log('Cleaning up test database state...');
    
    // Mock database cleanup
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  /**
   * Get test file paths for upload testing
   */
  getTestFilePaths() {
    return {
      pdf: './tests/fixtures/test-document.pdf',
      image: './tests/fixtures/test-image.jpg',
      excel: './tests/fixtures/test-spreadsheet.xlsx',
      invalidFile: './tests/fixtures/invalid-file.txt'
    };
  }

  /**
   * Generate test performance data
   */
  getPerformanceTestData() {
    return {
      expectedLoadTimes: {
        login: 3000,
        dashboard: 5000,
        fundsList: 4000,
        resolutionsList: 4000
      },
      performanceThresholds: {
        firstContentfulPaint: 2000,
        largestContentfulPaint: 4000,
        cumulativeLayoutShift: 0.1,
        firstInputDelay: 100
      }
    };
  }

  /**
   * Get accessibility test data
   */
  getAccessibilityTestData() {
    return {
      requiredAriaLabels: [
        'username-input',
        'password-input',
        'login-button',
        'navigation-menu',
        'fund-search',
        'resolution-actions'
      ],
      colorContrastRequirements: {
        normalText: 4.5,
        largeText: 3.0,
        uiComponents: 3.0
      },
      keyboardNavigationElements: [
        'login-form',
        'main-navigation',
        'fund-cards',
        'resolution-cards',
        'action-buttons'
      ]
    };
  }

  /**
   * Get cross-browser test configuration
   */
  getCrossBrowserTestConfig() {
    return {
      browsers: [
        { name: 'chrome-ar', language: 'ar', priority: 'high' },
        { name: 'chrome-en', language: 'en', priority: 'high' },
        { name: 'firefox-ar', language: 'ar', priority: 'medium' },
        { name: 'firefox-en', language: 'en', priority: 'medium' },
        { name: 'edge-ar', language: 'ar', priority: 'low' },
        { name: 'edge-en', language: 'en', priority: 'low' }
      ],
      mobileDevices: [
        { name: 'iPhone 12', width: 390, height: 844 },
        { name: 'Samsung Galaxy S21', width: 384, height: 854 },
        { name: 'iPad', width: 768, height: 1024 }
      ]
    };
  }

  /**
   * Get test execution configuration
   */
  getTestExecutionConfig() {
    return {
      timeouts: {
        short: 5000,
        medium: 15000,
        long: 30000,
        veryLong: 60000
      },
      retries: {
        flaky: 2,
        stable: 0
      },
      parallel: {
        maxWorkers: 4,
        browserInstances: 2
      }
    };
  }
}

// Export singleton instance
export const seleniumTestDataManager = new SeleniumTestDataManager();
